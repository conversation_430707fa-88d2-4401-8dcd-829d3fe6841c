#!/usr/bin/env python3
"""
深度分析PDF结构，检查GoodNotes批注的存储方式
"""

import fitz  # PyMuPDF
import os


def analyze_pdf_structure():
    """深度分析PDF结构"""
    
    pdf_path = "goodnotes/GoodNotes/考研材料/26王道数据结构.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"❌ 文件不存在: {pdf_path}")
        return
    
    try:
        doc = fitz.open(pdf_path)
        page_index = 18  # 第19页
        
        if page_index >= len(doc):
            print(f"❌ 第19页不存在")
            doc.close()
            return
        
        page = doc[page_index]
        print(f"🔍 分析第19页的PDF结构...")
        
        # 1. 检查页面对象
        print(f"\n📄 页面信息:")
        print(f"  页面尺寸: {page.rect}")
        print(f"  旋转角度: {page.rotation}")
        print(f"  媒体框: {page.mediabox}")
        print(f"  裁剪框: {page.cropbox}")
        
        # 2. 检查所有批注类型
        print(f"\n📝 批注分析:")
        annotations = list(page.annots())
        print(f"  标准批注数量: {len(annotations)}")
        
        for i, annot in enumerate(annotations):
            print(f"    {i+1}. 类型: {annot.type}")
            print(f"       内容: {annot.content}")
            print(f"       矩形: {annot.rect}")
            print(f"       颜色: {annot.colors}")
            print(f"       边框: {annot.border}")
            print(f"       标志: {annot.flags}")
        
        # 3. 检查页面内容流
        print(f"\n🔧 页面内容流分析:")
        try:
            # 获取页面的原始内容流
            content_stream = page.read_contents()
            print(f"  内容流长度: {len(content_stream)} 字节")
            
            # 查找可能的绘图命令
            content_str = content_stream.decode('latin-1', errors='ignore')
            
            # 查找常见的绘图操作
            drawing_ops = ['m ', 'l ', 'c ', 'v ', 'y ', 'h ', 'S', 'f', 'F', 'B', 'b*', 'f*', 'F*', 'B*']
            found_ops = []
            for op in drawing_ops:
                if op in content_str:
                    count = content_str.count(op)
                    found_ops.append(f"{op}: {count}")
            
            if found_ops:
                print(f"  发现绘图操作: {', '.join(found_ops)}")
            else:
                print(f"  未发现明显的绘图操作")
                
            # 查找颜色设置
            color_ops = ['rg', 'RG', 'g', 'G', 'k', 'K', 'cs', 'CS', 'sc', 'SC', 'scn', 'SCN']
            found_colors = []
            for op in color_ops:
                if f' {op}' in content_str or f'{op} ' in content_str:
                    count = content_str.count(f' {op}') + content_str.count(f'{op} ')
                    found_colors.append(f"{op}: {count}")
            
            if found_colors:
                print(f"  发现颜色操作: {', '.join(found_colors)}")
            
        except Exception as e:
            print(f"  内容流分析失败: {e}")
        
        # 4. 检查页面资源
        print(f"\n📚 页面资源:")
        try:
            # 获取页面字典
            page_dict = page.get_contents()
            print(f"  页面字典长度: {len(page_dict)} 字节")
        except Exception as e:
            print(f"  页面字典获取失败: {e}")
        
        # 5. 检查图像对象
        print(f"\n🖼️  图像对象:")
        image_list = page.get_images()
        print(f"  图像数量: {len(image_list)}")
        
        for i, img in enumerate(image_list):
            print(f"    {i+1}. XRef: {img[0]}, 名称: {img[7]}")
            try:
                # 获取图像信息
                img_doc = doc.extract_image(img[0])
                print(f"       格式: {img_doc['ext']}, 尺寸: {img_doc['width']}x{img_doc['height']}")
            except Exception as e:
                print(f"       图像信息获取失败: {e}")
        
        # 6. 检查表单字段
        print(f"\n📋 表单字段:")
        widgets = page.widgets()
        print(f"  表单字段数量: {len(widgets)}")
        
        for i, widget in enumerate(widgets):
            print(f"    {i+1}. 类型: {widget.field_type}, 名称: {widget.field_name}")
            print(f"       值: {widget.field_value}")
            print(f"       矩形: {widget.rect}")
        
        # 7. 检查链接
        print(f"\n🔗 链接:")
        links = page.get_links()
        print(f"  链接数量: {len(links)}")
        
        for i, link in enumerate(links):
            print(f"    {i+1}. 类型: {link.get('kind', 'unknown')}")
            print(f"       矩形: {link.get('from', 'unknown')}")
            print(f"       目标: {link.get('uri', link.get('page', 'unknown'))}")
        
        # 8. 尝试检查页面的所有对象
        print(f"\n🔍 页面对象扫描:")
        try:
            # 获取页面的xref
            page_xref = page.xref
            print(f"  页面XRef: {page_xref}")
            
            # 尝试获取页面对象的详细信息
            page_obj = doc.xref_get_key(page_xref, "Contents")
            print(f"  Contents对象: {page_obj}")
            
        except Exception as e:
            print(f"  页面对象扫描失败: {e}")
        
        doc.close()
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


def try_alternative_removal_methods():
    """尝试其他批注移除方法"""
    
    pdf_path = "goodnotes/GoodNotes/考研材料/26王道数据结构.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"❌ 文件不存在: {pdf_path}")
        return
    
    try:
        doc = fitz.open(pdf_path)
        page_index = 18  # 第19页
        page = doc[page_index]
        
        print(f"🔧 尝试其他批注移除方法...")
        
        # 方法1: 尝试清理页面内容
        print(f"\n方法1: 清理页面内容流")
        try:
            # 获取原始内容
            original_content = page.read_contents()
            print(f"  原始内容长度: {len(original_content)} 字节")
            
            # 尝试重新构建页面（这可能会移除一些非标准的内容）
            new_page = doc.new_page(width=page.rect.width, height=page.rect.height)
            
            # 复制文本内容
            text_dict = page.get_text("dict")
            for block in text_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            # 重新插入文本
                            point = fitz.Point(span["bbox"][0], span["bbox"][1])
                            new_page.insert_text(point, span["text"], 
                                                fontsize=span["size"],
                                                color=(0, 0, 0))
            
            # 删除原页面并替换
            doc.delete_page(page_index)
            doc.move_page(len(doc)-1, page_index)
            
            print(f"  ✅ 页面重构完成")
            
        except Exception as e:
            print(f"  ❌ 页面重构失败: {e}")
        
        # 方法2: 尝试移除所有绘图操作
        print(f"\n方法2: 移除绘图操作")
        try:
            page = doc[page_index]  # 重新获取页面
            
            # 获取页面内容并尝试过滤绘图命令
            content = page.read_contents()
            content_str = content.decode('latin-1', errors='ignore')
            
            # 移除可能的手写笔迹相关命令
            # 这些是PDF中常见的绘图命令
            drawing_commands = [
                r'\d+\.?\d*\s+\d+\.?\d*\s+m\s*',  # moveto
                r'\d+\.?\d*\s+\d+\.?\d*\s+l\s*',  # lineto
                r'S\s*',  # stroke
                r's\s*',  # close and stroke
                r'f\s*',  # fill
                r'B\s*',  # fill and stroke
            ]
            
            import re
            modified_content = content_str
            for cmd_pattern in drawing_commands:
                modified_content = re.sub(cmd_pattern, '', modified_content)
            
            if len(modified_content) != len(content_str):
                print(f"  移除了 {len(content_str) - len(modified_content)} 个字符的绘图命令")
                
                # 尝试应用修改后的内容
                # 注意：这种方法可能不安全，仅用于实验
                try:
                    page.clean_contents()
                    print(f"  ✅ 页面内容已清理")
                except Exception as e:
                    print(f"  ⚠️  页面清理警告: {e}")
            else:
                print(f"  未发现可移除的绘图命令")
                
        except Exception as e:
            print(f"  ❌ 绘图操作移除失败: {e}")
        
        # 保存修改后的PDF
        output_path = "26王道数据结构_page19_alternative_cleaned.pdf"
        doc.save(output_path)
        print(f"\n💾 替代方法处理后的PDF已保存为: {output_path}")
        
        # 生成新的页面图片
        page = doc[page_index]
        mat = fitz.Matrix(2.0, 2.0)
        pix = page.get_pixmap(matrix=mat)
        pix.save("page19_alternative_cleaned.png")
        print(f"📸 替代方法处理后的页面图片已保存为: page19_alternative_cleaned.png")
        
        doc.close()
        
    except Exception as e:
        print(f"❌ 替代方法处理失败: {str(e)}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🔍 PDF结构深度分析工具")
    print("📖 目标: 分析GoodNotes批注的存储方式")
    print("=" * 50)
    
    print("\n1️⃣  分析PDF结构")
    analyze_pdf_structure()
    
    print("\n" + "=" * 50)
    print("2️⃣  尝试其他移除方法")
    try_alternative_removal_methods()
    
    print("\n🎉 分析完成!")


if __name__ == "__main__":
    main()
