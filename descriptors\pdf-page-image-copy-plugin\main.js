/*
 * Windows-only Obsidian plugin:
 * - Captures the most visible PDF page to PNG into %USERPROFILE%\AppData\Local\Temp\pdf-page-image-copy
 * - Batches multiple captures within 3s
 * - If you stay on the same page within the 3s window and trigger again, it auto-captures the NEXT page (anchor+1, +2, ...).
 * - After 3s idle, runs a PowerShell script (-File) to Set-Clipboard -Path <all files>
 */

const { Plugin, Notice } = require('obsidian');
const fs = require('fs');
const path = require('path');
const os = require('os');
const { spawn } = require('child_process');

module.exports = class PdfPageImageCopyPlugin extends Plugin {
  onload() {
    this.pendingFilePaths = [];
    this.copyTimeoutId = null;

    // 连续复制相关内存状态
    this.batchAnchorPageNumber = null;   // 本批次锚点页
    this.lastCapturedPageNumber = null;  // 本批次最后一次已复制的页

    // 固定临时目录
    this.tmpDir = path.join(os.tmpdir(), 'pdf-page-image-copy');
    try { fs.mkdirSync(this.tmpDir, { recursive: true }); } catch (_) {}

    this.addCommand({
      id: 'copy-current-pdf-page-image',
      name: '复制当前 PDF 可视页（3 秒内合并；同页连续触发自动复制下一页）',
      callback: async () => { await this.copyCurrentPdfPage(); },
    });
  }

  async copyCurrentPdfPage() {
    if (process.platform !== 'win32') {
      new Notice('此插件仅支持 Windows（需要 PowerShell）');
      return;
    }

    const leaf = this.app.workspace.activeLeaf;
    const view = leaf?.view;
    const file = view?.file;
    if (!file || file.extension.toLowerCase() !== 'pdf') {
      new Notice('请在激活的 PDF 文件中使用此命令');
      return;
    }

    // 先判定“当前最大可视页”的页码，作为当前位置判断依据
    const currentCanvas = this.getCurrentPageCanvas();
    if (!currentCanvas) {
      new Notice('无法定位当前 PDF 页或其画布');
      return;
    }
    const currentPageNumber = this.resolvePageNumberFromCanvas(currentCanvas) ?? 1;

    // 选定本次要复制的目标页码与画布（支持“同页连点 -> 自动下一页”）
    let targetCanvas = null;
    let targetPageNumber = currentPageNumber;

    const inBatch = !!this.copyTimeoutId; // 计时器仍在，说明处于同一批次
    if (!inBatch || this.batchAnchorPageNumber == null || this.lastCapturedPageNumber == null) {
      // 新批次第一张：记录锚点 = 当前页
      this.batchAnchorPageNumber = currentPageNumber;
      this.lastCapturedPageNumber = null; // 还没真正写入
      targetCanvas = currentCanvas;
      targetPageNumber = currentPageNumber;
    } else {
      // 批次中：如果当前页仍是锚点页 -> 自动取“下一页”
      if (currentPageNumber === this.batchAnchorPageNumber) {
        const propose = (this.lastCapturedPageNumber ?? this.batchAnchorPageNumber) + 1;
        const nextCanvas = this.getCanvasForPageNumber(propose);
        if (nextCanvas) {
          targetCanvas = nextCanvas;
          targetPageNumber = propose;
        } else {
          // 找不到下一页的画布（可能尚未渲染），退回到当前页
          targetCanvas = currentCanvas;
          targetPageNumber = currentPageNumber;
        }
      } else {
        // 你已经滚动到别的页：按原逻辑复制“当前最大可视页”，并把锚点更新为新的页
        this.batchAnchorPageNumber = currentPageNumber;
        this.lastCapturedPageNumber = null;
        targetCanvas = currentCanvas;
        targetPageNumber = currentPageNumber;
      }
    }

    try {
      const blob = await new Promise((resolve, reject) => {
        targetCanvas.toBlob((b) => (b ? resolve(b) : reject(new Error('画布转 PNG 失败'))), 'image/png');
      });
      await this.addImageToBatch(blob, file, targetPageNumber);
      this.lastCapturedPageNumber = targetPageNumber; // 更新批次的最后页码
    } catch (e) {
      console.error(e);
      new Notice('截取失败：' + e.message);
    }
  }

  sanitizeFileName(name) {
    return name.replace(/[<>:"/\\|?*]/g, '_');
  }

  async addImageToBatch(blob, file, pageNumber) {
    // 若是新批次（队列为空），先清理临时目录
    if (this.pendingFilePaths.length === 0) {
      try {
        if (fs.existsSync(this.tmpDir)) {
          for (const entry of fs.readdirSync(this.tmpDir)) {
            try { fs.unlinkSync(path.join(this.tmpDir, entry)); } catch (_) {}
          }
        }
      } catch (e) { console.warn('清理临时目录失败：', e); }
    }

    const buffer = Buffer.from(await blob.arrayBuffer());
    const safeBase = this.sanitizeFileName(file.basename);
    const fileName = `${safeBase}_${pageNumber}.png`;
    const filePath = path.join(this.tmpDir, fileName);

    try { fs.writeFileSync(filePath, buffer); }
    catch (e) {
      console.error('写入 PNG 失败：', e);
      new Notice('写入临时图片失败');
      return;
    }

    this.pendingFilePaths.push(filePath);
    new Notice(`已截取第 ${pageNumber} 页；3 秒内继续可合并复制（当前 ${this.pendingFilePaths.length} 张）`);

    if (this.copyTimeoutId) clearTimeout(this.copyTimeoutId);
    this.copyTimeoutId = setTimeout(() => this.finalizeCopy(), 3000);
  }

  finalizeCopy() {
    const filePaths = this.pendingFilePaths.slice();
    this.pendingFilePaths = [];
    this.copyTimeoutId = null;

    // 批次结束，释放连续复制状态
    this.batchAnchorPageNumber = null;
    this.lastCapturedPageNumber = null;

    if (filePaths.length === 0) return;

    // 用临时 ps1 + -File 方式调用，避免引号/编码问题
    const psScript = `
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$p = Join-Path $env:USERPROFILE 'AppData\\Local\\Temp\\pdf-page-image-copy'
if (-Not (Test-Path -LiteralPath $p)) { Write-Output 'NO_PATH'; exit 2 }
$files = Get-ChildItem -LiteralPath $p -File | ForEach-Object { $_.FullName }
if (-Not $files) { Write-Output 'NO_FILES'; exit 3 }
Set-Clipboard -Path $files
Write-Output ("OK=" + ($files.Count))
`.trim();

    const tmpPs1 = path.join(os.tmpdir(), `obs-pdfcopy-${Date.now()}.ps1`);
    try { fs.writeFileSync(tmpPs1, psScript, { encoding: 'utf8' }); }
    catch (e) {
      console.error('写入临时 PS 脚本失败：', e);
      new Notice('创建 PowerShell 脚本失败');
      return;
    }

    const psExe = 'powershell.exe';
    const args = ['-NoProfile', '-ExecutionPolicy', 'Bypass', '-File', tmpPs1];
    const child = spawn(psExe, args, { windowsHide: true });

    let stdout = '', stderr = '';
    child.stdout.on('data', (d) => (stdout += d.toString()));
    child.stderr.on('data', (d) => (stderr += d.toString()));

    child.on('close', (code) => {
      try { fs.unlinkSync(tmpPs1); } catch (_) {}

      const out = (stdout || '').trim();
      if (code !== 0) {
        if (out.includes('NO_PATH')) { new Notice('临时目录不存在（请重试）'); return; }
        if (out.includes('NO_FILES')) { new Notice('没有可复制的文件'); return; }
        new Notice('PowerShell 执行失败：' + (stderr || out || `退出码 ${code}`));
        return;
      }

      const m = out.match(/OK=(\d+)/);
      new Notice(m ? `已将 ${m[1]} 个文件复制到剪贴板，可在资源管理器中粘贴`
                   : '复制完成，但未能解析数量');
    });
  }

  // —— 页面/画布工具 —— //
  resolvePageNumberFromCanvas(canvas) {
    let el = canvas?.parentElement;
    while (el && el !== document.body) {
      if (el.hasAttribute?.('data-page-number')) {
        const n = parseInt(el.getAttribute('data-page-number'), 10);
        if (!Number.isNaN(n)) return n;
        break;
      }
      el = el.parentElement;
    }
    return null;
  }

  getCanvasForPageNumber(n) {
    if (!n || n < 1) return null;
    const pageDiv = document.querySelector(`.page[data-page-number="${n}"]`);
    if (!pageDiv) return null;
    const c = pageDiv.querySelector('canvas');
    return c instanceof HTMLCanvasElement ? c : null;
  }

  getMostVisiblePageElement() {
    const pages = Array.from(document.querySelectorAll('.page[data-page-number]'));
    if (pages.length === 0) return null;
    let maxArea = 0, best = null;
    const vw = window.innerWidth, vh = window.innerHeight;
    for (const page of pages) {
      const r = page.getBoundingClientRect();
      const w = Math.max(0, Math.min(r.right, vw) - Math.max(r.left, 0));
      const h = Math.max(0, Math.min(r.bottom, vh) - Math.max(r.top, 0));
      const area = w * h;
      if (area > maxArea) { maxArea = area; best = page; }
    }
    return best;
  }

  getCurrentPageCanvas() {
    // 先用窗口中心处元素向上找
    const cx = window.innerWidth / 2, cy = window.innerHeight / 2;
    let el = document.elementFromPoint(cx, cy);
    while (el && el !== document.body) {
      if (el instanceof HTMLElement) {
        if (el.hasAttribute?.('data-page-number') || el.classList?.contains('page')) {
          const c = el.querySelector('canvas');
          if (c instanceof HTMLCanvasElement) return c;
        }
      }
      el = el.parentElement;
    }
    // 失败则退回最大可视页
    const pageDiv = this.getMostVisiblePageElement();
    if (!pageDiv) return null;
    const fallback = pageDiv.querySelector('canvas');
    return fallback instanceof HTMLCanvasElement ? fallback : null;
  }
};
