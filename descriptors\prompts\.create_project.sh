#!/bin/bash

ENV_FILE=".project_env"

if [ $# -eq 0 ]; then
    # 删除模式
    source "$ENV_FILE"

    # 先清空提示词
    curl -s "https://aidaxue.link/api/organizations/$LAST_ACTIVE_ORG/projects/$PROJECT_ID" \
      -X PUT \
      -H "content-type: application/json" \
      -b "share-session=$SHARE_SESSION; lastActiveOrg=$LAST_ACTIVE_ORG" \
      -H "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)" \
      --data-raw '{"prompt_template":""}'

    # 删除项目（信息）
    curl -s "https://aidaxue.link/api/organizations/$LAST_ACTIVE_ORG/projects" \
      -H "content-type: application/json" \
      -b "share-session=$SHARE_SESSION; lastActiveOrg=$LAST_ACTIVE_ORG" \
      -H "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)" \
      --data-raw '{"name":"学习","description":""}'

    # 删除（存档？）项目
    curl -s "https://aidaxue.link/api/organizations/$LAST_ACTIVE_ORG/projects/$PROJECT_ID" \
      -X DELETE \
      -H "content-type: application/json" \
      -b "share-session=$SHARE_SESSION; lastActiveOrg=$LAST_ACTIVE_ORG" \
      -H "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
    rm -f "$ENV_FILE"
    echo "Project deleted"
else
    # 创建模式
    SHARE_SESSION=$1
    
    # 获取组织ID
    lastActiveOrg=$(curl -s "https://aidaxue.link/new" \
      -b "share-session=$SHARE_SESSION" \
      -H "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)" | \
      grep -o '\\"uuid\\":\\"[^\\]*\\",\\"name\\"' | head -1 | sed 's/.*:\\"//;s/\\".*//')
    
    # 创建项目
    projectId=$(curl -s "https://aidaxue.link/api/organizations/$lastActiveOrg/projects" \
      -H "content-type: application/json" \
      -b "share-session=$SHARE_SESSION; lastActiveOrg=$lastActiveOrg" \
      -H "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)" \
      --data-raw '{"name":"Anki Helper","description":"Create Anki Flashcards base on my textbook materials.","is_private":true}' | \
      jq -r ".uuid")
    
    # 更新提示词（如果文件存在）
    [ -f "anki_prompt.md" ] && {
        PROMPT_TEMPLATE=$(cat anki_prompt.md | sed '/---/,$d')
        json_data=$(jq -n --arg template "$PROMPT_TEMPLATE" '{prompt_template: $template}')
        curl -s "https://aidaxue.link/api/organizations/$lastActiveOrg/projects/$projectId" \
          -X PUT \
          -H "content-type: application/json" \
          -b "share-session=$SHARE_SESSION; lastActiveOrg=$lastActiveOrg" \
          -H "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)" \
          --data-raw "$json_data"
    }
    
    # 保存环境变量
    cat > "$ENV_FILE" << EOF
PROJECT_ID="$projectId"
LAST_ACTIVE_ORG="$lastActiveOrg"
SHARE_SESSION="$SHARE_SESSION"
EOF
    
    echo "Project created: $projectId"
fi