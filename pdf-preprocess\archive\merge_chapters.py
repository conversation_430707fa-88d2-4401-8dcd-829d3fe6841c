# 该脚本用于合并基于章节的 Markdown 文件。
# 创建此脚本是为了将 "26武忠祥高等数学基础篇_mds" 文件夹中的
# 特定章节（第二、三、四、六、七、九章）的笔记合并为按章节划分的单个文件。
# 脚本会识别属于这些章节的文件，对它们进行排序，
# 然后将它们合并到一个以章节标题命名的新文件中，
# 文件名前缀使用该章节中找到的最小文件序号。

import os
import re

def merge_chapter_files():
    """
    Merges markdown files based on chapter numbers.
    """
    # The directory containing the markdown files
    base_dir = "pdf-preprocess/3.数学/26武忠祥高等数学基础篇_mds"

    # Chapters to merge, mapping chapter number to chapter title
    chapters_to_merge = {
        2: "第二章 导数与微分",
        3: "第三章 微分中值定理及导数应用",
        4: "第四章 不定积分",
        6: "第六章 定积分的应用",
        7: "第七章 微分方程",
        9: "第九章 二重积分"
    }

    # A dictionary to group files by chapter number
    chapter_files = {chap_num: [] for chap_num in chapters_to_merge}

    # Regex to parse filenames like '01_1.1 ... .txt'
    # It captures the file sequence number and chapter number.
    filename_regex = re.compile(r'^(\d+)_(\d+)\..*\.txt$')

    # List files in the directory and group them by chapter
    try:
        files = os.listdir(base_dir)
    except FileNotFoundError:
        print(f"错误：找不到目录 '{base_dir}'")
        return

    for filename in files:
        match = filename_regex.match(filename)
        if match:
            file_seq = int(match.group(1))
            chap_num = int(match.group(2))

            if chap_num in chapters_to_merge:
                chapter_files[chap_num].append((file_seq, filename))

    # Process each chapter to merge files
    for chap_num, files_list in chapter_files.items():
        if not files_list:
            print(f"第 {chap_num} 章没有找到文件。")
            continue

        # Sort files by their sequence number to ensure correct order
        files_list.sort()

        # Determine the new filename using the minimum file sequence number
        min_file_seq = files_list[0][0]
        chapter_title = chapters_to_merge[chap_num]
        
        output_filename = f"{min_file_seq:02d}_{chapter_title}.md"
        output_filepath = os.path.join(base_dir, output_filename)

        print(f"正在合并第 {chap_num} 章的文件到 '{output_filename}'...")

        # Merge the content of the files
        with open(output_filepath, 'w', encoding='utf-8') as outfile:
            for _, filename in files_list:
                filepath = os.path.join(base_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as infile:
                        outfile.write(infile.read())
                        # Add a couple of newlines for separation between file contents
                        outfile.write("\n\n")
                except Exception as e:
                    print(f"读取文件 {filepath} 时出错: {e}")

        print(f"成功创建 '{output_filename}'")

if __name__ == "__main__":
    merge_chapter_files() 