# 8.2 Network/Cookie 存在污染的情况，导致获取到的 share-session 与实际不一致：删除该文件夹重启 Ex
import sys
import os
import re
import requests
import asyncio
import json
import websockets
import aiohttp
from dotenv import load_dotenv, set_key

ENV_FILE = ".project_env"

async def get_cookie(key):
    """通过调试器即时获取 share-session"""
    # 1. 获取调试目标
    targets = requests.get("http://localhost:9229/json/list").json()
    ws_url = targets[0]["webSocketDebuggerUrl"]
    
    # 2. 连接并执行
    async with websockets.connect(ws_url) as ws:
        command = {
            "id": 1,
            "method": "Runtime.evaluate",
            "params": {
                "expression": """
                (async () => {
                    const { session } = process.mainModule.require('electron');
                    const cookies = await session.defaultSession.cookies.get({ name: '""" + key + """' });
                    return cookies[0];
                })()
                """,
                "awaitPromise": True,
                "returnByValue": True
            }
        }
        
        await ws.send(json.dumps(command))
        result = json.loads(await ws.recv())
        
        # 3. 提取cookie值
        cookie = result["result"]["result"]["value"]
        return cookie["value"]

async def delete_all_conversations(org_id, project_id, headers, cookies):
    """异步并发删除所有conversations"""
    response = requests.get(f"https://claudeexperience.com/api/organizations/{org_id}/projects/{project_id}/conversations", headers=headers, cookies=cookies)
    if response.status_code != 200 or not response.json():
        return
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        for conv in response.json():
            print(f"Deleting: {conv['uuid']} - {conv['name']}")
            url = f"https://claudeexperience.com/api/organizations/{org_id}/chat_conversations/{conv['uuid']}"
            data = {"uuid": conv['uuid'], "projectUuid": project_id}
            tasks.append(session.delete(url, headers=headers, cookies=cookies, json=data))
        
        await asyncio.gather(*tasks)

# --- 主逻辑 ---

if os.path.exists(ENV_FILE):
    # --- 删除模式 ---
    load_dotenv(dotenv_path=ENV_FILE)
    PROJECT_ID = os.getenv('PROJECT_ID')
    
    # 实际执行前显示当前状态并确认
    print(f"Environment file found for project {PROJECT_ID}.")
    try:
        confirm = input("Are you sure you want to delete this project? (Y/n): ")
        if confirm.lower() in ['n', 'no']:
            print("Deletion cancelled.")
            sys.exit()
    except KeyboardInterrupt:
        print("\nDeletion cancelled.")
        sys.exit()
        
    LAST_ACTIVE_ORG = os.getenv('LAST_ACTIVE_ORG')
    SHARE_SESSION = os.getenv('SHARE_SESSION')

    cookies = {"share-session": SHARE_SESSION, "lastActiveOrg": LAST_ACTIVE_ORG}
    headers = {
        "content-type": "application/json",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) ClaudeNest/0.10.38 Chrome/136.0.7103.149 Electron/36.4.0 Safari/537.36"
    }
    
    # 异步并发删除项目中的所有conversations
    asyncio.run(delete_all_conversations(LAST_ACTIVE_ORG, PROJECT_ID, headers, cookies))
    
    # 清空项目prompt template
    requests.put(
        f"https://claudeexperience.com/api/organizations/{LAST_ACTIVE_ORG}/projects/{PROJECT_ID}",
        headers=headers,
        cookies=cookies,
        json={"prompt_template": ""}
    )
    
    # 覆盖清空项目details
    requests.put(
        f"https://claudeexperience.com/api/organizations/{LAST_ACTIVE_ORG}/projects/{PROJECT_ID}",
        headers=headers,
        cookies=cookies,
        json={"name": "xuexue", "description": ""}
    )
    
    # 删除项目
    requests.delete(
        f"https://claudeexperience.com/api/organizations/{LAST_ACTIVE_ORG}/projects/{PROJECT_ID}",
        headers=headers,
        cookies=cookies
    )
    
    os.remove(ENV_FILE)
    print("Project deleted")

else:
    # --- 创建模式 ---
    
    print("Environment file not found.")

    # 不再需要手动指定，而是自动获取
    SHARE_SESSION = asyncio.run(get_cookie("share-session"))
    print(f"Successfully fetched share-session: {SHARE_SESSION}")
    
    # 获取组织ID
    cookies = {"share-session": SHARE_SESSION}
    headers = {"user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) ClaudeNest/0.10.38 Chrome/136.0.7103.149 Electron/36.4.0 Safari/537.36"}
    
    """
    response = requests.get("https://claudeexperience.com/new", cookies=cookies, headers=headers)
    match = re.search(r'\\"uuid\\":\\"([^\\]*)\\"[,}]', response.text)
    lastActiveOrg = match.group(1)
    """
    lastActiveOrg = asyncio.run(get_cookie("lastActiveOrg"))
    print(f"Find last active org: {lastActiveOrg}")

    # 执行确认
    try:
        confirm = input("Are you sure you want to create a new project? (Y/n): ")
        if confirm.lower() in ['n', 'no']:
            print("Creation cancelled.")
            sys.exit()
    except KeyboardInterrupt:
        print("\nCreation cancelled.")
        sys.exit()

    # 创建项目，更新项目details
    cookies["lastActiveOrg"] = lastActiveOrg
    headers["content-type"] = "application/json"

    response = requests.post(
        f"https://claudeexperience.com/api/organizations/{lastActiveOrg}/projects",
        headers=headers,
        cookies=cookies,
        json={
            "name": "Anki Helper",
            "description": "Create Anki Flashcards base on my textbook materials.",
            "is_private": True
        }
    )
    projectId = response.json()["uuid"]
    
    # 更新prompt template
    if os.path.exists("anki_prompt.md"):
        with open("anki_prompt.md", 'r', encoding='utf-8') as f:
            content = f.read()
        PROMPT_TEMPLATE = content.split('---')[0].rstrip('\n')
        
        requests.put(
            f"https://claudeexperience.com/api/organizations/{lastActiveOrg}/projects/{projectId}",
            headers=headers,
            cookies=cookies,
            json={"prompt_template": PROMPT_TEMPLATE}
        )
    
    # 保存环境变量
    set_key(ENV_FILE, "PROJECT_ID", projectId)
    set_key(ENV_FILE, "LAST_ACTIVE_ORG", lastActiveOrg)
    set_key(ENV_FILE, "SHARE_SESSION", SHARE_SESSION)
    
    print(f"Project created: {projectId}")
