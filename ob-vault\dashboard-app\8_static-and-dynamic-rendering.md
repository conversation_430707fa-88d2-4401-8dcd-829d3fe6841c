App Router: Static and Dynamic Rendering | Next.js

===============
[Skip to content](https://nextjs.org/learn/dashboard-app/static-and-dynamic-rendering#geist-skip-nav)

[](https://vercel.com/home?utm_source=next-site&utm_medium=banner&utm_campaign=learn_dashboard-app_static-and-dynamic-rendering "Go to Vercel homepage")

[![Image 1: Next.js uwu logo by SAWARATSUKI](https://nextjs.org/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1714730590%2Ffront%2Fnextjs%2Fuwu%2Fnext-uwu-logo.png&w=128&q=75)](https://nextjs.org/?uwu=true "Go to the homepage")

[](https://nextjs.org/ "Go to the homepage")

Search documentation...CtrlK Search...⌘K

[](https://vercel.com/home?utm_source=next-site&utm_medium=banner&utm_campaign=learn_dashboard-app_static-and-dynamic-rendering "Go to Vercel homepage")

[![Image 2: Next.js uwu logo by SAWARATSUKI](https://nextjs.org/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1714730590%2Ffront%2Fnextjs%2Fuwu%2Fnext-uwu-logo.png&w=128&q=75)](https://nextjs.org/?uwu=true "Go to the homepage")

[](https://nextjs.org/ "Go to the homepage")

[Showcase](https://nextjs.org/showcase)[Docs](https://nextjs.org/docs "Documentation")[Blog](https://nextjs.org/blog)[Templates](https://vercel.com/templates/next.js?utm_source=next-site&utm_medium=navbar&utm_campaign=next_site_nav_templates)[Enterprise](https://vercel.com/contact/sales/nextjs?utm_source=next-site&utm_medium=navbar&utm_campaign=next_site_nav_enterprise)

Search documentation...CtrlK Search...⌘K[Deploy](https://vercel.com/new/clone?utm_source=next-site&utm_medium=banner&b=main&s=https%3A%2F%2Fgithub.com%2Fvercel%2Fvercel%2Ftree%2Fmain%2Fexamples%2Fnextjs&showOptionalTeamCreation=false&template=nextjs&teamCreateStatus=hidden&utm_campaign=learn_dashboard-app_static-and-dynamic-rendering)[Learn](https://nextjs.org/learn)

Chapter 8

Static and Dynamic Rendering

[Sign in](https://nextjs.org/api/auth/authorize?slug=dashboard-app/static-and-dynamic-rendering)

[Sign in to save progress](https://nextjs.org/api/auth/authorize?slug=dashboard-app/static-and-dynamic-rendering)

8

Chapter 8

Static and Dynamic Rendering
============================

In the previous chapter, you fetched data for the Dashboard Overview page. However, we briefly discussed two limitations of the current setup:

1.   The data requests are creating an unintentional waterfall.
2.   The dashboard is static, so any data updates will not be reflected on your application.

In this chapter...

Here are the topics we'll cover

![Image 3](https://nextjs.org/_next/static/media/lightning.ab4557f0.svg)

What static rendering is and how it can improve your application's performance.

![Image 4](https://nextjs.org/_next/static/media/lightning.ab4557f0.svg)

What dynamic rendering is and when to use it.

![Image 5](https://nextjs.org/_next/static/media/box.cf41945d.svg)

Different approaches to make your dashboard dynamic.

![Image 6](https://nextjs.org/_next/static/media/clock.773a4385.svg)

Simulate a slow data fetch to see what happens.

### [What is Static Rendering?](https://nextjs.org/learn/dashboard-app/static-and-dynamic-rendering#what-is-static-rendering)

With static rendering, data fetching and rendering happens on the server at build time (when you deploy) or when [revalidating data](https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating#revalidating-data).

Whenever a user visits your application, the cached result is served. There are a couple of benefits of static rendering:

*   **Faster Websites** - Prerendered content can be cached and globally distributed when deployed to platforms like [Vercel](https://vercel.com/). This ensures that users around the world can access your website's content more quickly and reliably.
*   **Reduced Server Load** - Because the content is cached, your server does not have to dynamically generate content for each user request. This can reduce compute costs.
*   **SEO** - Prerendered content is easier for search engine crawlers to index, as the content is already available when the page loads. This can lead to improved search engine rankings.

Static rendering is useful for UI with **no data** or **data that is shared across users**, such as a static blog post or a product page. It might not be a good fit for a dashboard that has personalized data which is regularly updated.

The opposite of static rendering is dynamic rendering.

### It's time to take a quiz!

Test your knowledge and see what you've just learned.

Why might static rendering not be a good fit for a dashboard app?

A

Because it makes the website slower

B

Because the server load will increase

C

Because the application will not reflect the latest data changes

D

Because you need a Content Delivery Network

Check Answer

[What is Dynamic Rendering?](https://nextjs.org/learn/dashboard-app/static-and-dynamic-rendering#what-is-dynamic-rendering)
---------------------------------------------------------------------------------------------------------------------------

With dynamic rendering, content is rendered on the server for each user at **request time** (when the user visits the page). There are a couple of benefits of dynamic rendering:

*   **Real-Time Data** - Dynamic rendering allows your application to display real-time or frequently updated data. This is ideal for applications where data changes often.
*   **User-Specific Content** - It's easier to serve personalized content, such as dashboards or user profiles, and update the data based on user interaction.
*   **Request Time Information** - Dynamic rendering allows you to access information that can only be known at request time, such as cookies or the URL search parameters.

### It's time to take a quiz!

Test your knowledge and see what you've just learned.

What kind of information is typically only known at request time?

A

Database schema

B

URL Path

C

Cookies and URL search params

Check Answer

[Simulating a Slow Data Fetch](https://nextjs.org/learn/dashboard-app/static-and-dynamic-rendering#simulating-a-slow-data-fetch)
--------------------------------------------------------------------------------------------------------------------------------

The dashboard application we're building is dynamic.

However, there is still one problem mentioned in the previous chapter. What happens if one data request is slower than all the others?

Let's simulate a slow data fetch. In `app/lib/data.ts`, uncomment the `console.log` and `setTimeout` inside `fetchRevenue()`:

/app/lib/data.ts

```
export async function fetchRevenue() {
  try {
    // We artificially delay a response for demo purposes.
    // Don't do this in production :)
    console.log('Fetching revenue data...');
    await new Promise((resolve) => setTimeout(resolve, 3000));
 
    const data = await sql<Revenue[]>`SELECT * FROM revenue`;
 
    console.log('Data fetch completed after 3 seconds.');
 
    return data;
  } catch (error) {
    console.error('Database Error:', error);
    throw new Error('Failed to fetch revenue data.');
  }
}
```

Now open [http://localhost:3000/dashboard/](http://localhost:3000/dashboard/) in a new tab and notice how the page takes longer to load. In your terminal, you should also see the following messages:

```
Fetching revenue data...
Data fetch completed after 3 seconds.
```

Here, you've added an artificial 3-second delay to simulate a slow data fetch. The result is that now your whole page is blocked from showing UI to the visitor while the data is being fetched. Which brings us to a common challenge developers have to solve:

With dynamic rendering, **your application is only as fast as your slowest data fetch.**

8

You've Completed Chapter 8
--------------------------

Nice! You've just learned about static and dynamic rendering in Next.js.

Next Up

9: Streaming

Learn how to improve your user's experience by adding streaming.

[Start Chapter 9](https://nextjs.org/learn/dashboard-app/streaming)

Was this helpful?

supported.

Send

[](https://vercel.com/home?utm_source=next-site&utm_medium=footer&utm_campaign=next-website "Go to the Vercel website")

[](https://github.com/vercel/next.js)

* * *

[](https://x.com/nextjs)

* * *

[](https://bsky.app/profile/nextjs.org)

#### Resources

[Docs](https://nextjs.org/docs)[Support Policy](https://nextjs.org/support-policy)[Learn](https://nextjs.org/learn)[Showcase](https://nextjs.org/showcase)[Blog](https://nextjs.org/blog)[Team](https://nextjs.org/team)[Analytics](https://vercel.com/analytics?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_static-and-dynamic-rendering)[Next.js Conf](https://nextjs.org/conf)[Previews](https://vercel.com/products/previews?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_static-and-dynamic-rendering)

#### More

[Next.js Commerce](https://vercel.com/templates/next.js/nextjs-commerce?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_static-and-dynamic-rendering)[Contact Sales](https://vercel.com/contact/sales?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_static-and-dynamic-rendering)[Community](https://community.vercel.com/)[GitHub](https://github.com/vercel/next.js)[Releases](https://github.com/vercel/next.js/releases)[Telemetry](https://nextjs.org/telemetry)[Governance](https://nextjs.org/governance)

#### About Vercel

[Next.js + Vercel](https://vercel.com/solutions/nextjs?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_static-and-dynamic-rendering)[Open Source Software](https://vercel.com/oss?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_static-and-dynamic-rendering)[GitHub](https://github.com/vercel)[Bluesky](https://bsky.app/profile/vercel.com)[X](https://x.com/vercel)

#### Legal

[Privacy Policy](https://vercel.com/legal/privacy-policy)Cookie Preferences

#### Subscribe to our newsletter

Stay updated on new releases and features, guides, and case studies.

Subscribe

© 2025 Vercel, Inc.

[](https://github.com/vercel/next.js)

* * *

[](https://x.com/nextjs)

* * *

[](https://bsky.app/profile/nextjs.org)