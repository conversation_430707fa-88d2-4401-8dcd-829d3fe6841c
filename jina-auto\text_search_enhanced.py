#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的文本搜索模块
支持文本到向量转换和精确的语义搜索
"""

import base64
import httpx
import os
from typing import List, Dict, Any
from qdrant_textbook_manager import TextbookManager

# 配置参数
JINA_API_KEY = os.getenv("JINA_API_KEY", "jina_fb37b8f75d47454bb4eaac0668fd206417QJlBJx1vkZdH54Z3mYK7_mnKEE")
JINA_EMBEDDINGS_V4_URL = "https://api.jina.ai/v1/embeddings"

class EnhancedTextSearch:
    """增强的文本搜索类"""
    
    def __init__(self):
        """初始化搜索器"""
        self.manager = TextbookManager()
    
    def get_text_embedding(self, text: str) -> List[float]:
        """获取文本的嵌入向量"""
        print(f"🔍 正在获取文本嵌入向量: {text[:50]}...")
        
        headers = {
            "Content-Type": "application/json", 
            "Authorization": f"Bearer {JINA_API_KEY}"
        }
        
        # 构建文本嵌入请求
        payload = {
            "model": "jina-embeddings-v4",
            "task": "retrieval.passage",
            "input": [text]
        }
        
        try:
            with httpx.Client() as client:
                response = client.post(
                    JINA_EMBEDDINGS_V4_URL,
                    headers=headers,
                    json=payload,
                    timeout=60.0,
                )
                response.raise_for_status()
                result = response.json()
                
                # 提取嵌入向量
                embedding = result["data"][0]["embeddings"]
                print(f"✅ 成功获取文本嵌入向量，维度: {len(embedding)}")
                
                return embedding
                
        except Exception as e:
            print(f"❌ 获取文本嵌入向量失败: {e}")
            raise
    
    def search_by_text(self, query_text: str, limit: int = 3, 
                      min_score: float = 0.5) -> List[Dict[str, Any]]:
        """基于文本进行语义搜索"""
        print(f"🔍 执行文本搜索: {query_text}")
        
        try:
            # 1. 获取查询文本的嵌入向量
            query_vector = self.get_text_embedding(query_text)
            
            # 2. 执行向量搜索
            search_results = self.manager.client.search(
                collection_name=self.manager.COLLECTION_NAME,
                query_vector=query_vector,
                limit=limit,
                with_payload=True,
                score_threshold=min_score
            )
            
            # 3. 处理搜索结果
            results = []
            for hit in search_results:
                result = {
                    "id": hit.id,
                    "score": hit.score,
                    "textbook_title": hit.payload.get("textbook_title"),
                    "page_number": hit.payload.get("page_number"),
                    "width": hit.payload.get("width"),
                    "height": hit.payload.get("height"),
                    "image_data": hit.payload.get("image_bytes"),
                    "source_type": hit.payload.get("source_type")
                }
                results.append(result)
            
            print(f"✅ 找到 {len(results)} 个相关页面")
            return results
            
        except Exception as e:
            print(f"❌ 文本搜索失败: {e}")
            return []
    
    def search_by_textbook(self, query_text: str, textbook_title: str, 
                          limit: int = 3) -> List[Dict[str, Any]]:
        """在指定教材中搜索"""
        print(f"🔍 在教材 '{textbook_title}' 中搜索: {query_text}")
        
        try:
            # 获取查询向量
            query_vector = self.get_text_embedding(query_text)
            
            # 创建过滤器
            from qdrant_client.models import Filter, FieldCondition, MatchValue
            
            search_filter = Filter(
                must=[
                    FieldCondition(
                        key="textbook_title",
                        match=MatchValue(value=textbook_title)
                    )
                ]
            )
            
            # 执行过滤搜索
            search_results = self.manager.client.search(
                collection_name=self.manager.COLLECTION_NAME,
                query_vector=query_vector,
                query_filter=search_filter,
                limit=limit,
                with_payload=True
            )
            
            results = []
            for hit in search_results:
                result = {
                    "id": hit.id,
                    "score": hit.score,
                    "textbook_title": hit.payload.get("textbook_title"),
                    "page_number": hit.payload.get("page_number"),
                    "width": hit.payload.get("width"),
                    "height": hit.payload.get("height"),
                    "image_data": hit.payload.get("image_bytes")
                }
                results.append(result)
            
            print(f"✅ 在教材 '{textbook_title}' 中找到 {len(results)} 个相关页面")
            return results
            
        except Exception as e:
            print(f"❌ 教材内搜索失败: {e}")
            return []
    
    def get_textbook_list(self) -> List[str]:
        """获取所有教材列表"""
        try:
            # 获取集合中的所有点
            scroll_results = self.manager.client.scroll(
                collection_name=self.manager.COLLECTION_NAME,
                limit=1000,
                with_payload=True
            )
            
            textbooks = set()
            for point in scroll_results[0]:
                if point.payload and "textbook_title" in point.payload:
                    textbooks.add(point.payload["textbook_title"])
            
            return sorted(list(textbooks))
            
        except Exception as e:
            print(f"❌ 获取教材列表失败: {e}")
            return []
    
    def get_textbook_stats(self) -> Dict[str, Any]:
        """获取教材统计信息"""
        try:
            textbooks = self.get_textbook_list()
            stats = {}
            
            for textbook in textbooks:
                # 统计每个教材的页面数
                from qdrant_client.models import Filter, FieldCondition, MatchValue
                
                filter_condition = Filter(
                    must=[
                        FieldCondition(
                            key="textbook_title",
                            match=MatchValue(value=textbook)
                        )
                    ]
                )
                
                count_result = self.manager.client.count(
                    collection_name=self.manager.COLLECTION_NAME,
                    count_filter=filter_condition
                )
                
                stats[textbook] = {
                    "page_count": count_result.count,
                    "pages": []
                }
                
                # 获取页面列表
                search_results = self.manager.client.search(
                    collection_name=self.manager.COLLECTION_NAME,
                    query_vector=[0] * self.manager.VECTOR_SIZE,  # 虚拟向量
                    query_filter=filter_condition,
                    limit=100,
                    with_payload=True
                )
                
                for hit in search_results:
                    stats[textbook]["pages"].append({
                        "page_number": hit.payload.get("page_number"),
                        "width": hit.payload.get("width"),
                        "height": hit.payload.get("height")
                    })
            
            return stats
            
        except Exception as e:
            print(f"❌ 获取教材统计信息失败: {e}")
            return {}


def test_enhanced_search():
    """测试增强搜索功能"""
    print("🧪 测试增强搜索功能...")
    
    searcher = EnhancedTextSearch()
    
    # 测试基本文本搜索
    test_queries = [
        "数据结构基础概念",
        "线性表的实现",
        "栈和队列",
        "二叉树遍历",
        "排序算法",
        "操作系统进程管理",
        "内存管理",
        "文件系统",
        "高等数学微积分",
        "导数与微分"
    ]
    
    print("\n📊 教材统计信息:")
    stats = searcher.get_textbook_stats()
    for textbook, info in stats.items():
        print(f"  📚 {textbook}: {info['page_count']} 页")
    
    print("\n🔍 测试文本搜索:")
    for query in test_queries[:5]:  # 只测试前5个查询
        print(f"\n查询: '{query}'")
        results = searcher.search_by_text(query, limit=3)
        
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result['textbook_title']} - 第{result['page_number']}页 (相似度: {result['score']:.4f})")
    
    # 测试教材内搜索
    textbooks = searcher.get_textbook_list()
    if textbooks:
        print(f"\n🔍 测试教材内搜索 (在 '{textbooks[0]}' 中搜索):")
        results = searcher.search_by_textbook("数据结构", textbooks[0], limit=3)
        
        for i, result in enumerate(results, 1):
            print(f"  {i}. 第{result['page_number']}页 (相似度: {result['score']:.4f})")
    
    print("\n✅ 增强搜索功能测试完成")


if __name__ == "__main__":
    test_enhanced_search()
