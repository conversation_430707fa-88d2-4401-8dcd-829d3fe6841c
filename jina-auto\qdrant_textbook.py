#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qdrant教材管理系统 - 简洁版本
"""

import base64
import httpx
import fitz
import os
import uuid
import hashlib
import json
import asyncio
import time
from typing import List, Dict, Any, Optional
from qdrant_client import QdrantClient, models

# 配置
JINA_API_KEY = os.getenv("JINA_API_KEY", "jina_fb37b8f75d47454bb4eaac0668fd206417QJlBJx1vkZdH54Z3mYK7_mnKEE")
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333
COLLECTION_NAME = "textbooks"
VECTOR_SIZE = 128  # 多向量：每个token 128维（Jina v4）

class TextbookManager:
    def __init__(self):
        self.client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
        self._create_collection()
    
    def _create_collection(self):
        """创建集合"""
        if not self.client.collection_exists(COLLECTION_NAME):
            self.client.create_collection(
                collection_name=COLLECTION_NAME,
                vectors_config=models.VectorParams(
                    size=VECTOR_SIZE,
                    distance=models.Distance.COSINE,
                    multivector_config=models.MultiVectorConfig(
                        comparator=models.MultiVectorComparator.MAX_SIM
                    ),
                ),
            )
            print(f"✅ 创建集合: {COLLECTION_NAME}")
        # 为页码与标题创建payload索引，便于过滤
        try:
            self.client.create_payload_index(
                collection_name=COLLECTION_NAME,
                field_name="page_number",
                field_schema=models.PayloadSchemaType.INTEGER,
            )
        except Exception:
            pass
        try:
            self.client.create_payload_index(
                collection_name=COLLECTION_NAME,
                field_name="textbook_title",
                field_schema=models.PayloadSchemaType.KEYWORD,
            )
        except Exception:
            pass
    
    async def process_pdf(self, pdf_path: str, title: str):
        """处理PDF文件"""
        if not os.path.exists(pdf_path):
            print(f"❌ PDF不存在: {pdf_path}")
            return
        
        print(f"📖 处理: {title}")
        
        # 打开PDF并获取总页数
        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        print(f"📚 PDF总页数: {total_pages}")
        
        # 询问用户要处理多少页
        while True:
            try:
                user_input = input(f"🤔 请输入要处理的页数 (1-{total_pages}，直接回车处理所有页面): ").strip()
                if user_input == "":
                    max_pages = total_pages
                    print(f"✅ 将处理全部 {total_pages} 页")
                    break
                else:
                    max_pages = int(user_input)
                    if 1 <= max_pages <= total_pages:
                        print(f"✅ 将处理前 {max_pages} 页")
                        break
                    else:
                        print(f"❌ 请输入1到{total_pages}之间的数字")
            except ValueError:
                print("❌ 请输入有效数字")
        
        # 转换PDF为图像
        pages_data = []
        
        for page_num in range(min(len(doc), max_pages)):
            page = doc[page_num]
            pix = page.get_pixmap(annots=False)
            img_bytes = pix.tobytes()
            
            page_uuid = str(uuid.uuid5(uuid.NAMESPACE_OID, hashlib.sha256(pix.samples).hexdigest()))
            
            pages_data.append({
                "page_number": page_num + 1,
                "image_bytes": img_bytes,
                "page_uuid": page_uuid,
                "width": pix.width,
                "height": pix.height
            })
        
        doc.close()
        
        # 检查去重：查看哪些页面已经存在于Qdrant中
        all_uuids = [p["page_uuid"] for p in pages_data]
        existing_uuids = self._check_existing_points(all_uuids)
        existing_set = set(existing_uuids)
        
        # 筛选出需要处理的新页面
        new_pages_data = [p for p in pages_data if p["page_uuid"] not in existing_set]
        
        print(f"🔍 去重检查: 总共{len(pages_data)}页，已存在{len(existing_uuids)}页，需要处理{len(new_pages_data)}页")
        
        if new_pages_data:
            # 流式处理：每获得一个嵌入向量就立即存储
            await self._process_and_store_images(title, new_pages_data)
        else:
            print("🎯 所有页面都已存在，跳过API调用")
        
        print(f"✅ 完成: {title} ({len(pages_data)}页，新增{len(new_pages_data)}页)")
    
    async def _process_and_store_images(self, title: str, pages_data: List[Dict]):
        """异步处理图片并流式存储到Qdrant"""
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {JINA_API_KEY}"}
        total_images = len(pages_data)
        completed_count = 0
        
        print(f"📊 开始流式处理 {total_images} 张图片，每张完成后立即存储")
        
        async def process_and_store_single_image(img_idx: int, page_data: Dict):
            """处理单张图片的API请求并存储"""
            nonlocal completed_count
            
            base64_image = base64.b64encode(page_data["image_bytes"]).decode("utf-8")
            
            payload = {
                "model": "jina-embeddings-v4",
                "task": "retrieval.passage",
                "return_multivector": True,
                "input": [{"image": base64_image}]  # 只包含一张图片
            }
            
            async with httpx.AsyncClient(timeout=300) as client:
                try:
                    # 1. 获取嵌入向量
                    response = await client.post("https://api.jina.ai/v1/embeddings", headers=headers, json=payload)
                    response.raise_for_status()
                    result = response.json()
                    
                    embedding = result.get("data", [{}])[0].get("embeddings")
                    
                    # 2. 立即存储到Qdrant
                    self._store_single_point_to_qdrant(title, page_data, embedding)
                    
                    # 3. 更新进度
                    completed_count += 1
                    progress_percent = (completed_count / total_images) * 100
                    progress_bar = "█" * int(progress_percent / 5) + "░" * (20 - int(progress_percent / 5))
                    print(f"\r⏳ 进度: [{progress_bar}] {completed_count}/{total_images} ({progress_percent:.1f}%)", end="", flush=True)
                    
                except httpx.RequestError as e:
                    completed_count += 1
                    print(f"\n❌ 图片 {img_idx + 1} API请求失败: {e}")
                    raise
                except httpx.HTTPStatusError as e:
                    completed_count += 1
                    print(f"\n❌ 图片 {img_idx + 1} HTTP错误 {e.response.status_code}: {e.response.text}")
                    raise
                except Exception as e:
                    completed_count += 1
                    print(f"\n❌ 图片 {img_idx + 1} 存储错误: {e}")
                    raise
        
        # 并发处理所有图片
        tasks = [process_and_store_single_image(i, page_data) for i, page_data in enumerate(pages_data)]
        await asyncio.gather(*tasks)
        
        print(f"\n🎉 所有图片处理并存储完成！共处理 {total_images} 张图片")
    
    def _store_single_point_to_qdrant(self, title: str, page_data: Dict, embedding: Any):
        """存储单个点到Qdrant"""
        # 兼容多向量与单向量：确保为多向量格式 List[List[float]]
        vector_value = embedding if (isinstance(embedding, list) and embedding and isinstance(embedding[0], list)) else [embedding]
        
        point = models.PointStruct(
            id=page_data["page_uuid"],
            vector=vector_value,
            payload={
                "textbook_title": title,
                "page_number": page_data["page_number"],
                "width": page_data["width"],
                "height": page_data["height"]
                # 移除了 image_bytes 字段，减少存储大小
            }
        )
        
        self.client.upsert(collection_name=COLLECTION_NAME, points=[point])
    
    def _check_existing_points(self, point_ids: List[str]) -> List[str]:
        """检查哪些点ID已经存在于Qdrant中，返回已存在的ID列表"""
        if not point_ids:
            return []
        
        try:
            # 批量检查点是否存在
            response = self.client.retrieve(
                collection_name=COLLECTION_NAME,
                ids=point_ids,
                with_payload=False,  # 只需要知道是否存在，不需要payload
                with_vectors=False   # 不需要向量数据
            )
            # 返回已存在的点的ID
            existing_ids = [point.id for point in response if point is not None]
            return existing_ids
        except Exception as e:
            print(f"⚠️ 检查现有点时出错: {e}")
            return []  # 出错时假设都不存在，继续处理
    
    def _get_text_query_multivector(self, query_text: str) -> List[List[float]]:
        """使用Jina v4获取文本查询的多向量嵌入 (retrieval.query)"""
        print(f"🔍 开始请求Jina API获取查询向量: '{query_text}'")
        start_time = time.time()
        
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {JINA_API_KEY}"}
        payload = {
            "model": "jina-embeddings-v4",
            "task": "retrieval.query",
            "return_multivector": True,
            "input": [{"text": query_text}],
        }
        with httpx.Client() as client:
            resp = client.post("https://api.jina.ai/v1/embeddings", headers=headers, json=payload, timeout=120)
            resp.raise_for_status()
            data = resp.json()
            mv = data.get("data", [{}])[0].get("embeddings")
            # 确保为 List[List[float]]
            result = mv if (isinstance(mv, list) and mv and isinstance(mv[0], list)) else [mv]
        
        api_time = time.time() - start_time
        print(f"✅ Jina API调用完成，耗时: {api_time:.3f}秒")
        return result

    def search(self, query_text: str, limit: int = 3, *, page_from: Optional[int] = None, page_to: Optional[int] = None, 
               page_ranges: Optional[List[tuple[Optional[int], Optional[int]]]] = None, title: Optional[str] = None) -> List[Dict]:
        """使用多向量查询 (MAX_SIM) 搜索相似页面，可选按页码范围与标题过滤
        
        Args:
            query_text: 查询文本
            limit: 返回结果数量限制
            page_from: 单一范围的起始页码（向后兼容）
            page_to: 单一范围的结束页码（向后兼容）
            page_ranges: 多组页码范围，格式为[(起始页码, 结束页码), ...]，None表示不限制
            title: 指定教材标题过滤
        """
        search_start_time = time.time()
        
        # 1. 获取查询向量（Jina API）
        query_vector = self._get_text_query_multivector(query_text)
        
        # 2. 准备Qdrant查询
        query_filter = None
        must_clauses: list[models.FieldCondition] = []
        
        # 页码过滤逻辑
        page_conditions = []
        # 处理传统的单一范围参数（向后兼容）
        if page_from is not None or page_to is not None:
            rng = models.Range(
                gte=page_from if page_from is not None else None,
                lte=page_to if page_to is not None else None,
            )
            page_conditions.append(models.FieldCondition(key="page_number", range=rng))
        
        # 处理多组页码范围
        if page_ranges:
            for start_page, end_page in page_ranges:
                rng = models.Range(
                    gte=start_page if start_page is not None else None,
                    lte=end_page if end_page is not None else None,
                )
                page_conditions.append(models.FieldCondition(key="page_number", range=rng))
        
        # 如果有多个页码条件，使用OR逻辑
        if page_conditions:
            if len(page_conditions) == 1:
                must_clauses.append(page_conditions[0])
            else:
                must_clauses.append(models.Filter(should=page_conditions))
        
        if title:
            must_clauses.append(models.FieldCondition(key="textbook_title", match=models.MatchValue(value=title)))
        if must_clauses:
            query_filter = models.Filter(must=must_clauses)
        
        # 3. 执行本地Qdrant查询
        print(f"📊 开始Qdrant本地向量检索，limit={limit}")
        qdrant_start_time = time.time()
        
        results = self.client.query_points(
            collection_name=COLLECTION_NAME,
            query=query_vector,
            query_filter=query_filter,
            limit=limit,
            with_payload=True,
        )
        
        qdrant_time = time.time() - qdrant_start_time
        print(f"✅ Qdrant本地查询完成，耗时: {qdrant_time:.3f}秒")
        
        # 按照 Qdrant Python 客户端文档，query_points 返回 QueryResponse，需要通过 .points 获取 ScoredPoint 列表
        hits = getattr(results, "points", results)

        search_results = [
            {
                "id": hit.id,
                "score": hit.score,
                "textbook_title": (hit.payload or {}).get("textbook_title"),
                "page_number": (hit.payload or {}).get("page_number"),
                "width": (hit.payload or {}).get("width"),
                "height": (hit.payload or {}).get("height"),
            }
            for hit in hits
        ]
        
        total_time = time.time() - search_start_time
        print(f"🎯 搜索完成！总耗时: {total_time:.3f}秒，找到 {len(search_results)} 个结果")
        return search_results
    
    def display_search_results(self, query_text: str, limit: int = 3):
        """搜索并展示结果（仅文本信息）"""
        print(f"🔍 搜索: {query_text}")
        results = self.search(query_text, limit)
        
        if not results:
            print("❌ 未找到相关结果")
            return
        
        print(f"\n📋 找到 {len(results)} 个相关结果：")
        print("=" * 60)
        
        # 打印文本信息
        for i, result in enumerate(results, 1):
            print(f"{i}. 📖 {result['textbook_title']}")
            print(f"   📄 第{result['page_number']}页")
            print(f"   📊 相似度: {result['score']:.4f}")
            print(f"   📐 尺寸: {result['width']}x{result['height']}")
            print("-" * 40)
    
    async def process_all_textbooks(self):
        """处理数据结构教材"""
        with open("../pdf-preprocess/textbook-meta.json", "r", encoding="utf-8") as f:
            meta_data = json.load(f)
        
        # 只处理数据结构
        for textbook in meta_data["textbooks"]:
            if "数据结构" in textbook["title"]:
                try:
                    # 构建新的PDF路径：../pdf-preprocess/subdir/title.pdf
                    #### 注意：textbook-meta.json 中的 subdir 等目录路径应当包含末尾的 / ####
                    subdir = textbook["subdir"].lstrip("./")  # 移除开头的 "./"
                    pdf_path = f"../pdf-preprocess/{subdir}{textbook['title']}.pdf"
                    await self.process_pdf(pdf_path, textbook["title"])
                except Exception as e:
                    print(f"❌ 处理失败 {textbook['title']}: {e}")
                break


def test_search():
    """测试搜索功能，包括页码过滤能力"""
    manager = TextbookManager()
    
    print("=" * 80)
    print("🧪 开始搜索功能测试")
    print("=" * 80)
        
    # 测试2：单一页码范围过滤（向后兼容模式）
    print("\n🔍 测试2：单一页码范围过滤（1-50页）")
    results = manager.search("利用栈实现线索树遍历", limit=5, page_from=124, page_to=197)
    print(f"📋 找到 {len(results)} 个结果（页码1-50）：")
    for i, result in enumerate(results, 1):
        print(f"  {i}. 📖 {result['textbook_title']} - 第{result['page_number']}页 (相似度: {result['score']:.4f})")
    
    # 测试3：多组页码范围过滤
    print("\n🔍 测试3：多组页码范围过滤（1-20页 或 100-150页）")
    results = manager.search("利用栈实现线索树遍历", limit=5, page_ranges=[(124, 170), (62, 74)])
    print(f"📋 找到 {len(results)} 个结果（页码1-20或100-150）：")
    for i, result in enumerate(results, 1):
        print(f"  {i}. 📖 {result['textbook_title']} - 第{result['page_number']}页 (相似度: {result['score']:.4f})")
    
    # 测试4：无限制范围
    print("\n🔍 测试4：开放式范围过滤（50页以后）")
    results = manager.search("利用栈实现线索树遍历", limit=3, page_ranges=[(124, None)])
    print(f"📋 找到 {len(results)} 个结果（页码≥50）：")
    for i, result in enumerate(results, 1):
        print(f"  {i}. 📖 {result['textbook_title']} - 第{result['page_number']}页 (相似度: {result['score']:.4f})")
    
    print("\n✅ 搜索测试完成！")


async def main():
    """异步主函数"""
    print("🚀 Qdrant教材管理系统")
    
    manager = TextbookManager()
    
    # 异步处理所有教材（运行时询问页数）
    await manager.process_all_textbooks()
    
    # 测试搜索
    test_search()
    
    print("\n✅ 完成！")


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
