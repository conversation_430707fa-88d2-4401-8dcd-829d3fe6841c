# 已通过Gemini Gems替代
import anthropic
import base64
import httpx
import sys

# Load and encode the PDF
# pdf_url = "https://assets.anthropic.com/m/1cd9d098ac3e6467/original/Claude-3-Model-Card-October-Addendum.pdf"
# pdf_data = base64.standard_b64encode(httpx.get(pdf_url).content).decode("utf-8")

# 从本地加载和编码 PDF
pdf_path = sys.argv[1]
course = sys.argv[2]

with open(pdf_path, "rb") as pdf_file:
    pdf_data = base64.standard_b64encode(pdf_file.read()).decode("utf-8")

prompt = """参考下面的目录内容格式：
```
## 应用多元统计分析

第1章 多元正态分布 1
1.1 多元分布的基本概念 1
1.2 统计距离 5
1.3 多元正态分布 8
1.4 均值向量和协方差阵的估计 13
1.5 常用分布及抽样分布 15

第2章 均值向量和协方差阵的检验 21
2.1 均值向量的检验 21
2.2 协方差阵的检验 27
2.3 有关检验的上机实现 28

……

第9章 定性数据的建模分析 210
9.1 对数线性模型的基本理论和方法 211
9.2 对数线性模型的上机实现 212
9.3 Logistic 回归的基本理论和方法 216
9.4 Logistic 回归的方法及步骤 224
```

请你完整提取出图片中{course}课程目录的文本内容，不要省略或遗漏。"""

client = anthropic.Anthropic(
    api_key="************************************************************************************************************")
message = client.messages.create(
    model="claude-3-5-sonnet-20241022",
    max_tokens=8192,
    messages=[{
        "role": "user",
        "content": [
            {"type": "document", "source": {"type": "base64", "media_type": "application/pdf", "data": pdf_data}},
            {"type": "text", "text": prompt}
        ]
    }],
)

print(message.content[0].text)
