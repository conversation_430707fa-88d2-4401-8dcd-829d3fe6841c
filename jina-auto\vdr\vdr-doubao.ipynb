{"cells": [{"cell_type": "markdown", "id": "6407320a", "metadata": {}, "source": ["7/25 16:08"]}, {"cell_type": "code", "execution_count": 2, "id": "bf1a1f00-eddb-4f3f-8294-d65ededb811a", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: http://mirrors.aliyun.com/pypi/simple\n", "Collecting volcengine-python-sdk[ark]\n", "  Downloading http://mirrors.aliyun.com/pypi/packages/58/c0/9578b9504cd7ae9f8de0a4e5a5e21b8efa1a3873707e9475425c09e7663f/volcengine-python-sdk-4.0.5.tar.gz (6.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.2/6.2 MB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25ldone\n", "\u001b[?25hRequirement already satisfied: certifi>=2017.4.17 in /root/miniconda3/lib/python3.12/site-packages (from volcengine-python-sdk[ark]) (2024.2.2)\n", "Requirement already satisfied: python-dateutil>=2.1 in /root/miniconda3/lib/python3.12/site-packages (from volcengine-python-sdk[ark]) (2.9.0.post0)\n", "Requirement already satisfied: six>=1.10 in /root/miniconda3/lib/python3.12/site-packages (from volcengine-python-sdk[ark]) (1.17.0)\n", "Requirement already satisfied: urllib3>=1.23 in /root/miniconda3/lib/python3.12/site-packages (from volcengine-python-sdk[ark]) (2.1.0)\n", "Collecting pydantic<3,>=1.9.0 (from volcengine-python-sdk[ark])\n", "  Downloading http://mirrors.aliyun.com/pypi/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl (444 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m444.8/444.8 kB\u001b[0m \u001b[31m2.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hRequirement already satisfied: httpx<1,>=0.23.0 in /root/miniconda3/lib/python3.12/site-packages (from volcengine-python-sdk[ark]) (0.28.1)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/miniconda3/lib/python3.12/site-packages (from volcengine-python-sdk[ark]) (4.9.0)\n", "Requirement already satisfied: cryptography>=42.0.0 in /root/miniconda3/lib/python3.12/site-packages (from volcengine-python-sdk[ark]) (42.0.5)\n", "Requirement already satisfied: idna>=2.8 in /root/miniconda3/lib/python3.12/site-packages (from anyio<5,>=3.5.0->volcengine-python-sdk[ark]) (3.7)\n", "Requirement already satisfied: sniffio>=1.1 in /root/miniconda3/lib/python3.12/site-packages (from anyio<5,>=3.5.0->volcengine-python-sdk[ark]) (1.3.1)\n", "Requirement already satisfied: typing_extensions>=4.5 in /root/miniconda3/lib/python3.12/site-packages (from anyio<5,>=3.5.0->volcengine-python-sdk[ark]) (4.13.2)\n", "Requirement already satisfied: cffi>=1.12 in /root/miniconda3/lib/python3.12/site-packages (from cryptography>=42.0.0->volcengine-python-sdk[ark]) (1.16.0)\n", "Requirement already satisfied: httpcore==1.* in /root/miniconda3/lib/python3.12/site-packages (from httpx<1,>=0.23.0->volcengine-python-sdk[ark]) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/miniconda3/lib/python3.12/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->volcengine-python-sdk[ark]) (0.16.0)\n", "Collecting annotated-types>=0.6.0 (from pydantic<3,>=1.9.0->volcengine-python-sdk[ark])\n", "  Downloading http://mirrors.aliyun.com/pypi/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl (13 kB)\n", "Collecting pydantic-core==2.33.2 (from pydantic<3,>=1.9.0->volcengine-python-sdk[ark])\n", "  Downloading http://mirrors.aliyun.com/pypi/packages/f9/41/4b043778cf9c4285d59742281a769eac371b9e47e35f98ad321349cc5d61/pydantic_core-2.33.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25hCollecting typing-inspection>=0.4.0 (from pydantic<3,>=1.9.0->volcengine-python-sdk[ark])\n", "  Downloading http://mirrors.aliyun.com/pypi/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl (14 kB)\n", "Requirement already satisfied: pycparser in /root/miniconda3/lib/python3.12/site-packages (from cffi>=1.12->cryptography>=42.0.0->volcengine-python-sdk[ark]) (2.21)\n", "Building wheels for collected packages: volcengine-python-sdk\n", "  Building wheel for volcengine-python-sdk (setup.py) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for volcengine-python-sdk: filename=volcengine_python_sdk-4.0.5-py3-none-any.whl size=20380632 sha256=2ba0bbea0e88e259a724893b01a712880790647bcfb9837b43e109d34be4bc0a\n", "  Stored in directory: /root/.cache/pip/wheels/10/ec/f3/5b142ec9cc4d94184846ce5b8684751255958f9d09df94ad10\n", "Successfully built volcengine-python-sdk\n", "Installing collected packages: typing-inspection, pydantic-core, annotated-types, volcengine-python-sdk, pydantic\n", "Successfully installed annotated-types-0.7.0 pydantic-2.11.7 pydantic-core-2.33.2 typing-inspection-0.4.1 volcengine-python-sdk-4.0.5\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install --upgrade \"volcengine-python-sdk[ark]\""]}, {"cell_type": "code", "execution_count": 1, "id": "7a987aa4-8c66-413c-b233-46fff2f59311", "metadata": {}, "outputs": [], "source": ["# Encode query\n", "queries =[\n", "    'How much percentage of Germanys population died in the 2nd World War?',\n", "    'How many million tons CO2 were captured from Gas processing in 2018?',\n", "    'What is the average CO2 emission of someone in Japan?'\n", "]\n", "\n", "# Encode image/document\n", "images=[\n", "    'https://wiki-upload.yayeah.xyz/wikipedia/commons/3/35/Human_losses_of_world_war_two_by_country.png',\n", "    'https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/7/76/20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg/2560px-20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg.png',\n", "    'https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/f/f3/20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg/2880px-20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg.png'\n", "]"]}, {"cell_type": "code", "execution_count": 2, "id": "50335417-41b6-4cf6-bfac-538f64773886", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/lib/python3.12/site-packages/pydantic/main.py:463: UserWarning: Pydantic serializer warnings:\n", "  PydanticSerializationUnexpectedValue(Expected `list[MultimodalEmbedding]` - serialized value may not be as expected [input_value={'embedding': [0.03369140..., 'object': 'embedding'}, input_type=dict])\n", "  PydanticSerializationUnexpectedValue(Expected `MultimodalEmbeddingUsage` - serialized value may not be as expected [input_value={'prompt_tokens': 30, 'pr...30}, 'total_tokens': 30}, input_type=dict])\n", "  return self.__pydantic_serializer__.to_python(\n", "/root/miniconda3/lib/python3.12/site-packages/pydantic/main.py:463: UserWarning: Pydantic serializer warnings:\n", "  PydanticSerializationUnexpectedValue(Expected `list[MultimodalEmbedding]` - serialized value may not be as expected [input_value={'embedding': [-0.0306396..., 'object': 'embedding'}, input_type=dict])\n", "  PydanticSerializationUnexpectedValue(Expected `MultimodalEmbeddingUsage` - serialized value may not be as expected [input_value={'prompt_tokens': 32, 'pr...32}, 'total_tokens': 32}, input_type=dict])\n", "  return self.__pydantic_serializer__.to_python(\n", "/root/miniconda3/lib/python3.12/site-packages/pydantic/main.py:463: UserWarning: Pydantic serializer warnings:\n", "  PydanticSerializationUnexpectedValue(Expected `list[MultimodalEmbedding]` - serialized value may not be as expected [input_value={'embedding': [-0.0375976..., 'object': 'embedding'}, input_type=dict])\n", "  PydanticSerializationUnexpectedValue(Expected `MultimodalEmbeddingUsage` - serialized value may not be as expected [input_value={'prompt_tokens': 26, 'pr...26}, 'total_tokens': 26}, input_type=dict])\n", "  return self.__pydantic_serializer__.to_python(\n"]}], "source": ["from volcenginesdkarkruntime import Ark\n", "\n", "client = Ark(api_key=\"5c79d0d8-5035-4c73-92e2-a7f9ce2521e5\")\n", "query_embeddings = list()\n", "\n", "for text in queries:\n", "    query_resp = client.multimodal_embeddings.create(\n", "        model=\"doubao-embedding-vision-250615\",\n", "        input=[\n", "            {\"type\": \"text\", \"text\": text}\n", "        ]\n", "    )\n", "    \n", "    query_embedding = query_resp.to_dict()['data']['embedding']\n", "    query_embeddings.append(query_embedding)"]}, {"cell_type": "code", "execution_count": 3, "id": "20d7fbdd-8877-4e48-9185-ec800fce66c8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/lib/python3.12/site-packages/pydantic/main.py:463: UserWarning: Pydantic serializer warnings:\n", "  PydanticSerializationUnexpectedValue(Expected `list[MultimodalEmbedding]` - serialized value may not be as expected [input_value={'embedding': [0.01031494..., 'object': 'embedding'}, input_type=dict])\n", "  PydanticSerializationUnexpectedValue(Expected `MultimodalEmbeddingUsage` - serialized value may not be as expected [input_value={'prompt_tokens': 359, 'p...3}, 'total_tokens': 359}, input_type=dict])\n", "  return self.__pydantic_serializer__.to_python(\n", "/root/miniconda3/lib/python3.12/site-packages/pydantic/main.py:463: UserWarning: Pydantic serializer warnings:\n", "  PydanticSerializationUnexpectedValue(Expected `list[MultimodalEmbedding]` - serialized value may not be as expected [input_value={'embedding': [0.01263427..., 'object': 'embedding'}, input_type=dict])\n", "  PydanticSerializationUnexpectedValue(Expected `MultimodalEmbeddingUsage` - serialized value may not be as expected [input_value={'prompt_tokens': 1325, '...}, 'total_tokens': 1325}, input_type=dict])\n", "  return self.__pydantic_serializer__.to_python(\n", "/root/miniconda3/lib/python3.12/site-packages/pydantic/main.py:463: UserWarning: Pydantic serializer warnings:\n", "  PydanticSerializationUnexpectedValue(Expected `list[MultimodalEmbedding]` - serialized value may not be as expected [input_value={'embedding': [-0.0277099..., 'object': 'embedding'}, input_type=dict])\n", "  PydanticSerializationUnexpectedValue(Expected `MultimodalEmbeddingUsage` - serialized value may not be as expected [input_value={'prompt_tokens': 1325, '...}, 'total_tokens': 1325}, input_type=dict])\n", "  return self.__pydantic_serializer__.to_python(\n"]}], "source": ["image_embeddings = list()\n", "for url in images:\n", "    image_resp = client.multimodal_embeddings.create(\n", "        model=\"doubao-embedding-vision-250615\",\n", "        input=[\n", "            {\n", "                \"type\": \"image_url\",\n", "                \"image_url\": {\"url\": url}\n", "            }\n", "        ]\n", "    )\n", "    \n", "    image_embedding = image_resp.to_dict()['data']['embedding']\n", "    image_embeddings.append(image_embedding)"]}, {"cell_type": "code", "execution_count": 4, "id": "59886ae4-fd36-44d7-b919-94507e8e5cd6", "metadata": {}, "outputs": [{"data": {"text/plain": ["((3, 2048), (3, 2048))"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "\n", "query_embeddings = np.array(query_embeddings)\n", "image_embeddings = np.array(image_embeddings)\n", "\n", "query_embeddings.shape, image_embeddings.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "51b8fcc8-eaa4-47c3-b937-04b625f4e764", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: http://mirrors.aliyun.com/pypi/simple\n", "Requirement already satisfied: scikit-learn in /root/miniconda3/lib/python3.12/site-packages (1.7.0)\n", "Requirement already satisfied: numpy>=1.22.0 in /root/miniconda3/lib/python3.12/site-packages (from scikit-learn) (2.2.6)\n", "Requirement already satisfied: scipy>=1.8.0 in /root/miniconda3/lib/python3.12/site-packages (from scikit-learn) (1.16.0)\n", "Requirement already satisfied: joblib>=1.2.0 in /root/miniconda3/lib/python3.12/site-packages (from scikit-learn) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /root/miniconda3/lib/python3.12/site-packages (from scikit-learn) (3.6.0)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install scikit-learn"]}, {"cell_type": "code", "execution_count": 6, "id": "114efa54-b418-4919-93fb-0d88a27a3697", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.43332361, 0.08228536, 0.15003124],\n", "       [0.06179931, 0.46729786, 0.15312928],\n", "       [0.2195135 , 0.19962305, 0.42660374]])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "from sklearn.preprocessing import normalize\n", "\n", "def compute_similarity_matrix(query_embeddings, doc_embeddings, do_normalize=True):\n", "    \"\"\"\n", "    计算查询向量与文档向量之间的余弦相似度矩阵\n", "    \n", "    参数:\n", "    query_embeddings (np.ndarray): 查询向量数组 (n_queries, dim)\n", "    doc_embeddings (np.ndarray): 文档向量数组 (n_docs, dim)\n", "    normalize (bool): 是否在计算前对向量进行L2归一化（默认为True）\n", "    \n", "    返回:\n", "    np.ndarray: 相似度矩阵 (n_queries, n_docs)\n", "    \"\"\"\n", "    # 确保输入是二维数组\n", "    if query_embeddings.ndim == 1:\n", "        query_embeddings = query_embeddings[np.newaxis, :]\n", "    if doc_embeddings.ndim == 1:\n", "        doc_embeddings = doc_embeddings[np.newaxis, :]\n", "    \n", "    # 归一化向量（如果需要）\n", "    if do_normalize:\n", "        query_embeddings = normalize(query_embeddings, norm='l2', axis=1)\n", "        doc_embeddings = normalize(doc_embeddings, norm='l2', axis=1)\n", "    \n", "    # 计算余弦相似度矩阵\n", "    return np.dot(query_embeddings, doc_embeddings.T)\n", "\n", "compute_similarity_matrix(query_embeddings, image_embeddings)"]}, {"cell_type": "code", "execution_count": null, "id": "78ac496a-3a54-4646-8da2-c0abf7d267a8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}