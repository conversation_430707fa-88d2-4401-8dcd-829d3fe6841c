## 第5章 Markov链

有一类随机过程，它具有所谓的"无后效性"（Markov性），即，要确定过程将来的状态，知道它此刻的情况就足够了，并不需要对它以往状况的认识，这类过程称为Markov过程。我们将介绍Markov过程中最简单的两种类型：离散时间Markov链（简称马氏链）及连续时间的Markov链。

### 5.1 基本概念

#### 5.1.1 Markov链的定义

**定义5.1**（Markov链）随机过程$\{X_n, n = 0, 1, 2, \cdots\}$称为**Markov链**，若它只取有限或可列个值$E_0, E_1, E_2, \cdots$（我们以$\{0, 1, 2, \cdots\}$来标记$E_0, E_1, \cdots$并称它们是过程的状态）。$\{0, 1, 2, \cdots\}$或者其子集记为$S$，称为过程的状态空间。以后，对$\{X_n, n = 0, 1, 2 \cdots \}$（一般认为它的状态是非负整数）和任意的$n \geq 0$，对任意$i, j, i_0, i_1 \cdots, i_{n-1}$，有

$$P(X_{n+1} = j|X_0 = i_0, X_1 = i_1, X_2 = i_2, X_{n-1} = i_{n-1}, X_n = i)= P(X_{n+1} = j|X_n = i). \tag{1}$$

式(1)刻画了Markov链的特性，故称为**Markov性**。

由定义知

$$P(X_0 = i_0, X_1 = i_1, \cdots, X_n = i_n)= P(X_n = i_n|X_0 = i_0, X_1 = i_1, \cdots, X_{n-1} = i_{n-1}) \times P(X_0 = i_0, X_1 = i_1, \cdots, X_{n-1} = i_{n-1})= P(X_n = i_n|X_{n-1} = i_{n-1}) \times P(X_0 = i_0, X_1 = i_1, \cdots, X_{n-1} = i_{n-1})= \cdots= P(X_n = i_n|X_{n-1} = i_{n-1}) \times P(X_{n-1} = i_{n-1}|X_{n-2} = i_{n-2}) \times \cdots \times P(X_1 = i_1|X_0 = i_0) \times P(X_0 = i_0).$$

可见，一旦Markov链初始分布$P(X_0 = i_0)$确定，其统计特性完全由条件概率

$$P(X_n = i_n|X_{n-1} = i_{n-1})$$

决定。因而(这种条件概率，使Markov链中仅需掌握很少的数据。

#### 5.1.2 转移概率

**定义5.2**（转移概率）称式（1）中的条件概率$P(X_{n+1} = j|X_n = i)$为Markov链$\{X_n, n = 0, 1, 2, \cdots \}$的一步转移概率，简称转移概率。

一般情况下，转移概率与状态$i,j$和时间$n$有关。

**定义5.3**（时齐Markov链）当Markov链的转移概率$P(X_{n+1} = j|X_n = i)$只与状态$i,j$有关，而与$n$无关时，称Markov链为**时齐的**，并记$p_{ij} = P(X_{n+1} = j|X_n = i)(n \geq 0)$；否则，就称之为非时齐的。

在本书中，我们只讨论时齐Markov链，并且简称为Markov链。

当Markov链的状态为有限的，称为有限链，否则称为无限链。不妨假设其为可数的，我们可以用$p_{ij}(i, j \in S)$来表示，记为矩阵

$$P = (p_{ij}) = \begin{pmatrix} p_{00} & p_{01} & p_{02} & p_{03} & \cdots \\ p_{10} & p_{11} & p_{12} & p_{13} & \cdots \\ p_{20} & p_{21} & p_{22} & p_{23} & \cdots \\ p_{30} & p_{31} & p_{32} & p_{33} & \cdots \\ \vdots & \vdots & \vdots & \vdots & \ddots \end{pmatrix}. \tag{2}$$

称$P$为转移概率矩阵，有时简称为转移矩阵。其中每项$p_{ij}(i, j \in S)$有以下特征
(1) $p_{ij} \geq 0, i, j \in S$;
(2) $\sum_{j\in S} p_{ij} = 1, \forall i \in S$. \tag{3}

**定义5.4**（随机矩阵）：若矩阵的元素满足(3)中两个条件，则称该矩阵为随机矩阵。

其中随机矩阵中每一行元素和为1。

#### 5.1.3 一些例子

**例5.1**（一个简单的疾病，死亡模型，Fix-Neyman(1951)）考虑一个包含两个健康状态$S_1$和$S_2$以及两个死亡状态$S_3$和$S_4$（即由不同原因引起的死亡）的模型。若个体病态，则认为它处于状态$S_1$，若患病，则认为它处于$S_2$，个体可以从$S_1,S_2$进入$S_3$和$S_4$,易见这是一个马氏链的模型，转移矩阵为

$$P = \begin{pmatrix} p_{11} & p_{12} & p_{13} & p_{14} \\ p_{21} & p_{22} & p_{23} & p_{24} \\ 0 & 0 & 1 & 0 \\ 0 & 0 & 0 & 1 \end{pmatrix}. \tag{4}$$

**例5.2**（赌徒的破产或标准问收益）系统的状态是$0到n$，表示赌徒手持筹码的数目。假设在各回合赌徒以概率$p$获胜而以概率$q = 1 - p$失败。赌徒每次加注1单位，此赌徒转移概率矩阵为

$$P = \begin{pmatrix} 1 & 0 & 0 & 0 & \cdot & 0 & 0 & 0 \\ q & 0 & p & 0 & \cdot & \cdot & 0 & 0 & 0 \\ 0 & q & 0 & p & \cdot & \cdot & 0 & 0 & 0 \\ \cdot & \cdot & \cdot & \cdot & \cdot & \cdot & \cdot & \cdot \\ 0 & 0 & 0 & 0 & \cdot & \cdot & q & 0 & p \\ 0 & 0 & 0 & 0 & \cdot & \cdot & 0 & 0 & 1 \end{pmatrix}_{(n+1)\times(n+1)}. \tag{5}$$

**例5.3**（带反射壁的随机游动）例5.2中A类人变为等价于限制了破产。即这个过程不能离开0：如"赌徒"在步1走到0，那么在步骤2中仍然停留在状态0（没机会）。此时该过程为带有反射壁的随机游动。过程

$$P = \begin{pmatrix} 0 & 1 & 0 & 0 & \cdot & 0 & 0 & 0 \\ q & 0 & p & 0 & \cdot & \cdot & 0 & 0 & 0 \\ 0 & q & 0 & p & \cdot & \cdot & 0 & 0 & 0 \\ \vdots & \vdots & \vdots & \vdots & \cdots & \vdots & \vdots & \vdots \\ 0 & 0 & 0 & 0 & \cdot & \cdot & q & 0 & p \\ 0 & 0 & 0 & 0 & \cdot & \cdot & 0 & 0 & 1 \end{pmatrix}_{(n+1)\times(n+1)}. \tag{6}$$

这种带反射壁的过程可以用来描述排队，我们可以用此来分析排队现象。

**例5.4**（自由随机游动）设一个球在全直线上做无限制的随机游动，它的状态为$0, \pm1, \pm2, \cdots$。这是一个Markov链，转移矩阵可画作

$$P = \begin{pmatrix} \cdot & \cdot & \cdot & \cdot & \cdot & \cdot & \cdot & \cdot & \cdot \\ \cdot & \cdot & q & 0 & p & 0 & \cdot & \cdot & \cdot & \cdot \\ \cdot & \cdot & 0 & q & 0 & p & \cdot & \cdot & \cdot & \cdot \\ \vdots & \vdots & \vdots & \ddots & \ddots & \ddots & \vdots & \vdots & \vdots \\ \cdot & \cdot & \cdot & \cdot & q & 0 & p & 0 & \cdot \\ \cdot & \cdot & \cdot & \cdot & 0 & q & 0 & p & \cdot \\ \cdot & \cdot & \cdot & \cdot & \cdot & \cdot & \cdot & \cdot & \cdot \end{pmatrix}. \tag{7}$$

虽然，它的状态与我们马氏链的定义中提到的$S$有所区别，但其本质是相同的，仍然是可列多个，对它的讨论是类似的。

**例5.5**（图上的简单随机游动）设有一蚂蚁在图5.1的图上爬行，当两个结点相临时，蚂蚁将向它临近一点，并且爬向任何一个邻居的概率是相同的。

则此Markov链的转移矩阵是

$$P = \begin{pmatrix} 0 & \frac{1}{2} & \frac{1}{2} & 0 & 0 & 0 \\ \frac{1}{2} & 0 & \frac{1}{2} & 0 & 0 & 0 \\ \frac{1}{4} & \frac{1}{4} & 0 & \frac{1}{4} & \frac{1}{4} & 0 \\ 0 & 0 & 1 & 0 & 0 & 0 \\ 0 & 0 & \frac{1}{2} & 0 & 0 & \frac{1}{2} \\ 0 & 0 & 0 & 0 & 1 & 0 \end{pmatrix}. \tag{8}$$

**例5.7**

考虑定货问题.设某商店使用$(s,S)$定货策略，每天早上检查来商品的剩余量，设为$x$，则定购量为

$$
\begin{cases}
0, & 若x \geq s, \\
S - x, & 若x < s.
\end{cases}
\tag{9}
$$

设定货和进货不需时间，每天的需求量$Y_n$独立同分布且$P(Y_n = j) = a_j, j = 0, 1, 2, \cdots$。现在我们考虑上述问题中寻找一个Markov链。

令$X_n$为第$n$天结束时的存货量，则

$$X_{n+1} = 
\begin{cases}
X_n - Y_{n+1}, & 若X_n \geq s, \\
S - Y_{n+1}, & 若X_n < s.
\end{cases}
\tag{10}
$$

因此$\{X_n, n \geq 1\}$是Markov链，请读者写出它的转移概率。

**例5.8**

以$S_n$表示保险公司在时刻$n$的盈余，这里的时间以适当的单位来计算（如天,月等）。初始盈余$S_0 = x_0$虽然为已知，但本来的盈余$S_1, S_2, \cdots$却必须预测为随机变量，增量$S_n - S_{n-1}$解释为$n-1$和$n$之间获得的盈利（可以为负）。假设$X_1, X_2, \cdots$是不包含息的盈利且独立同分布为$F(x)$，则

$$S_n = S_{n-1}(1 + \gamma) + X_n,$$

其中$\gamma$是利率，$\{S_n\}$是一Markov链，转移概率是
$p_{xy} = F(y - (1 + \gamma)x)$。

#### 5.1.4 n步转移概率 C-K方程

**定义5.5**
(n步转移概率) 称条件概率

$$p_{ij}^{(n)} = P(X_{m+n} = j|X_m = i), i, j \in S, m \geq 0, n \geq 1 \tag{11}$$

为Markov链n步转移概率，相应地称$P^{(n)} = (p_{ij}^{(n)})$为n步转移矩阵。

当$n = 1$时, $p_{ij}^{(1)} = p_{ij}, P^{(1)} = P$, 此时定义

$$p_{ij}^{(0)} = 
\begin{cases}
0, & i \neq j, \\
1, & i = j.
\end{cases}
\tag{12}
$$

显然，n步转移概率$p_{ij}^{(n)}$直观地就是在给定初始状态，而且过了$n-1$步后转移到某状态，再过一步转移到状态$j$的概率，这和过了$n$步到达状态$j$的概率是相同的。

**定理 5.1**
(Chapman-Kolmogorov方程，简称C-K方程)
对于任$n, m \geq 0, i, j \in S$有
(1)
$$p_{ij}^{(m+n)} = \sum_{k\in S}p_{ik}^{(m)}p_{kj}^{(n)}; \tag{13}$$

(2)
$$P^{(n)} = P \cdot P^{(n-1)} = P \cdot P \cdot P^{(n-2)} = \cdots = P^n. \tag{14}$$

证明

$$p_{ij}^{(m+n)} = P(X_{m+n} = j|X_0 = i)= \frac{P(X_{m+n} = j, X_0 = i)}{P(X_0 = i)}= \sum_{k\in S}\frac{P(X_{m+n} = j, X_m = k, X_0 = i)}{P(X_0 = i)}(概率公式)= \sum_{k\in S}\frac{P(X_{m+n} = j, X_m = k, X_0 = i)}{P(X_0 = i)}\frac{P(X_m = k, X_0 = i)}{P(X_m = k, X_0 = i)}= \sum_{k\in S}P(X_{m+n} = j|X_m = k, X_0 = i)P(X_m = k|X_0 = i)= \sum_{k\in S}p_{kj}^{(n)} \cdot p_{ik}^{(m)}= \sum_{k\in S}p_{ik}^{(m)}p_{kj}^{(n)}.$$

(2)是(1)的矩阵形式，由矩阵乘法即可。

**例5.11** 设例5.2中, $n = 3, p = q = \frac{1}{2}$。赌徒从2元开始赌博，求计算他经过4轮赌博以后拥有1元的概率。

即计算概率为$p_{20}^{(4)} = P(X_4 = 0|X_0 = 2)$，转移矩阵

$$P = \begin{pmatrix} 1 & 0 & 0 & 0 \\ \frac{1}{2} & 0 & \frac{1}{2} & 0 \\ 0 & \frac{1}{2} & 0 & \frac{1}{2} \\ 0 & 0 & 0 & 1 \end{pmatrix}, \tag{15}$$

利用矩阵乘法

$$P^{(4)} = P^4 = \begin{pmatrix} 1 & 0 & 0 & 0 \\ \frac{5}{8} & \frac{1}{16} & 0 & \frac{5}{16} \\ \frac{5}{16} & 0 & \frac{1}{16} & \frac{5}{8} \\ 0 & 0 & 0 & 1 \end{pmatrix}. \tag{16}$$

$p_{20}^4 = \frac{5}{16}$ ($P^{(4)}$中第3行第1列)。

### 5.3 状态的分类及性质

本节我们首先来讨论一下Markov链各个状态之间的关系，并以这些关系将状态分类，最后来研究它们的性质。

**定义5.7**

称状态$i$可**到达**状态$j$ $(i,j \in S)$, 若存在$n \geq 0$使得$p_{ij}^{(n)} > 0$, 记为$i \to j$。若同时有状态$j \to i$, 则称$i$与$j$**互通**，记为$i \leftrightarrow j$。

**定理5.3**

互通是一种等价关系，即满足：
(1)自反性$i \leftrightarrow i$；
(2)对称性$i \leftrightarrow j$, 则$j \leftrightarrow i$；
(3)传递性$i \leftrightarrow j, j \leftrightarrow k$, 则$i \leftrightarrow k$。

证明

由互通定义条(1)(2)已知，只要证明(3)。假设$i \to j, j \to k$在$m, n$步; $p_{ij}^{(m)} > 0, p_{jk}^{(n)} > 0$。根据C-K方程, $p_{ik}^{(m+n)} = \sum_{l\in S}p_{il}^{(m)}p_{lk}^{(n)} \geq p_{ij}^{(m)}p_{jk}^{(n)} > 0$ 即 $i \to k$。同理可知$k \to i$，于是有$i \leftrightarrow k$。

我们根据等价关系将状态空间分成若干类，于是将整个过程分成了几个"区"，而不同区之间的状态有不同的性质。

**定义5.8**

若Markov链只存在一个类，就称它是**不可约的**。否则称为可约的。

**例5.13** 我们来看例5.1中疾病死亡模型的4个状态这间的关系，为清楚起见，经常以转移图来表示Markov链的状态变化，由转移矩阵可得图5.2.

其中已知$S_1 \to S_1, S_2 \to S_1, S_1 \to S_2, S_2 \to S_2, S_1 \to S_3, S_2 \to S_3, S_1 \to S_4, S_2 \to S_4$。因此我们有$S_1 \leftrightarrow S_2$，$S_3 \not\to S_1, S_4 \not\to S_1, S_3 \not\to S_2, S_4 \not\to S_2$,$S_3 \not\to S_4, S_4 \not\to S_3$。按分类将状态空间分为三个类$\{S_1, S_2\}$，$\{S_3\}$与$\{S_4\}$。

类比可以利用赌徒问题来证明一般情况下赌徒问题如果两个边界状态$i, j, 0 < i < n, 0 < j < n$互通，则状态空间可分为三个类：$\{0\}$，$\{1, 2, \cdots, n - 1\}$,$\{n\}$。

若不是我们还要讨论状态的周期性质，这引出：

**定义5.9**

（周期性）若集合$\{n : n \geq 1, p_{ii}^{(n)} > 0\}$非空，则称它的最大公约数$d = d(i)$为状态$i$的周期。若$d > 1$，称$i$是**周期的**；若$d = 1$，称$i$是**非周期的**。并特别规定上述集合为空集时，称$i$的周期为无穷大。

注 由定义5.9知道，虽然有周期$d$，但并不是对所有的$n, p_{ii}^{(nd)}$都大于0。如果集合 $\{n : n \geq 1, p_{ii}^{(n)} > 0\}$为$\{3, 9, 18, 21, \cdots \}$，那Markov链的周期$d = 3$，即3是该的周期，显然，$n = 6, 12, 15$都不属于此集合，即$p_{ii}^{(6)} = 0, p_{ii}^{(12)} = 0, p_{ii}^{(15)} = 0$。下面可以证明，$n$若能被$d$整除则$p_{ii}^{(dn)} > 0$ (详见[38]）。

**例5.14**

考察如图5.3所示的Markov链。

由图5.4可知,$f_{00}^{(1)} = \frac{1}{2}, f_{00}^{(2)} = \frac{1}{4}, f_{00}^{(3)} = \frac{1}{8}, \cdots ,f_{00}^{(n)} = \frac{1}{2^n}$，故$f_{00} = \sum_{n=1}^{\infty}\frac{1}{2^n} = 1, \mu_0 = \sum_{n=1}^{\infty}n2^{-n} < \infty$。可见状态0是正常返，并且它是非周期的，故0是遍历态。对其他状态$i > 0$，由$i \leftrightarrow 0$，故$i$也是遍历态。

**例5.15** 考虑直线上无限制的随机游动，状态空间为$S = \{0, \pm1, \pm2, \cdots \}$，转移概率为$p_{i,i+1} = 1 - p_{i,i-1} = p, i \in S$。对于状态0，可知$p_{00}^{(2n+1)} = 0, n = 1, 2, \cdots$, 即从0出发奇数次不可能返回到0。而

$$p_{00}^{(2n)} = \begin{pmatrix} 2n \\ n \end{pmatrix} p^n(1 - p)^n = \frac{(2n)!}{n!n!}[p(1 - p)]^n.$$

当转移到极限时，由Stirling 公式知，当$n$充分大时，$n! \sim n^{n+\frac{1}{2}}e^{-n}\sqrt{2\pi}$。于是
$p_{00}^{(2n)} \sim \frac{[4p(1-p)]^n}{\sqrt{n\pi}}$ 而$p(1 - p) \leq \frac{1}{4}$且$p(1 - p) = \frac{1}{4} \Leftrightarrow p = \frac{1}{2}$。

于是$p = \frac{1}{2}$时，$\sum_{n=0}^{\infty}p_{ii}^{(n)} = \infty$, 否则$\sum_{n=0}^{\infty}p_{ii}^{(n)} < \infty$, 即$p = \frac{1}{2}$时状态0是零常返，$p \neq \frac{1}{2}$时是非常返。显然，这是过程的各个状态的常返性。(请读者自己考虑它们的周期是什么？)

考虑一个强化装备的问题，假设每次强化装备有0.5的概率强化成功加一级，0.25的概率等级不变，0.25等级下降一级。等级为1时不会降级（即等级为1时0.5概率升一级，0.5概率等级不变）。问从第2级升到第5级需要的强化次数的期望。

根据题意，状态空间$S = \{1, 2, 3, 4, 5\}$，转移概率矩阵为

$$P = \begin{pmatrix} 0.5 & 0.5 & 0 & 0 & 0 \\ 0.25 & 0.25 & 0.5 & 0 & 0 \\ 0 & 0.25 & 0.25 & 0.5 & 0 \\ 0 & 0 & 0.25 & 0.25 & 0.5 \\ 0 & 0 & 0 & 0 & 1 \end{pmatrix}.$$

令$a_i = E(T|X_0 = i)$从状态$i$到达状态5的期望时间。有 $a_5 = 0, a_i = 1 + \sum_{j=1}^5 p_{ij}a_j = 1 + \sum_{j=1}^4 p_{ij}a_j$.

$$a_1 = 1 + 0.5a_1 + 0.5a_2;$$
$$a_2 = 1 + 0.25a_1 + 0.25a_2 + 0.5a_3;$$
$$a_3 = 1 + 0.25a_2 + 0.25a_3 + 0.5a_4;$$
$$a_4 = 1 + 0.25a_3 + 0.25a_4 + 0.5a_5.$$

解此方程组从而得到 $a_2 = 10.25$.

### 5.4 极限定理及不变分布

#### 5.4.1 极限定理

对于一个系统来说，考虑它的长期的性质是很必要的，本节我们将研究Markov链的极限情况和非齐Markov链的有关性质。首先来看两个例子。

**例5.17** 设Markov链的转移矩阵为

$$P = \begin{pmatrix} 1-p & p \\ q & 1-q \end{pmatrix}, 0 < p, q < 1, \tag{20}$$

现在考虑$P^{(n)}$, 当$n \to \infty$时的情况。由$P^{(n)} = P^n$知，只需计算其的重要特征的极限。令

$$Q = \begin{pmatrix} 1 & -p \\ 1 & q \end{pmatrix}, \tag{21}$$

$$D = \begin{pmatrix} 1 & 0 \\ 0 & 1-p-q \end{pmatrix}, \tag{22}$$

则

$$Q^{-1} = \begin{pmatrix} \frac{q}{p+q} & \frac{p}{p+q} \\ -\frac{1}{p+q} & \frac{1}{p+q} \end{pmatrix}, \quad P = QDQ^{-1}. \tag{23}$$

从而

$$P^n = (QDQ^{-1})^n = Q\begin{pmatrix} 1 & 0 \\ 0 & (1-p-q)^n \end{pmatrix} Q^{-1}= \begin{pmatrix} \frac{q+p(1-p-q)^n}{p+q} & \frac{p-p(1-p-q)^n}{p+q} \\ \frac{q-q(1-p-q)^n}{p+q} & \frac{p+q(1-p-q)^n}{p+q} \end{pmatrix}. \tag{24}$$

由于$|1-p-q| < 1$，上式的极限为

$$\lim_{n\to\infty} P^n = \begin{pmatrix} \frac{q}{p+q} & \frac{p}{p+q} \\ \frac{q}{p+q} & \frac{p}{p+q} \end{pmatrix}. \tag{25}$$

可见此Markov链的n步转移概率有一个稳定的极限。

**例 5.18**

在例5.16中令$p = \frac{1}{3}$, 则

$$\lim_{n\to\infty} p_{00}^{(2n)} = \lim_{n\to\infty} \frac{(4 \times \frac{1}{3} \times \frac{2}{3})^n}{\sqrt{n\pi}} = 0. \tag{26}$$

令$p = \frac{1}{2}$, 则

$$\lim_{n\to\infty} p_{00}^{(2n)} = \lim_{n\to\infty} \frac{(4 \times \frac{1}{2} \times \frac{1}{2})^n}{\sqrt{n\pi}} = 0. \tag{27}$$

由（26）和（27）式知道，从出发经过足够次数的转移之后，系统在某一规定时刻回到原点的概率趋于0。

我们可以证明例5.17 中的状态是正常返态，而例5.16中$p = \frac{1}{3}$时状态0是非常返态，$p = \frac{1}{2}$时，0是零常返态。基于这些例子得到的一些观察（忽略了一些数学严格性），我们可以有Markov链的一个基本极限定理。

**定理 5.9**

若状态$j$是周期为$d$的常返状态，则

$$\lim_{n\to\infty} p_{jj}^{(nd)} = \frac{d}{\mu_j}, \tag{28}$$

当$\mu_j = \infty$时，$\frac{d}{\mu_j} = 0$。

在定理5.9中我们得到了常返状态的情形。一但状态$j$是非常返状态，由于$\sum_{n=1}^{\infty} p_{jj}^{(n)} < \infty$, 自然$\lim_{n\to\infty} p_{jj}^{(n)} = 0$。

**引理5.1**

设$i$为常返状态，则

$$i$为零常返态 $\Leftrightarrow \lim_{n\to\infty} p_{ii}^{(n)} = 0.$$

证明 若$i$为零常返状态,则 $\mu_i = \infty$, 从而$\lim_{n\to\infty} p_{ii}^{(nd)} = 0$。而当$m$不是$d$的整数倍时,$p_{ii}^{(m)} = 0$, 故$\lim_{n\to\infty} p_{ii}^{(n)} = 0$。

反之若$\lim_{n\to\infty} p_{ii}^{(n)} = 0$. 设$i$为正常返状态，则$\mu_i < \infty$, 由定理5.9, 知道$\lim_{n\to\infty} p_{ii}^{(nd)} > 0$, 矛盾。

**定理5.10**

若$j$为非常返状态或零常返状态，则对$\forall i \in S$

$$\lim_{n\to\infty} p_{ij}^{(n)} = 0. \tag{32}$$

证明 由引理5.1,得

$$p_{ij}^{(n)} = \sum_{l=1}^n f_{ij}^{(l)}p_{jj}^{(n-l)}. \tag{33}$$

对$N < n$, 有

$$\sum_{l=1}^n f_{ij}^{(l)}p_{jj}^{(n-l)} \leq \sum_{l=1}^N f_{ij}^{(l)}p_{jj}^{(n-l)} + \sum_{l=N+1}^n f_{ij}^{(l)} \tag{34}$$

先固定$N$,令$n \to \infty$, 由于$p_{jj}^{(n)} \to 0$, 所以(37)式右端第一项趋于0。再令$N \to \infty$, (37)式右端第二项因$\sum_{l=1}^{\infty}f_{ij}^{(l)} \leq 1$ 而趋于0, 故

$$\lim_{n\to\infty} p_{ij}^{(n)} = 0.$$

定理得证。

另外还有若$j$为正常返状态且周期为$d$, 则对$\forall i \to j, i \in S$

$$\lim_{n\to\infty} p_{ij}^{(nd)} = \frac{d}{\mu_j}. \tag{35}$$

证明 由引理5.1,得

$$p_{ij}^{(n)} = \sum_{l=1}^n f_{ij}^{(l)}p_{jj}^{(n-l)}. \tag{36}$$

对$N < n$, 有

$$\sum_{l=1}^N f_{ij}^{(l)}p_{jj}^{(n-l)} \leq p_{ij}^{(n)} \leq \sum_{l=1}^N f_{ij}^{(l)}p_{jj}^{(n-l)} + \sum_{l=N+1}^n f_{ij}^{(l)} \tag{37}$$

先固定$N$,令$n \to \infty$, 再令$N \to \infty$，可得

$$\frac{d}{\mu_j} \leq \lim_{n\to\infty} p_{ij}^{(n)} \leq \frac{d}{\mu_j}.$$

定理得证。

**推论5.2**

有限状态的Markov链，不可能全为非常返状态，至少有一个常返状态。

证明 假设所有状态都是非常返状态，记其个数为$N$。若考虑定理5.10，则将导出状态空间$S = \{1, 2, \cdots N\}$。若全都是非常返，对于任意$i \to j$，有 $p_{ij}^{(n)} \to 0$。当$n \to \infty$时，
$\sum_{j=1}^N p_{ij}^{(n)} \to 0$, 与$\sum_{j=1}^N p_{ij}^{(n)} = 1$，矛盾。若$S$中有零常返状态，设为$i$，令$C = \{j : i \to j\}$, 则有$\sum_{j\in C} p_{ij}^{(n)} = 1$ 并且对$j \in C, j \to i$. 但由于$j$为零常返状态。则 $\lim_{n\to\infty} p_{ij}^{(n)} = 0$, 从而$\sum_{j\in C} p_{ij}^{(n)} \to 0(n \to \infty)$, 矛盾。

**推论5.3**

若Markov链有一个零常返状态，则必有无限个零常返状态。

#### 5.4.2 不变分布与极限分布

前面我们只讨论了Markov 链的转移概率$p_{ij}$的有关问题，下面我们将讨论它的初始分布问题和出一些结论.首先是关于Markov链的不变分布和极限分布的概念。

**定义5.12**

对于Markov链，概率分布$\pi_j, j \in S$称为不变的，若

$$\pi_j = \sum_{i\in S} \pi_i p_{ij}. \tag{39}$$

可见，若Markov链的初始分布$P(X_0 = j) = p_j$是不变分布，则$X_1$的分布将是

$$P(X_1 = j) = \sum_{i\in S}P(X_1 = j|X_0 = i) \cdot P(X_0 = i)= \sum_{i\in S}p_{ij}p_i= p_j \tag{40}$$

这与$X_0$的分布是相同的，依次这推广$X_n, n = 0, 1, 2, 3, \cdots$将有相同的分布，这也是为什么称$p_i, i \in S$为不变分布的原因。

**定义5.13**

称Markov链是遍历的,如果所有状态相通且均有周期为1的正常返态。对于遍历的Markov链,极限

$$\lim_{n\to\infty} p_{ij}^{(n)} = \pi_j, \quad j \in S \tag{41}$$

称为Markov链的**极限分布**。

由定理5.11知, $\pi_j = \frac{1}{\mu_j}$。下面的定理说明对于不可约遍历的Markov链，极限分布就是不变分布并且还是唯一的不变分布。

**定理5.13**

对于不可约非周期的Markov链:
(1) 若它是遍历的则$\pi_j = \lim_{n\to\infty} p_{ij}^{(n)} > 0(j \in S)$是不变分布且是唯一的不变分布；
(2) 若状态都是瞬态的或为零常返的，则不变分布不存在。

证明 (1) 对遍历的Markov链，由定理5.10知$\lim_{n\to\infty} p_{ij}^{(n)} > 0$存在，记为$\pi_j$。

首先证明$\{\pi_j, j \in S\}$是不变分布。由于$\sum_{i\in S} p_{ij}^{(n)} = 1$, 则有

$$\lim_{n\to\infty} \sum_{i\in S} p_{ij}^{(n)} = 1 \tag{42}$$

由于(42) 式中极限与求和可交换。即有$\sum_{i\in S} \pi_j = 1$.

利用C-K方程,得

$$p_{ij}^{(n+1)} = \sum_{k\in S}p_{ik}^{(n)}p_{kj}$$

两边取极限,得

$$\lim_{n\to\infty} p_{ij}^{(n+1)} = \lim_{n\to\infty} \sum_{k\in S}p_{ik}^{(n)}p_{kj}= \sum_{k\in S}( \lim_{n\to\infty} p_{ik}^{(n)})p_{kj} \tag{44}$$

再证$\pi_j, j \in S$是唯一的不变分布. 假设另外还有一个不变分布$\tilde{\pi}_j, j \in S$, 则由$\tilde{\pi}_j = \sum_{k\in S} \tilde{\pi}_k p_{kj}$推导得到

$$\tilde{\pi}_j = \sum_{k\in S} \tilde{\pi}_k p_{kj}^{(n)}, n = 1, 2, \cdots \tag{45}$$

令$n \to \infty$, 对 (45) 式两端取极限,有

$$\tilde{\pi}_j = \sum_{i\in S} \tilde{\pi}_i \lim_{n\to\infty} p_{ij}^{(n)}= \sum_{i\in S} \tilde{\pi}_i \cdot \pi_j. \tag{46}$$

由于$\sum_{i\in S} \tilde{\pi}_i = 1$，因此$\tilde{\pi}_j = \pi_j$，证明了不变分布的唯一。

(2) 假设存在一个不变分布$\{\pi_j, j \in S\}$, 则由$\pi_j = \sum_{i\in S} \pi_i p_{ij}^{(n)},(n = 1, 2, \cdots)$成立，令$n \to \infty$知$p_{ij}^{(n)} \to 0$, 则推出$\pi_j = 0, j \in S$, 这是不可能的。于是对于非常返的或零常返的Markov链不存在不变分布。

**例5.20**

Markov链的转移矩阵为

$$P = \begin{pmatrix} 0.5 & 0.5 & 0 \\ 0.5 & 0 & 0.5 \\ 0 & 0.5 & 0.5 \end{pmatrix},$$

则它的不变分布满足

$$
\begin{cases}
\pi_1 & = 0.5\pi_1 + 0.5\pi_2, \\
\pi_2 & = 0.5\pi_1 + 0.5\pi_3, \\
\pi_3 & = 0.5\pi_2 + 0.5\pi_3,
\end{cases}
$$

解得 $\pi = (\pi_1, \pi_2, \pi_3) = (\frac{1}{3}, \frac{1}{3}, \frac{1}{3})$. 则

$$\lim_{n\to\infty} p_{ij}^{(n)} = \lim_{n\to\infty} P(X_n = j|X_0 = i) = \frac{1}{3}$$

即0时刻从出发在很久的时间之后Markov链处于状态1,2,3的概率均为$\frac{1}{3}$, 即$X_n$的极限分布为均匀分布。

**例5.21**

设有6个车站,车站中间的公路连接情况如图5-6所示。

汽车每天可以从一个站驶向与之直接相临的车站，并且在夜晚到达某车站留宿，次日凌晨重复相同的活动。设每天凌晨汽车在任何一个车站都是等可能的，试说明很长时间后，各站等晚留宿的汽车比例趋于稳定。求出这个比例以便正确地设置各站的服务规模。

解 以$\{X_n, n = 0, 1, \cdots \}$记第$n$天某辆汽车留宿的车站号。这是一个Makov链，转移概率矩阵为

$$P = \begin{pmatrix} 0 & \frac{1}{2} & 0 & 0 & 0 & \frac{1}{2} \\ \frac{1}{3} & 0 & \frac{1}{3} & 0 & 0 & \frac{1}{3} \\ 0 & \frac{1}{2} & 0 & \frac{1}{2} & 0 & 0 \\ 0 & 0 & \frac{1}{3} & 0 & \frac{1}{3} & \frac{1}{3} \\ 0 & 0 & 0 & \frac{1}{2} & 0 & \frac{1}{2} \\ \frac{1}{4} & \frac{1}{4} & 0 & \frac{1}{4} & \frac{1}{4} & 0 \end{pmatrix}.$$

解方程

$$
\begin{cases}
\pi P & = \pi \\
\sum_{i=1}^6 \pi_i & = 1
\end{cases}
$$

其中$\pi = (\pi_1, \pi_2, \pi_3, \pi_4, \pi_5, \pi_6)$, 可得$\pi = (\frac{1}{8}, \frac{3}{16}, \frac{1}{8}, \frac{3}{16}, \frac{1}{8}, \frac{1}{4})$. 从而无论开始汽车从哪一个车站出发在很长时间后他在任一个车站留宿的概率都是固定的，从而所有的汽车也将以一个稳定的比例在各车站留宿。

### MCMC（Markov Chain Monte Carlo）

考虑一个马氏链$X_0, X_1, X_2, \cdots , X_n, \cdots$, 其转移概率矩阵为$P$，初始分布为$\pi^{(0)}(x)$，则$X_i$的分布为 $\pi^{(i)}(x) = \pi^{(0)}(x)P^i$. 现在考虑基于马氏链来对某一分布$p(x)$进行抽样。

- 假设到第$n$步的时候马氏链收敛，则有$X_n, X_{n+1}, \cdots$服从同一分布，记为$\pi(x)$.
- 如果$\pi(x)$恰好是我们想抽样的分布$p(x)$，则$X_n, X_{n+1}, \cdots$ 即为来自目标分布$p(x)$的样本，尽管它们并不独立。

### MCMC（Markov Chain Monte Carlo）

这种抽样的思法在 1953 年被 Metropolis 想到了，为了研究粒子系统的平稳性质， Metropolis 考虑了物理学中常见的波尔兹曼分布的采样问题，首次提出了基于马氏链的蒙特卡罗方法，即 Metropolis 算法，并在最早的计算机上编程实现。Metropolis 算法是首个普适的采样方法，并且发了一系列 MCMC 方法，所以人们把它视为随机模拟技术腾飞的起点。Metropolis 的这篇论文被收录在《统计学中的重大突破》中， Metropolis 算法也被选进为二十世纪的十个重要的算法之一。

### 细致平稳条件

**定理**

如果马氏链的转移矩阵$P$和分布$\pi(x)$满足

$$\pi(i)P_{ij} = \pi(j)P_{ji}, \forall i,j,$$

则$\pi(x)$即为平稳分布

证明: 注意到

$$\sum_{i=1}\pi(i)P_{ij} = \sum_{i=1}\pi(j)P_{ji} = \pi(j)\sum_{i=1}P_{ji} = \pi(j).$$

### Metropolis-Hastings 算法

假设我们已经有一个转移矩阵为$Q = (q_{ij})$的马氏链。显然，通常情况下

$$p(i)q_{ij} \neq p(j)q_{ji}.$$

因此$p(x)$不太可能是这个马氏链的平稳分布。

引入$a_{ij}$，希望

$$p(i)q_{ij}a_{ij} = p(j)q_{ji}a_{ji}.$$

特别地，取$a_{ij} = p(j)q_{ji}, a_{ji} = p(i)q_{ij}$则上式成立。

若令$q_{ij}^* = q_{ij}a_{ij}, q_{ji}^* = q_{ji}a_{ji}$. 于是我们把原来具有转移矩阵 $Q$ 的一个很普通的马氏链，改造成具有转移矩阵 $Q^*$的马氏链，而$Q^*$恰好满足细致平稳条件，由此马氏链$Q^*$的平稳分布就是$p(x)$!

### Metropolis-Hastings 算法

在改造$Q$的过程中引入的$a_{ij}$称为接受率，物理意义可以理解为在原来的马氏链上，从状态$i$以$q_{ij}$的概率转跳到状态$j$时，我们以$a_{ij}$的概率接受这个转移，于是得到新的马氏链$Q^*$ 转移概率为$q_{ij}a_{ij}$。

$q_{ij}a_{ij}$可能很小，从而使得马氏链容易原地踏步，拒绝大量的跳转

### Metropolis-Hastings 算法

假设$a_{ij} = 0.1, a_{ji} = 0.2$, 此时满足细致平稳条件
$p(i)q_{ij} \times 0.1 = p(j)q_{ji} \times 0.2$
上式两边都乘 5 倍，我们改写为
$p(i)q_{ij} \times 0.5 = p(j)q_{ji} \times 1$
我们提高了接受率，而细致平稳条件并没有打破！

### Metropolis-Hastings 算法

令

$$a_{ij} = \min\left\{\frac{p(j)q_{ji}}{p(i)q_{ij}}, 1\right\}$$

### 5.4 群体消失模型与人口模型
#### 5.4.1 群体消失模型(分支过程)

考虑一个从单个祖先开始的群体,每个个体生命结束时以概 率$p_j = P(Z = j), j = 0,1,2,3,\dots$来产生了$j$个新的后代,与 别的个体产生的后代个数相互独立,其中Z为它产生的后代 数。以$X_n$记第$n$代的个体数,从而
$$
X_{n+1} = \sum_{i=1}^{X_n} Z_{n,i}
$$
其中$Z_{n,i}$表示第$n$代的第$i$个成员的后代的个数.由于考虑的是 单个祖先开始的,所以$X_0 = 1$.

首先来考虑第$n+1$代的平均个体数$EX_{n+1}$,对$X_n$取条件,有
$$
\begin{aligned}
EX_{n+1} &= E[E(X_{n+1}|X_n)] \\
&= \mu E(X_n) \\
&= \mu^2 E(X_{n-1}) \\
&= \dots \\
&= \mu^n E(X_1) = \mu^{n+1},
\end{aligned}
$$
其中$\mu = \sum_{i=0}^{\infty} ip_i$是每个个体的后代个数的均值。从而可以 看出,若$\mu < 1$则平均个体数单调下降趋于0。若$\mu = 1$时,各 代平均个体数相同。当$\mu > 1$时,平均个体数按指数阶上升至 无穷。

以$\pi_0$记从单个个体开始群体迟早灭绝的概率. 对初始个体 的后代取条件,可以导出一个确定$\pi_0$的方程
$$
\begin{aligned}
\pi_0 &= P(\text{群体消亡}) \\
&= \sum_{j=0}^{\infty} P(\text{群体消亡}|X_1 = j) \cdot p_j \\
&= \sum_{j=0}^{\infty} \pi_0^j p_j
\end{aligned}
$$
上面的第二个等式是因为群体最终灭绝是以第1代为祖先 的$j$个家族全部消亡,而各家族已经假定为独立的,每一家 族灭绝的概率均为$\pi_0$.

很自然我们很会假设:家族消亡与$\mu$有关,在此我们给出 一个定理,以证明$\pi_0 = 1$的充要条件是$\mu \le 1$(不考虑$p_0 = 1$和$p_0 = 0$的平凡情况,即家族在第0代后就消失或永不消 失)。

**定理**
设$0 < p_0 < 1$,则$\pi_0 = 1 \iff \mu \le 1$.

**证明** 由$\pi_0$的表达式
$$
\pi_0 = \sum_{j=0}^{\infty} \pi_0^j p_j = F(\pi_0), \tag{1}
$$

可知,它是直线$y = x$ 和曲线$y = F(x)$交点的横坐标,显 然$(1,1)$是一个交点。
当$p_0 + p_1 = 1$时, $y = F(x)$是一条直线,当$p_0+p_1 < 1$时, 由于
$$
F'(x) = \sum_{j=1}^{\infty} jx^{j-1}p_j > 0, \quad 0 < x < 1, \tag{2}
$$
$$
F''(x) = \sum_{j=2}^{\infty} j(j-1)x^{j-2}p_j > 0, \quad 0 < x < 1, \tag{3}
$$

可见$F(x)$是单调增加的凸函数,如图5.7所示.
图5-7 $F(x)$图示

从图5.7(a)可以知道,当$p_0+p_1 = 1$时,方程(1)只有一 个解,即$\pi_0 = 1$, 家族最终必定消亡。由图5.7(b)可分为两种 情况:(1)若$0 < s < 1, F(s) > s, F(1) = 1$,方程(1) 只有惟 一的解$\pi_0 = 1$; (2)存在一个$0 < s < 1$使得$F(s) = s$, 那么此 时$\pi_0$应取$s$还是1呢?可以证明,$\pi_0$必定取值为$s$,为此只需证 明$\pi_0$是方程(1)的最小解.利用归纳法,首先,当$n=1$时,
$$
\pi = \sum_{j=0}^{\infty} \pi_0^j p_j \ge \pi_0^0 p_0 = p_0 = P(X_1 = 0).
$$
假设$\pi \ge P(X_n = 0)$,则

$$
\begin{aligned}
P(X_{n+1} = 0) &= \sum_{j=0}^{\infty} P(X_{n+1} = 0|X_1 = j) \cdot p_j \\
&= \sum_{j=0}^{\infty} P(X_n = 0)^j \cdot p_j \\
&\le \sum_{j=0}^{\infty} \pi^j p_j \\
&= \pi.
\end{aligned}
$$
从而,对一切$n, \pi \ge P(X_n = 0)$.而$\lim_{n\to\infty} P(X_n = 0) = P(\text{群体最终灭绝}) = \pi_0$。从而$\pi \ge \pi_0$.这就证明了在这种情况下$\pi_0$取值应为$s$.

再来看在上述这三种情形下$\mu$的情况.
第一种情况,若$p_0+p_1=1$,显然
$$
\mu = \sum_{j=0}^{\infty} j p_j = p_1 < 1.
$$
第二种情况,若$p_0+p_1<1$而且方程(1)只有一个根$\pi_0 = 1$, 由图5.4.2容易看出,此时$F'(1) \le 1$而$F'(1) = \sum_{j=0}^{\infty} j p_j = \mu$, 从而$\mu \le 1$。
第三种情况下,容易看出$F'(1) = \mu > 1$, 这就证明了$\pi_0 = 1 \iff \mu \le 1$。
□

在实际应用中,考虑一个群体的真实增长时,分枝过程 的假定在群体达到无限之前就不成立了(比如独立同分布 性)。但另一方面,利用分枝过程研究消亡现象是有意义 的,因为一般灭绝常常发生在过程的早期。

外星人消亡问题
*   一个外星人来到地球后,它每天或者以1/2的概率自爆,或者以1/2的概率分 裂成两个外星人。此后每天,每个外星人都采取相同的行动,且彼此之间互相 独立,那么地球上最终没有外星人的概率是多少呢?
*   记所求概率为$\pi_0$,则根据全概率公式可知:
$$
\pi_0 = \frac{1}{2} + \frac{1}{2}\pi_0^2.
$$
解得:$\pi_0 = 1$。即外星人以概率1会最终灭亡。

外星人消亡问题
*   现在假设这个外星人以等概率或者自爆或者分裂为两个外星人或者什么也不 做。其他条件不变,这时最终消亡的概率是多少呢?
*   同样地,利用全概率公式有:
$$
\pi_0 = \frac{1}{3} + \frac{1}{3}\pi_0 + \frac{1}{3}\pi_0^2.
$$
同样可解得:$\pi_0 = 1$。

外星人消亡问题
*   进一步,现在这个外星人以等概率或者自爆或者分裂为两个外星人或者分裂为 三个外星人或者什么也不做。则有
$$
\pi_0 = \frac{1}{4} + \frac{1}{4}\pi_0 + \frac{1}{4}\pi_0^2 + \frac{1}{4}\pi_0^3.
$$
变形后得:
$$
(\pi_0 - 1)(\pi_0^2 + 2\pi_0 - 1) = 0.
$$
故得两个解:$\pi_0 = 1$或者$\pi_0 = \sqrt{2}-1$。那么外星人最终消亡的概率是1还 是0.4左右呢?
*   由之前的定理可知对于等可能采取四种行动的外星人,其最终灭亡的概率 是$\pi_0 = \sqrt{2}-1$。同时也可知如果平均后代个数小于等于1,则群体最终会以 概率1消亡。

### 5.5 连续时间Markov链
前面几节讨论的是时间和状态空间都是离散的Markov过 程,本节我们将介绍另外一种情况的Markov过程,它的状 态空间仍然是离散的,但时间是连续变化的,称为连续时 间Markov链,也称为纯不连续Markov过程。我们会给出它 的一些性质、一个重要的方程(Kolmogrov方程)和一个重 要的应用(生灭过程)。

#### 5.5.1 连续时间Markov链
**定义5.14**
过程 $\{X(t),t \ge 0\}$ 的状态空间 $S$ 为离散空间,为方便书写，设 $S$ 为 $\{0,1,2,\dots\}$ 或其子集。若对一切 $s, t \ge 0$ 及 $i, j \in S$, 有
$$
P(X(t + s) = j|X(s) = i, X(u) = x(u), 0 \le u < s)
= P(X(t+s) = j|X(s) = i)
\tag{4}
$$
成立,则称$\{X(t), t \ge 0\}$是一个连续时间Markov链。

条件概率$P(X(t + s) = j|X(s) = i)$记作$p_{ij}(s,t)$表示过 程在时刻$s$处于状态$i$,经$t$时间后转移到$j$的转移概率,并 称$P(s,t) = (p_{ij}(s,t))$为相应的转移概率矩阵。

**定义5.15**
称连续时间Markov链是时齐的,若$p_{ij}(s,t)$与$s$无关.简 记$p_{ij}(s,t) = p_{ij}(t)$,相应的记$P(t) = (p_{ij}(t))$。

我们只讨论时齐的连续时间Markov链,并且简称为连续时 间Markov链(在不引起混淆的情况下有时也称为Markov链)。

对于连续时间Markov链来说,除了要考虑在某一时刻它将 处于什么状态外,还关心它在离开这个状态之前会停留多长 的时间,从它具备Markov性来看,这个”停留时间”具备”无 记忆性”的特征,应该服从指数分布,下面我们给出一个具 体的解释。

**定理5.18**
设$\{X(t),t \ge 0\}$是连续时间Markov链,假定在时刻0过程刚 刚到达$i(i \in S)$。以$\tau_i$记过程在离开$i$之前在$i$停留的时间, 则$\tau_i$服从指数分布。

**证明**
我们只需证明对$s, t \ge 0$, 有
$$
P(\tau_i > s + t|\tau_i > s) = P(\tau_i > t). \tag{5}
$$
即无记忆性。
注意到
$$
\{\tau_i > s\} \iff \{X(u) = i, 0 < u \le s|X(0) = i\}
$$
$$
\{\tau_i > s + t\} \iff \{X(u) = i, 0 < u \le s, X(v) = i, s < v \le s + t|X(0) = i\}
$$

则
$$
\begin{aligned}
&P(\tau_i > s + t|\tau_i > s) \\
&= P(X(u) = i, 0 < u \le s, X(v) = i, s < v \le s + t|X(u) = i, 0 \le u \le s) \\
&= P(X(v) = i, s < v \le s + t|X(s) = i) \\
&= P(X(u) = i, 0 < u \le t|X(0) = i) \\
&= P(\tau_i > t).
\end{aligned}
$$
得证. □
由上述定理,实际上我们得到了另外一个构造连续时间Markov链 的方法,它是具有如下两条性质的随机过程.

(1)在转移到下一个状态之前处于状态$i$的时间服从参数 为$\mu_i$的指数分布;
(2)在过程离开状态$i$时,将以概率$P_{ij}$到达$j$,且$\sum_{j \in S} P_{ij} = 1$.
**注** 当$\mu_i = \infty$时,它在状态$i$停留的平均时间为0,即一旦 进入马上离开,称这样的状态为瞬过的,但假设在我们考虑 的连续时间Markov链中不存在瞬过态,即,设对$\forall i, 0 \le \mu_i < +\infty$(若$\mu_i = 0$,称为吸收态,即一旦进入,将停留的平均时 间无限长)。由此我们看出,连续时间Markov链是一个做下 面的运动的随机过程:它以一个Markov链的方式在各个状态 之间转移,在两次转移之间以指数分布停留。

**定义5.16**
称一个连续时间Markov链是正则的,若以概率1在任意有限 长的时间内转移的次数是有限的。
从而可得连续性条件
$$
\lim_{t\to 0} p_{ij}(t) = \delta_{ij} = \begin{cases} 1, & i=j, \\ 0, & i \ne j. \end{cases} \tag{6}
$$
以下我们总假定所考虑的Markov链都满足正则性条件.下 面是几个连续时间Markov链的典型例子.

**例5.22**
(Poisson过程)参数为$\lambda$的Poisson过程$\{N(t), t \ge 0\}$,取值 为$\{0,1,2,\dots\}$由第3章知道,它在任一个状态$i$停留的时间 服从指数分布,并且在离开$i$时以概率1转到$i+1$(又一个 事件发生).由Poisson过程的独立增量性容易看出它在$i$停 留的时间与状态的转移是独立的(特别是由它的平稳增量 性$\mu_i = \mu_{i+1} = \lambda, i = 0,1,2,\dots$),从而Poisson过程是时齐的 连续时间Markov链.对$i \in S$, 它的转移概率为

$$
\begin{aligned}
p_{i,i}(t) &= P(N(t+s) = i | N(s) = i) \\
&= P(N(t) = 0) \\
&= e^{-\lambda t}
\end{aligned}
$$
$$
\begin{aligned}
p_{i,i+1}(t) &= P(N(t+s) = i+1 | N(s) = i) \\
&= P(N(t) = 1) \\
&= \lambda t e^{-\lambda t}
\end{aligned}
$$
$$
p_{i,j}(t) = \frac{(\lambda t)^{j-i}}{(j-i)!} e^{-\lambda t}, \quad j > i+1
$$
$$
p_{i,j}(t) = 0, \quad j < i.
$$

