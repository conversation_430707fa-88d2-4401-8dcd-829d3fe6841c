# async_pdf_splitter_gemini.py 的早期版本
import os
import base64
import time
import json
from pathlib import Path
import requests
import pymupdf  # PyMuPDF
from google import genai
from google.genai import types, errors

os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7897'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7897'

class PDFSplitterWithGemini:
    def __init__(self, gemini_api_key=None, gemini_model="gemini-2.5-pro"):
        """
        初始化PDF拆分器
        
        Args:
            gemini_api_key (str, optional): Gemini API密钥，仅在需要调用API时提供
            gemini_model (str): 使用的Gemini模型
        """
        self.api_key = gemini_api_key
        self.model = gemini_model
        
    def extract_pdf_toc(self, pdf_path):
        """
        提取PDF目录信息
               
        Args:
            pdf_path (str): PDF文件路径
            
        Returns:
            list: 二级目录列表，格式：[(title, start_page, end_page), ...]
        """
        doc = pymupdf.open(pdf_path)
        toc = doc.get_toc()
        
        if not toc:
            print("警告：PDF文档没有找到目录信息")
            doc.close()
            return []
        
        # 筛选二级目录（level=2）
        level_2_items = [item for item in toc if item[0] == 2]
        
        if not level_2_items:
            print("警告：没有找到二级目录，尝试使用一级目录")
            level_2_items = [item for item in toc if item[0] == 1]
        
        chapters = []
        for i, item in enumerate(level_2_items):
            level, title, page = item[0], item[1], item[2]
            start_page = page
            
            # 确定结束页码（包含下一章第一页）
            if i < len(level_2_items) - 1:
                end_page = level_2_items[i + 1][2]  # 包含下一章第一页
            else:
                end_page = doc.page_count
            
            chapters.append((title, start_page, end_page))
        
        doc.close()
        return chapters
    
    def split_pdf_by_chapters(self, pdf_path, chapters=None, output_dir="split_pdfs"):
        """
        根据章节信息拆分PDF
        
        Args:
            pdf_path (str): 原PDF文件路径
            chapters (list, optional): 章节信息列表，如果为None则自动提取
            output_dir (str): 输出目录
            
        Returns:
            list: 拆分后的PDF文件信息列表
        """
        # 如果没有提供章节信息，自动提取
        if chapters is None:
            chapters = self.extract_pdf_toc(pdf_path)
            if not chapters:
                print("无法提取目录信息，拆分失败")
                return []
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        doc = pymupdf.open(pdf_path)
        split_files = []
        
        print(f"\n🔄 开始拆分PDF: {pdf_path}")
        print(f"📁 输出目录: {output_dir}")
        print("=" * 60)
        
        for i, (title, start_page, end_page) in enumerate(chapters, 1):
            # 清理文件名中的特殊字符
            safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            output_filename = f"{i:02d}_{safe_title}.pdf"
            output_path = os.path.join(output_dir, output_filename)
            
            # 创建新的PDF文档
            new_doc = pymupdf.open()
            
            # 插入指定页面范围（PyMuPDF使用0-based索引）
            new_doc.insert_pdf(doc, from_page=start_page-1, to_page=end_page-1)
            
            # 保存拆分的PDF
            new_doc.save(output_path)
            new_doc.close()
            
            # 获取文件大小
            file_size = os.path.getsize(output_path)
            file_size_mb = file_size / (1024 * 1024)
            
            split_info = {
                'index': i,
                'title': title,
                'file_path': output_path,
                'filename': output_filename,
                'page_range': f"{start_page}-{end_page}",
                'start_page': start_page,
                'end_page': end_page,
                'file_size': file_size,
                'file_size_mb': file_size_mb,
                'pages_count': end_page - start_page + 1
            }
            
            split_files.append(split_info)
            
            # 显示拆分进度
            status_icon = "✅" if file_size_mb <= 20 else "⚠️"
            print(f"{status_icon} [{i:02d}] {safe_title}")
            print(f"    📄 页面: {start_page}-{end_page} ({end_page - start_page + 1}页)")
            print(f"    📦 大小: {file_size_mb:.2f}MB")
            print(f"    📁 文件: {output_filename}")
            if file_size_mb > 20:
                print(f"    ⚠️  文件超过20MB限制，API调用时将被跳过")
            print()
        
        doc.close()
        
        print("=" * 60)
        print(f"✅ 拆分完成！共生成 {len(split_files)} 个文件")
        return split_files
    
    def preview_split_tasks(self, split_files, custom_prompt=""):
        """
        预览即将发送到API的任务信息
        
        Args:
            split_files (list): 拆分文件信息列表
            custom_prompt (str): 自定义prompt
        """
        print("\n" + "=" * 80)
        print("📋 API 任务预览")
        print("=" * 80)
        
        valid_files = [f for f in split_files if f['file_size_mb'] <= 20]
        skipped_files = [f for f in split_files if f['file_size_mb'] > 20]
        
        print(f"📊 统计信息:")
        print(f"   • 总文件数: {len(split_files)}")
        print(f"   • 可处理文件: {len(valid_files)}")
        print(f"   • 跳过文件: {len(skipped_files)} (超过20MB限制)")
        
        if valid_files:
            total_size = sum(f['file_size_mb'] for f in valid_files)
            total_pages = sum(f['pages_count'] for f in valid_files)
            print(f"   • 总处理大小: {total_size:.2f}MB")
            print(f"   • 总页面数: {total_pages}页")
        
        print(f"\n🤖 使用模型: {self.model}")
        print(f"📝 自定义Prompt: {'已设置' if custom_prompt.strip() else '未设置'}")
        
        if custom_prompt.strip():
            print(f"\n📄 Prompt 内容预览:")
            print("─" * 40)
            preview_text = custom_prompt[:200] + "..." if len(custom_prompt) > 200 else custom_prompt
            print(preview_text)
            print("─" * 40)
        
        print(f"\n📋 处理任务列表:")
        print("─" * 80)
        
        for file_info in split_files:
            status = "✅ 将处理" if file_info['file_size_mb'] <= 20 else "⏭️ 将跳过"
            print(f"{status} [{file_info['index']:02d}] {file_info['title']}")
            print(f"       📄 {file_info['page_range']} | 📦 {file_info['file_size_mb']:.2f}MB")
        
        if skipped_files:
            print(f"\n⚠️  以下文件将被跳过:")
            for file_info in skipped_files:
                print(f"   • {file_info['filename']} ({file_info['file_size_mb']:.2f}MB)")
        
        # 估算API成本（基于页面数）
        if valid_files:
            total_pages = sum(f['pages_count'] for f in valid_files)
            print(f"\n💰 预估成本信息:")
            print(f"   • PDF按图片计费，每页约等于1张图片")
            print(f"   • 总处理页数: {total_pages}页")
            print(f"   • 建议检查当前Gemini API定价")
        
        print("=" * 80)
    
    def confirm_api_processing(self):
        """
        交互式确认是否继续API处理
        
        Returns:
            bool: 用户确认结果
        """
        print("\n❓ 确认选项:")
        print("   1. 继续提交到 Gemini API")
        print("   2. 取消，稍后手动处理")
        print("   3. 仅处理指定的文件")
        
        while True:
            choice = input("\n请选择 (1/2/3): ").strip()
            if choice == '1':
                return True, None
            elif choice == '2':
                print("✋ 已取消API处理")
                return False, None
            elif choice == '3':
                return True, self._select_files_to_process()
            else:
                print("❌ 请输入有效选择 (1/2/3)")
    
    def _select_files_to_process(self):
        """
        让用户选择要处理的文件
        
        Returns:
            list: 选中的文件索引列表
        """
        print("\n📝 请输入要处理的文件编号 (例如: 1,3,5-7):")
        while True:
            try:
                user_input = input("文件编号: ").strip()
                selected_indices = []
                
                for part in user_input.split(','):
                    part = part.strip()
                    if '-' in part:
                        start, end = map(int, part.split('-'))
                        selected_indices.extend(range(start, end + 1))
                    else:
                        selected_indices.append(int(part))
                
                return sorted(set(selected_indices))
            except ValueError:
                print("❌ 输入格式错误，请使用逗号分隔的数字或范围 (如: 1,3,5-7)")
        
    def send_to_gemini(self, pdf_bytes, custom_prompt, max_retries=3, think_budget=None):
        """
        将PDF和prompt发送到Gemini API
        
        Args:
            pdf_base64 (str): base64编码的PDF数据
            custom_prompt (str): 用户提供的预设prompt
            max_retries (int): 最大重试次数
            
        Returns:
            str: Gemini API的响应文本
        """        
        client = genai.Client(
            api_key=self.api_key,
        )

        model = "gemini-2.5-pro"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=custom_prompt),
                    types.Part.from_bytes(
                        mime_type="application/pdf",
                        data=pdf_bytes,
                    ),
                ],
            )
        ]
        generate_content_config = types.GenerateContentConfig(
            thinking_config = types.ThinkingConfig(
                thinking_budget=32768,
            ),
            response_mime_type="text/plain",
        )

        try:
            response = client.models.generate_content(
                model=model,
                contents=contents,
                config=generate_content_config,
            )
            return response.text
        except errors.APIError as e:
            print(f"API错误代码: {e.code}")  # 这里有错误代码，如404
            print(f"错误信息: {e.message}")
        except Exception as e:
            print(f"其他错误: {e}")
    
    def save_text_output(self, content, output_path):
        """
        保存文本内容到文件
        
        Args:
            content (str): 要保存的文本内容
            output_path (str): 输出文件路径
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def process_with_gemini_api(self, split_files, custom_prompt, output_dir="gemini_output", selected_indices=None, think_budget=None):
        """
        使用Gemini API处理拆分的PDF文件
        
        Args:
            split_files (list): 拆分文件信息列表
            custom_prompt (str): 用户提供的预设prompt
            output_dir (str): 输出目录
            selected_indices (list, optional): 指定处理的文件索引
        """
        if not self.api_key:
            print("❌ 错误：未提供Gemini API密钥")
            return
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 筛选要处理的文件
        if selected_indices:
            files_to_process = [f for f in split_files if f['index'] in selected_indices]
        else:
            files_to_process = [f for f in split_files if f['file_size_mb'] <= 20]
        
        print(f"\n🤖 开始Gemini API处理...")
        print(f"📁 输出目录: {output_dir}")
        print(f"📋 处理文件数: {len(files_to_process)}")
        print("=" * 60)
        
        successful = 0
        failed = 0
        
        for i, file_info in enumerate(files_to_process, 1):
            print(f"\n[{i}/{len(files_to_process)}] 🔄 处理: {file_info['title']}")
            
            try:
                with open(file_info['file_path'], 'rb') as f:
                    pdf_bytes = f.read()

                # 发送到Gemini
                response_text = self.send_to_gemini(pdf_bytes, custom_prompt, think_budget=think_budget)
                
                # 保存响应文本
                text_filename = f"{Path(file_info['file_path']).stem}.txt"
                text_output_path = os.path.join(output_dir, text_filename)
                self.save_text_output(response_text, text_output_path)
                
                if response_text.startswith("错误："):
                    print(f"❌ 失败: {response_text}")
                    failed += 1
                else:
                    print(f"✅ 成功保存到: {text_filename}")
                    successful += 1
                
            except Exception as e:
                print(f"❌ 处理失败: {str(e)}")
                failed += 1
            
            # 避免过快请求API
            if i < len(files_to_process):
                time.sleep(1)
        
        print("\n" + "=" * 60)
        print(f"🎉 处理完成！")
        print(f"✅ 成功: {successful} 个文件")
        print(f"❌ 失败: {failed} 个文件")
        print(f"📁 输出目录: {output_dir}")


def main():
    """
    主函数 - 分步骤处理示例
    """
    # 配置参数
    PDF_PATH = "数学分析上册fitz.pdf"  # 请替换为您的PDF文件路径
    
    THINK_BUDGET = 16000

    # 预设prompt
    CUSTOM_PROMPT = """
    将pdf内容转换为markdown，注意下面记号格式：

    1. 忽略页眉和页脚上的内容。
    2. 标题级别
      - 章标题：2级比如## 第七章 相似标准型
      - 节标题：3级比如### § 6.4 特征值的估计
      - 节的习题标题：3级比如### 习题 6.4
      - 历史与展望、复习题X：3级标题比如### 复习题六
    3. 数学记号分隔符
      - 行内公式：使用$，**分隔符和公式之间**不要使用空格，比如$\lambda$（不要使用$ \lambda $这样的错误格式）
      - 行间公式：使用$$，分隔符和公式块之间换行，比如$$\n\frac{1}{n}\n$$。对于多行递推公式，保留与课件中一致的的等号对齐格式。
    4. 使用**加粗强调的内容：定义、引理、定理、推论、注、例，包含相应的序号。
    5. 仅当pdf内容中的行间公式存在公式编号时，转换相应的公式编号，使用` \tag{xxx}`在行间公式末尾追加，比如$$\n\frac{1}{n} \tag{7.1.2}\n$$。
    6. 不要使用HTML标签。
    7. 严格按照原始pdf内容图像进行转换，确保markdown内容完整一致，**不要进行**解释、修改、省略。
    8. 不要使用`[cite: <number>]`格式或其它记号引用原始文档内容，从而确保输出的内容与原始pdf内容一致。

    此外，注意结合语境含义来理解pdf文档中的图像内容，从而更准确地识别数学记号，比如区分 a 与 \alpha。
    """
    
    print("🚀 PDF拆分与Gemini API处理工具")
    print("=" * 50)
    
    # 第一步：创建处理器（暂不提供API密钥）
    processor = PDFSplitterWithGemini()
    
    # 第二步：拆分PDF
    try:
        split_files = processor.split_pdf_by_chapters(PDF_PATH)
        
        if not split_files:
            print("❌ 拆分失败，程序退出")
            return
        
        # 第三步：预览任务
        processor.preview_split_tasks(split_files, CUSTOM_PROMPT)
        
        # 第四步：用户确认
        continue_processing, selected_indices = processor.confirm_api_processing()
        
        if continue_processing:
            # 第五步：设置API密钥并处理
            api_key = input("\n🔑 请输入您的Gemini API密钥: ").strip()
            if not api_key:
                print("❌ 未提供API密钥，退出")
                return
            
            processor.api_key = api_key
            processor.process_with_gemini_api(
                split_files, 
                CUSTOM_PROMPT, 
                selected_indices=selected_indices,
                think_budget=THINK_BUDGET
            )
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")


if __name__ == "__main__":
    # 安装依赖提示
    print("请确保已安装必要的依赖包:")
    print("pip install PyMuPDF requests")
    print("=" * 50)
    
    main()