import fitz  # PyMuPDF
import uuid
import hashlib
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle

def generate_page_hash(pdf_path, page_idx):
    """
    为PDF页面生成UUID哈希值
    """
    doc = fitz.open(pdf_path)
    page = doc[page_idx]
    pixmap = page.get_pixmap(annots=False)
    samples_hash = hashlib.sha256(pixmap.samples).hexdigest()
    page_uuid = str(uuid.uuid5(uuid.NAMESPACE_OID, samples_hash))
    doc.close()
    return page_uuid

def compare_pdf_pages(pdf1_path, pdf2_path, num_pages=None):
    """
    比较两个PDF文件的前n页
    """
    # 打开两个PDF文件
    doc1 = fitz.open(pdf1_path)
    doc2 = fitz.open(pdf2_path)
    
    # 确定要比较的页数
    max_pages1 = len(doc1)
    max_pages2 = len(doc2)
    
    if num_pages is None:
        num_pages = min(max_pages1, max_pages2)
    else:
        num_pages = min(num_pages, max_pages1, max_pages2)
    
    print(f"比较前 {num_pages} 页")
    print(f"PDF1 总页数: {max_pages1}")
    print(f"PDF2 总页数: {max_pages2}")
    print("-" * 50)
    
    identical_count = 0
    different_count = 0
    different_pages = []
    
    # 逐页比较
    for i in range(num_pages):
        try:
            hash1 = generate_page_hash(pdf1_path, i)
            hash2 = generate_page_hash(pdf2_path, i)
            
            if hash1 == hash2:
                identical_count += 1
                print(f"第 {i+1} 页: 一致 ✓")
            else:
                different_count += 1
                different_pages.append(i)
                print(f"第 {i+1} 页: 不一致 ✗")
                print(f"  PDF1 哈希: {hash1}")
                print(f"  PDF2 哈希: {hash2}")
                
        except Exception as e:
            print(f"第 {i+1} 页比较出错: {str(e)}")
            different_count += 1
            different_pages.append(i)
    
    doc1.close()
    doc2.close()
    
    # 统计报告
    print("\n" + "="*60)
    print("比较结果统计:")
    print(f"总共比较页数: {num_pages}")
    print(f"一致页数: {identical_count}")
    print(f"不一致页数: {different_count}")
    print(f"一致率: {identical_count/num_pages*100:.1f}%")
    print("="*60)
    
    return different_pages

def display_different_pages(pdf1_path, pdf2_path, different_pages):
    """
    使用matplotlib展示不同页面的对比
    """
    if not different_pages:
        print("没有发现不同的页面，无需展示")
        return
    
    doc1 = fitz.open(pdf1_path)
    doc2 = fitz.open(pdf2_path)
    
    for page_idx in different_pages:
        try:
            # 获取页面像素图
            page1 = doc1[page_idx]
            page2 = doc2[page_idx]
            
            pixmap1 = page1.get_pixmap(annots=False)
            pixmap2 = page2.get_pixmap(annots=False)
            
            # 转换为numpy数组
            img1 = np.frombuffer(pixmap1.samples, dtype=np.uint8)
            img1 = img1.reshape(pixmap1.height, pixmap1.width, pixmap1.n)
            
            img2 = np.frombuffer(pixmap2.samples, dtype=np.uint8)
            img2 = img2.reshape(pixmap2.height, pixmap2.width, pixmap2.n)
            
            # 创建对比图
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 10))
            
            # 左侧显示PDF1的页面
            ax1.imshow(img1)
            ax1.set_title(f'PDF1 - 第 {page_idx + 1} 页\n文件: 26王道数据结构fitz.pdf', 
                         fontsize=12, fontweight='bold', color='blue')
            ax1.axis('off')
            ax1.add_patch(Rectangle((0, 0), img1.shape[1], img1.shape[0], 
                                  linewidth=3, edgecolor='blue', facecolor='none'))
            
            # 右侧显示PDF2的页面
            ax2.imshow(img2)
            ax2.set_title(f'PDF2 - 第 {page_idx + 1} 页\n文件: 26王道数据结构.pdf', 
                         fontsize=12, fontweight='bold', color='red')
            ax2.axis('off')
            ax2.add_patch(Rectangle((0, 0), img2.shape[1], img2.shape[0], 
                                  linewidth=3, edgecolor='red', facecolor='none'))
            
            plt.tight_layout()
            plt.suptitle(f'页面对比 - 第 {page_idx + 1} 页 (不同页面)', 
                        fontsize=16, fontweight='bold', y=0.98)
            
            # 显示图像
            plt.show()
            
            # 等待用户确认继续
            input(f"按回车键继续查看下一个不同页面... ({len(different_pages) - different_pages.index(page_idx) - 1} 个剩余)")
            
        except Exception as e:
            print(f"展示第 {page_idx + 1} 页时出错: {str(e)}")
            continue
    
    doc1.close()
    doc2.close()

def main():
    """
    主函数
    """
    # PDF文件路径
    pdf1_path = "pdf-preprocess/4.408/26王道数据结构fitz.pdf"
    pdf2_path = "goodnotes/GoodNotes/考研材料/26王道数据结构.pdf"
    
    print("PDF页面比较工具")
    print("="*60)
    print(f"PDF1: {pdf1_path}")
    print(f"PDF2: {pdf2_path}")
    print()
    
    # 询问要比较的页数
    try:
        num_pages_input = input("请输入要比较的前n页数量 (直接回车比较全部): ").strip()
        if num_pages_input:
            num_pages = int(num_pages_input)
        else:
            num_pages = None
    except ValueError:
        print("输入无效，将比较全部页面")
        num_pages = None
    
    print()
    
    try:
        # 比较页面
        different_pages = compare_pdf_pages(pdf1_path, pdf2_path, num_pages)
        
        # 询问是否显示不同页面
        if different_pages:
            show_diff = input(f"\n发现 {len(different_pages)} 个不同页面，是否要可视化显示？(y/n): ").strip().lower()
            if show_diff in ['y', 'yes', 'Y', '是', '要']:
                print("\n开始显示不同页面...")
                display_different_pages(pdf1_path, pdf2_path, different_pages)
                print("所有不同页面已展示完毕！")
            else:
                print("跳过可视化显示")
        else:
            print("\n✓ 所有比较的页面都完全一致！")
            
    except Exception as e:
        print(f"程序执行出错: {str(e)}")

if __name__ == "__main__":
    main()
