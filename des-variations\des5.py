# des4.py
# 优化点：
# - 使用 Python 整数表示 64/32 位分组，避免 list-of-bits 频繁创建与索引
# - 预计算 SP 表（S-Box + P 置换合并）
# - IP/FP 对 64 位整数直接做置换
# - f 函数仅做：按 E 取 6 位 -> 与子密钥异或 -> SP 查表 -> 累加异或

# -------------------- 常量与表 --------------------
S_BOXES = [
    [
        [14,4,13,1,2,15,11,8,3,10,6,12,5,9,0,7],
        [0,15,7,4,14,2,13,1,10,6,12,11,9,5,3,8],
        [4,1,14,8,13,6,2,11,15,12,9,7,3,10,5,0],
        [15,12,8,2,4,9,1,7,5,11,3,14,10,0,6,13]
    ],
    [
        [15,1,8,14,6,11,3,4,9,7,2,13,12,0,5,10],
        [3,13,4,7,15,2,8,14,12,0,1,10,6,9,11,5],
        [0,14,7,11,10,4,13,1,5,8,12,6,9,3,2,15],
        [13,8,10,1,3,15,4,2,11,6,7,12,0,5,14,9]
    ],
    [
        [10,0,9,14,6,3,15,5,1,13,12,7,11,4,2,8],
        [13,7,0,9,3,4,6,10,2,8,5,14,12,11,15,1],
        [13,6,4,9,8,15,3,0,11,1,2,12,5,10,14,7],
        [1,10,13,0,6,9,8,7,4,15,14,3,11,5,2,12]
    ],
    [
        [7,13,14,3,0,6,9,10,1,2,8,5,11,12,4,15],
        [13,8,11,5,6,15,0,3,4,7,2,12,1,10,14,9],
        [10,6,9,0,12,11,7,13,15,1,3,14,5,2,8,4],
        [3,15,0,6,10,1,13,8,9,4,5,11,12,7,2,14]
    ],
    [
        [2,12,4,1,7,10,11,6,8,5,3,15,13,0,14,9],
        [14,11,2,12,4,7,13,1,5,0,15,10,3,9,8,6],
        [4,2,1,11,10,13,7,8,15,9,12,5,6,3,0,14],
        [11,8,12,7,1,14,2,13,6,15,0,9,10,4,5,3]
    ],
    [
        [12,1,10,15,9,2,6,8,0,13,3,4,14,7,5,11],
        [10,15,4,2,7,12,9,5,6,1,13,14,0,11,3,8],
        [9,14,15,5,2,8,12,3,7,0,4,10,1,13,11,6],
        [4,3,2,12,9,5,15,10,11,14,1,7,6,0,8,13]
    ],
    [
        [4,11,2,14,15,0,8,13,3,12,9,7,5,10,6,1],
        [13,0,11,7,4,9,1,10,14,3,5,12,2,15,8,6],
        [1,4,11,13,12,3,7,14,10,15,6,8,0,5,9,2],
        [6,11,13,8,1,4,10,7,9,5,0,15,14,2,3,12]
    ],
    [
        [13,2,8,4,6,15,11,1,10,9,3,14,5,0,12,7],
        [1,15,13,8,10,3,7,4,12,5,6,11,0,14,9,2],
        [7,11,4,1,9,12,14,2,0,6,10,13,15,3,5,8],
        [2,1,14,7,4,10,8,13,15,12,9,0,3,5,6,11]
    ]
]

PC2 = [13,16,10,23,0,4,2,27,14,5,20,9,
       22,18,11,3,25,7,15,6,26,19,12,1,
       40,51,30,36,46,54,29,39,50,44,32,47,
       43,48,38,55,33,52,45,41,49,35,28,31]

IP_TABLE = [
    57,49,41,33,25,17,9,1,59,51,43,35,27,19,11,3,
    61,53,45,37,29,21,13,5,63,55,47,39,31,23,15,7,
    56,48,40,32,24,16,8,0,58,50,42,34,26,18,10,2,
    60,52,44,36,28,20,12,4,62,54,46,38,30,22,14,6
]

FP_TABLE = [
    39,7,47,15,55,23,63,31,
    38,6,46,14,54,22,62,30,
    37,5,45,13,53,21,61,29,
    36,4,44,12,52,20,60,28,
    35,3,43,11,51,19,59,27,
    34,2,42,10,50,18,58,26,
    33,1,41,9,49,17,57,25,
    32,0,40,8,48,16,56,24
]

E_TABLE = [
    31, 0, 1, 2, 3, 4,
    3, 4, 5, 6, 7, 8,
    7, 8, 9, 10,11,12,
    11,12,13,14,15,16,
    15,16,17,18,19,20,
    19,20,21,22,23,24,
    23,24,25,26,27,28,
    27,28,29,30,31,0
]

P_TABLE = [
    15,6,19,20,28,11,27,16,
    0,14,22,25,4,17,30,9,
    1,7,23,13,31,26,2,8,
    18,12,29,5,21,10,3,24
]

LOOP_TABLE = [1,1,2,2,2,2,2,2,1,2,2,2,2,2,2,1]

_HEX = '0123456789ABCDEF'


# -------------------- 预计算 --------------------
# E 分成 8 组×6位
_E_GROUPS = [E_TABLE[i*6:(i+1)*6] for i in range(8)]
# 将 E 的索引改为“用于 32 位整数取位的移位量”，int 的 bit31 对应索引0
_E_SHIFTS = [[31 - idx for idx in grp] for grp in _E_GROUPS]

# P 置换下，每个 S-Box 的 4 个输出位（bit3..bit0）落到 32 位目标位置
_P_DST_POS = [[0,0,0,0] for _ in range(8)]
for dest, src in enumerate(P_TABLE):
    m, k = divmod(src, 4)  # 第 m 个 S-Box 的第 k 位
    _P_DST_POS[m][k] = dest

# PC2 也按 8×6 分组（配合 E 组）
_PC2_GROUPS = [PC2[i*6:(i+1)*6] for i in range(8)]

# 预计算 SP 表：8 组×64 项，每项为 32 位掩码（S-Box 后直接落到 P 的目标位）
_SP_BOXES = []
for m in range(8):
    dst = _P_DST_POS[m]
    table = [0] * 64
    sb = S_BOXES[m]
    for v in range(64):
        row = (((v >> 5) & 1) << 1) | (v & 1)  # bit5 与 bit0 组成行
        col = (v >> 1) & 0xF                   # 中间 4 位
        nib = sb[row][col]                     # 0..15

        # 把 nib 的 bit3..bit0 直接布到 P 后的 32 位位置
        val = 0
        if (nib >> 3) & 1:
            val |= 1 << (31 - dst[0])
        if (nib >> 2) & 1:
            val |= 1 << (31 - dst[1])
        if (nib >> 1) & 1:
            val |= 1 << (31 - dst[2])
        if nib & 1:
            val |= 1 << (31 - dst[3])
        table[v] = val
    _SP_BOXES.append(table)


# -------------------- 基础工具 --------------------
def _permute64(x: int, table: list[int]) -> int:
    """对 64 位整数 x 按 table 做置换（table 元素 0..63，0 表示原 MSB）。"""
    y = 0
    for src in table:
        y = (y << 1) | ((x >> (63 - src)) & 1)
    return y


def _pack_block4(s: str) -> int:
    """最多4字符，按 16bit/字符打包成 64 位整数（不足补 '\0'）。"""
    s = s + '\0' * (4 - len(s))
    v = 0
    for i in range(4):
        v = (v << 16) | (ord(s[i]) & 0xFFFF)
    return v


def _u64_to_hex16(x: int) -> str:
    """64 位整数 -> 16 个大写十六进制字符"""
    return f"{x & 0xFFFFFFFFFFFFFFFF:016X}"


def _str_to_keyblocks(key: str) -> list[int]:
    """密钥字符串按 4 字符分块 -> 64 位整数列表"""
    if not key:
        return []
    n = (len(key) + 3) // 4
    return [_pack_block4(key[i*4:(i+1)*4]) for i in range(n)]


# -------------------- 轮密钥生成（保持原逻辑位序） --------------------
def _generate_round_keys_from_bits(keyBits: list[int]) -> list[list[int]]:
    """
    输入：64位bit列表（index0 为 MSB），输出：
    List[16]，每轮一个 List[8] 的 6bit 子密钥（int: 0..63），与 E 组配对使用。
    """
    # 原实现 PC1 逻辑：生成 56 位 key（列优先取位）
    key56 = [0] * 56
    for i in range(7):
        base_dest = i * 8
        for j in range(8):
            key56[base_dest + j] = keyBits[8 * (7 - j) + i]

    # 拆成 C、D 各 28 位，并做循环左移
    C = key56[:28]
    D = key56[28:56]

    c_off = 0
    d_off = 0

    keys = [[0] * 8 for _ in range(16)]
    loop = LOOP_TABLE
    pc2_groups = _PC2_GROUPS

    for rnd in range(16):
        shift = loop[rnd]
        c_off = (c_off + shift) % 28
        d_off = (d_off + shift) % 28

        gk = keys[rnd]
        for m in range(8):
            v = 0
            grp = pc2_groups[m]
            for k in range(6):
                idx = grp[k]
                if idx < 28:
                    b = C[(idx + c_off) % 28]
                else:
                    b = D[(idx - 28 + d_off) % 28]
                v = (v << 1) | b
            gk[m] = v
    return keys


def _generate_round_keys_from_u64(key64: int) -> list[list[int]]:
    """64 位整数密钥 -> 16 轮 × 8 组 6bit 子密钥"""
    keyBits = [ (key64 >> (63 - i)) & 1 for i in range(64) ]
    return _generate_round_keys_from_bits(keyBits)


# -------------------- 单块加密（使用整数与 SP 表） --------------------
def _enc_block_with_round_keys_u64(block64: int, round_keys: list[list[int]]) -> int:
    """
    使用已预计算的 16 轮子密钥对 64 位块加密。
    round_keys: List[16]，每项为 List[8]（8 个 6bit 子密钥）。
    """
    # IP
    ip = _permute64(block64, IP_TABLE)
    L = (ip >> 32) & 0xFFFFFFFF
    R = ip & 0xFFFFFFFF

    Eshifts = _E_SHIFTS
    SP = _SP_BOXES

    # 16 轮 Feistel
    for rnd in range(16):
        rk = round_keys[rnd]
        f = 0
        # 8 组 E->S->P
        # 按 E 表的位置取 6 位（按 bit5..bit0 顺序），与该组子密钥异或后做 SP 查表
        for m in range(8):
            sh = Eshifts[m]
            v = ((R >> sh[0]) & 1) << 5
            v |= ((R >> sh[1]) & 1) << 4
            v |= ((R >> sh[2]) & 1) << 3
            v |= ((R >> sh[3]) & 1) << 2
            v |= ((R >> sh[4]) & 1) << 1
            v |= ((R >> sh[5]) & 1)
            f ^= SP[m][v ^ rk[m]]
        # 轮换
        L, R = R, (L ^ f) & 0xFFFFFFFF

    # 拼接为 R||L 后做 FP
    preout = ((R & 0xFFFFFFFF) << 32) | (L & 0xFFFFFFFF)
    return _permute64(preout, FP_TABLE)


# -------------------- 对外 API --------------------
def strEnc(data: str, firstKey: str, secondKey: str, thirdKey: str) -> str:
    """
    DES 加密（按题述的“自定义”多密钥分层方式；未实现解密）
    data: 任意长度字符串；每 4 个字符为一块，不足补 '\0'
    每个 key 按 4 字符分块，依次对数据块进行再次加密
    返回：每块 16 个十六进制大写字符拼接的字符串
    """
    leng = len(data)
    if leng == 0:
        return ''

    # 预计算每个密钥串的所有“4字符块”的 16 轮子密钥
    keys_rounds_list = []
    for k in (firstKey, secondKey, thirdKey):
        if k:
            key_blocks = _str_to_keyblocks(k)  # List[u64]
            rounds_for_blocks = [_generate_round_keys_from_u64(b) for b in key_blocks]
            keys_rounds_list.append(rounds_for_blocks)
        else:
            keys_rounds_list.append(None)

    def encrypt_block_u64(b64: int) -> int:
        tmp = b64
        for round_keys_blocks in keys_rounds_list:
            if round_keys_blocks is None:
                continue
            for rk in round_keys_blocks:
                tmp = _enc_block_with_round_keys_u64(tmp, rk)
        return tmp

    # 分块处理：每 4 个字符 -> 64 位
    out_hex = []
    block_size = 4
    full_blocks = leng // block_size
    remainder = leng % block_size

    for i in range(full_blocks):
        blk = data[i*block_size:(i+1)*block_size]
        b64 = _pack_block4(blk)
        c64 = encrypt_block_u64(b64)
        out_hex.append(_u64_to_hex16(c64))

    if remainder:
        blk = data[full_blocks*block_size:]
        b64 = _pack_block4(blk)
        c64 = encrypt_block_u64(b64)
        out_hex.append(_u64_to_hex16(c64))

    return ''.join(out_hex)


# 如需简单自测，可使用如下示例：
# if __name__ == '__main__':
#     print(strEnc("HelloWorld", "KeyOne", "KeyTwo", "KeyThree"))
#     print(strEnc("ABC", "K", None, None))
