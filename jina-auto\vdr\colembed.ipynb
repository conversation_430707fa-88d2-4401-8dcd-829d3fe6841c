{"cells": [{"cell_type": "markdown", "id": "5ba8c7e2", "metadata": {}, "source": ["https://huggingface.co/nvidia/llama-nemoretriever-colembed-3b-v1\n", "\n", "7/12 9:33"]}, {"cell_type": "code", "execution_count": null, "id": "4aad72d3", "metadata": {}, "outputs": [], "source": ["%conda install pytorch==2.4.0 torchvision==0.19.0 torchaudio==2.4.0 pytorch-cuda=12.4 -c pytorch -c nvidia\n", "%pip install torch==2.4.0 torchvision==0.19.0 torchaudio==2.4.0 --index-url https://download.pytorch.org/whl/cu124\n", "\n", "%pip install transformers==4.49.0"]}, {"cell_type": "code", "execution_count": 21, "id": "c5d38d5b", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'source' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[21]\u001b[39m\u001b[32m, line 9\u001b[39m\n\u001b[32m      5\u001b[39m os.environ[\u001b[33m'\u001b[39m\u001b[33mHF_ENDPOINT\u001b[39m\u001b[33m'\u001b[39m] = \u001b[33m'\u001b[39m\u001b[33mhttps://hf-mirror.com\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m      8\u001b[39m \u001b[38;5;66;03m# github\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m \u001b[43msource\u001b[49m /etc/network_turbo\n", "\u001b[31mNameError\u001b[39m: name 'source' is not defined"]}], "source": ["# autodl: proxy\n", "# hf\n", "import os\n", "\n", "os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'\n", "\n", "\n", "# github\n", "source /etc/network_turbo"]}, {"cell_type": "markdown", "id": "5444d6c4", "metadata": {}, "source": ["**PROXY**\n", "\n", "```sh\n", "wget https://github.com/MetaCubeX/mihomo/releases/download/v1.19.11/mihomo-linux-amd64-v1.19.11.deb\n", "sudo dpkg -i mihomo-linux-amd64-v1.19.11.deb\n", "\n", "sudo vim /etc/mihomo/config.json\n", "```"]}, {"cell_type": "markdown", "id": "959d0504", "metadata": {}, "source": ["**DO NOT USE PIP**\n", "\n", "In terms of `flash_attn-2.6.3+cu123torch2.4cxx11abiFALSE-cp311-cp311-linux_x86_64.whl`:\n", "- CUDA 12.8 (higher than 12.3), PyTorch 2.4, Python 3.11\n", "- whl file from:\n", "  - win: https://github.com/kingbri1/flash-attention/releases\n", "  - linux: https://github.com/Dao-AILab/flash-attention/releases\n", "  - more: https://github.com/mjun0812/flash-attention-prebuild-wheels/releases\n", "- [Pytorch Versions](https://pytorch.org/get-started/previous-versions/)"]}, {"cell_type": "code", "execution_count": null, "id": "e8cc440d", "metadata": {}, "outputs": [], "source": ["%pip install --no-dependencies \\\n", "  https://github.com/kingbri1/flash-attention/releases/download/v2.6.3/flash_attn-2.6.3+cu123torch2.4cxx11abiFALSE-cp311-cp311-linux_x86_64.whl\n", "\n", "# autodl: PyTorch / 2.3.0 / 3.12(ubuntu22.04) / 12.1\n", "# https://github.com/Dao-AILab/flash-attention/releases/download/v2.6.3/flash_attn-2.6.3+cu123torch2.3cxx11abiFALSE-cp312-cp312-linux_x86_64.whl"]}, {"cell_type": "code", "execution_count": null, "id": "735a7aec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: einops in c:\\users\\<USER>\\miniforge3\\envs\\vdr-colembed\\lib\\site-packages (0.8.1)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR: Invalid requirement: \"'accelerate\": Expected package name at the start of dependency specifier\n", "    'accelerate\n", "    ^\n"]}], "source": ["%pip install einops\n", "%pip install \"accelerate>=0.26.0\"\n", "%pip install ipywidgets\n", "%pip install datasets\n", "%pip install peft"]}, {"cell_type": "code", "execution_count": 1, "id": "8f175c41", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ['https_proxy'] = 'http://localhost:7897'"]}, {"cell_type": "code", "execution_count": 2, "id": "54bab996", "metadata": {}, "outputs": [], "source": ["import requests\n", "from PIL import Image\n", "from io import BytesIO\n", "import torch\n", "from transformers import AutoModel"]}, {"cell_type": "code", "execution_count": 3, "id": "8e44923d", "metadata": {}, "outputs": [], "source": ["# Load Model\n", "model = AutoModel.from_pretrained(\n", "    'nvidia/llama-nemoretriever-colembed-1b-v1',\n", "    device_map='cuda',\n", "    trust_remote_code=True,\n", "    torch_dtype=torch.bfloat16,\n", "    attn_implementation=\"flash_attention_2\",\n", "    revision='1f0fdea7f5b19532a750be109b19072d719b8177'\n", ").eval()"]}, {"cell_type": "code", "execution_count": 4, "id": "6f115a69", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bf5742f695764c7397a426521bba9024", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniforge3\\envs\\vdr-colembed\\Lib\\site-packages\\huggingface_hub\\file_download.py:143: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--nvidia--llama-nemoretriever-colembed-3b-v1. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.\n", "To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development\n", "  warnings.warn(message)\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "00911973f1ec4d40a96f08da82179c24", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/17.2M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "68994efcab0344dc990c51d641c3e5de", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/454 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2c068cf9b796428ca2726d037dc137a4", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load Model\n", "model = AutoModel.from_pretrained(\n", "    'nvidia/llama-nemoretriever-colembed-3b-v1',\n", "    use_safetensors=True,\n", "    device_map='cuda',\n", "    trust_remote_code=True,\n", "    torch_dtype=torch.bfloat16,\n", "    attn_implementation=\"flash_attention_2\",\n", "    revision='50c36f4d5271c6851aa08bd26d69f6e7ca8b870c'\n", ").eval()"]}, {"cell_type": "code", "execution_count": 4, "id": "64b7e7a6", "metadata": {}, "outputs": [], "source": ["# Queries\n", "queries = [\n", "    'How much percentage of Germanys population died in the 2nd World War?',\n", "    'How many million tons CO2 were captured from Gas processing in 2018?',\n", "    'What is the average CO2 emission of someone in Japan?'\n", "]\n", "\n", "# Documents\n", "image_urls = [\n", "    'https://wiki-upload.yayeah.xyz/wikipedia/commons/3/35/Human_losses_of_world_war_two_by_country.png',\n", "    'https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/7/76/20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg/2560px-20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg.png',\n", "    'https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/f/f3/20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg/2880px-20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg.png'\n", "]\n", "\n", "# Load into PIL\n", "headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64)\"\n", "}\n", "\n", "images = [Image.open(BytesIO(requests.get(image_url, headers=headers).content)) for image_url in image_urls]"]}, {"cell_type": "code", "execution_count": 5, "id": "ef3550e6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Extracting query embeddings...:   0%|          | 0/3 [00:52<?, ?it/s]\n"]}, {"ename": "AssertionError", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAsser<PERSON>Error\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m query_embeddings = \u001b[43mmodel\u001b[49m\u001b[43m.\u001b[49m\u001b[43mforward_queries\u001b[49m\u001b[43m(\u001b[49m\u001b[43mqueries\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      3\u001b[39m passage_embeddings = model.forward_passages(images, batch_size=\u001b[32m1\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\.cache\\huggingface\\modules\\transformers_modules\\nvidia\\llama-nemoretriever-colembed-1b-v1\\1f0fdea7f5b19532a750be109b19072d719b8177\\modeling_llama_nemoretrievercolembed.py:458\u001b[39m, in \u001b[36mllama_NemoRetrieverColEmbed.forward_queries\u001b[39m\u001b[34m(self, queries, batch_size)\u001b[39m\n\u001b[32m    448\u001b[39m dataset = ListDataset[\u001b[38;5;28mstr\u001b[39m](queries)\n\u001b[32m    449\u001b[39m dataloader = DataLoader(\n\u001b[32m    450\u001b[39m     dataset=dataset,\n\u001b[32m    451\u001b[39m     batch_size=batch_size,\n\u001b[32m   (...)\u001b[39m\u001b[32m    456\u001b[39m     drop_last=\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m    457\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m458\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_extract_embeddings\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdataloader\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdataloader\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mis_query\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\.cache\\huggingface\\modules\\transformers_modules\\nvidia\\llama-nemoretriever-colembed-1b-v1\\1f0fdea7f5b19532a750be109b19072d719b8177\\modeling_llama_nemoretrievercolembed.py:430\u001b[39m, in \u001b[36mllama_NemoRetrieverColEmbed._extract_embeddings\u001b[39m\u001b[34m(self, dataloader, is_query)\u001b[39m\n\u001b[32m    427\u001b[39m             embeddings = F.normalize(embeddings, dim=-\u001b[32m1\u001b[39m)\n\u001b[32m    429\u001b[39m     \u001b[38;5;66;03m# Detecting abnormal outputs\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m430\u001b[39m     \u001b[38;5;28;01massert\u001b[39;00m torch.sum(embeddings).float().item() \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;28mfloat\u001b[39m(\u001b[32m0.\u001b[39m), \u001b[38;5;28mfloat\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33minf\u001b[39m\u001b[33m\"\u001b[39m)]\n\u001b[32m    431\u001b[39m     qs.append(embeddings.contiguous())\n\u001b[32m    433\u001b[39m qs_tensor = \u001b[38;5;28mself\u001b[39m.padding_various_shape_tensor(qs)\n", "\u001b[31mAssertionError\u001b[39m: "]}], "source": ["# Encoding\n", "query_embeddings = model.forward_queries(queries, batch_size=1)\n", "passage_embeddings = model.forward_passages(images, batch_size=1)"]}, {"cell_type": "code", "execution_count": null, "id": "926591be", "metadata": {}, "outputs": [], "source": ["scores = model.get_scores(\n", "    query_embeddings,\n", "    passage_embeddings\n", ")\n", "# Diagonal should have high scores\n", "print(scores)\n", "# tensor([[13.9970, 11.4219, 12.1225],\n", "#         [11.4157, 14.6388, 12.0341],\n", "#         [ 9.9023,  9.8857, 11.3387]], device='cuda:0')\n"]}], "metadata": {"kernelspec": {"display_name": "vdr-colembed", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}