import requests
import re
import base64
import hashlib
from urllib.parse import quote_plus

# DES加密算法实现 - 完全复制Rust代码逻辑
class DESEncrypt:
    # S盒定义 - 直接从Rust代码复制
    S_BOXES = [
        # S1
        [[14, 4, 13, 1, 2, 15, 11, 8, 3, 10, 6, 12, 5, 9, 0, 7],
         [0, 15, 7, 4, 14, 2, 13, 1, 10, 6, 12, 11, 9, 5, 3, 8],
         [4, 1, 14, 8, 13, 6, 2, 11, 15, 12, 9, 7, 3, 10, 5, 0],
         [15, 12, 8, 2, 4, 9, 1, 7, 5, 11, 3, 14, 10, 0, 6, 13]],
        # S2
        [[15, 1, 8, 14, 6, 11, 3, 4, 9, 7, 2, 13, 12, 0, 5, 10],
         [3, 13, 4, 7, 15, 2, 8, 14, 12, 0, 1, 10, 6, 9, 11, 5],
         [0, 14, 7, 11, 10, 4, 13, 1, 5, 8, 12, 6, 9, 3, 2, 15],
         [13, 8, 10, 1, 3, 15, 4, 2, 11, 6, 7, 12, 0, 5, 14, 9]],
        # S3
        [[10, 0, 9, 14, 6, 3, 15, 5, 1, 13, 12, 7, 11, 4, 2, 8],
         [13, 7, 0, 9, 3, 4, 6, 10, 2, 8, 5, 14, 12, 11, 15, 1],
         [13, 6, 4, 9, 8, 15, 3, 0, 11, 1, 2, 12, 5, 10, 14, 7],
         [1, 10, 13, 0, 6, 9, 8, 7, 4, 15, 14, 3, 11, 5, 2, 12]],
        # S4
        [[7, 13, 14, 3, 0, 6, 9, 10, 1, 2, 8, 5, 11, 12, 4, 15],
         [13, 8, 11, 5, 6, 15, 0, 3, 4, 7, 2, 12, 1, 10, 14, 9],
         [10, 6, 9, 0, 12, 11, 7, 13, 15, 1, 3, 14, 5, 2, 8, 4],
         [3, 15, 0, 6, 10, 1, 13, 8, 9, 4, 5, 11, 12, 7, 2, 14]],
        # S5
        [[2, 12, 4, 1, 7, 10, 11, 6, 8, 5, 3, 15, 13, 0, 14, 9],
         [14, 11, 2, 12, 4, 7, 13, 1, 5, 0, 15, 10, 3, 9, 8, 6],
         [4, 2, 1, 11, 10, 13, 7, 8, 15, 9, 12, 5, 6, 3, 0, 14],
         [11, 8, 12, 7, 1, 14, 2, 13, 6, 15, 0, 9, 10, 4, 5, 3]],
        # S6
        [[12, 1, 10, 15, 9, 2, 6, 8, 0, 13, 3, 4, 14, 7, 5, 11],
         [10, 15, 4, 2, 7, 12, 9, 5, 6, 1, 13, 14, 0, 11, 3, 8],
         [9, 14, 15, 5, 2, 8, 12, 3, 7, 0, 4, 10, 1, 13, 11, 6],
         [4, 3, 2, 12, 9, 5, 15, 10, 11, 14, 1, 7, 6, 0, 8, 13]],
        # S7
        [[4, 11, 2, 14, 15, 0, 8, 13, 3, 12, 9, 7, 5, 10, 6, 1],
         [13, 0, 11, 7, 4, 9, 1, 10, 14, 3, 5, 12, 2, 15, 8, 6],
         [1, 4, 11, 13, 12, 3, 7, 14, 10, 15, 6, 8, 0, 5, 9, 2],
         [6, 11, 13, 8, 1, 4, 10, 7, 9, 5, 0, 15, 14, 2, 3, 12]],
        # S8
        [[13, 2, 8, 4, 6, 15, 11, 1, 10, 9, 3, 14, 5, 0, 12, 7],
         [1, 15, 13, 8, 10, 3, 7, 4, 12, 5, 6, 11, 0, 14, 9, 2],
         [7, 11, 4, 1, 9, 12, 14, 2, 0, 6, 10, 13, 15, 3, 5, 8],
         [2, 1, 14, 7, 4, 10, 8, 13, 15, 12, 9, 0, 3, 5, 6, 11]]
    ]
    
    # P盒置换表
    P_BOX_PERMUTE = [
        15, 6, 19, 20, 28, 11, 27, 16,
        0, 14, 22, 25, 4, 17, 30, 9,
        1, 7, 23, 13, 31, 26, 2, 8,
        18, 12, 29, 5, 21, 10, 3, 24
    ]
    
    # 最终置换表
    FP_PERMUTE = [
        39, 7, 47, 15, 55, 23, 63, 31,
        38, 6, 46, 14, 54, 22, 62, 30,
        37, 5, 45, 13, 53, 21, 61, 29,
        36, 4, 44, 12, 52, 20, 60, 28,
        35, 3, 43, 11, 51, 19, 59, 27,
        34, 2, 42, 10, 50, 18, 58, 26,
        33, 1, 41, 9, 49, 17, 57, 25,
        32, 0, 40, 8, 48, 16, 56, 24
    ]
    
    @staticmethod
    def generate_keys(key_bytes):
        """生成16轮子密钥"""
        key = [0] * 56
        keys = [[0] * 48 for _ in range(16)]
        loop_shifts = [1, 1, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 1]
        
        # 初始化密钥数组
        for i in range(7):
            for j in range(8):
                key[i * 8 + j] = key_bytes[(7 - j) * 8 + i]
        
        # 生成16轮密钥
        for i in range(16):
            for _ in range(loop_shifts[i]):
                temp_left = key[0]
                temp_right = key[28]
                for k in range(27):
                    key[k] = key[k + 1]
                    key[28 + k] = key[29 + k]
                key[27] = temp_left
                key[55] = temp_right
            
            temp_key = [
                key[13], key[16], key[10], key[23], key[0], key[4], key[2], key[27],
                key[14], key[5], key[20], key[9], key[22], key[18], key[11], key[3],
                key[25], key[7], key[15], key[6], key[26], key[19], key[12], key[1],
                key[40], key[51], key[30], key[36], key[46], key[54], key[29], key[39],
                key[50], key[44], key[32], key[47], key[43], key[48], key[38], key[55],
                key[33], key[52], key[45], key[41], key[49], key[35], key[28], key[31]
            ]
            
            keys[i] = temp_key[:]
        
        return keys
    
    @staticmethod
    def str_to_bt(s):
        """字符串转为64位二进制数组"""
        leng = len(s)
        bt = [0] * 64
        
        if leng < 4:
            for i, c in enumerate(s):
                k = ord(c)
                for j in range(16):
                    pow_val = 1 << (15 - j)
                    bt[16 * i + j] = (k // pow_val) % 2
            
            for p in range(leng, 4):
                k = 0
                for q in range(16):
                    pow_val = 1 << (15 - q)
                    bt[16 * p + q] = (k // pow_val) % 2
        else:
            for i, c in enumerate(s[:4]):
                k = ord(c)
                for j in range(16):
                    pow_val = 1 << (15 - j)
                    bt[16 * i + j] = (k // pow_val) % 2
        
        return bt
    
    @staticmethod
    def get_key_bytes(key):
        """获取密钥字节数组"""
        leng = len(key)
        iterator = leng // 4
        remainder = leng % 4
        key_bytes = []
        
        for i in range(iterator):
            key_bytes.append(DESEncrypt.str_to_bt(key[i * 4:i * 4 + 4]))
        
        if remainder > 0:
            key_bytes.append(DESEncrypt.str_to_bt(key[iterator * 4:]))
        
        return key_bytes
    
    @staticmethod
    def init_permute(original_data):
        """初始置换"""
        ip_byte = [0] * 64
        
        for i in range(4):
            for j in range(8):
                ip_byte[i * 8 + j] = original_data[(7 - j) * 8 + 2 * i + 1]
                ip_byte[i * 8 + j + 32] = original_data[(7 - j) * 8 + 2 * i]
        
        return ip_byte
    
    @staticmethod
    def expand_permute(right_data):
        """E扩展置换"""
        ep_byte = [0] * 48
        
        for i in range(8):
            ep_byte[i * 6] = right_data[31] if i == 0 else right_data[i * 4 - 1]
            ep_byte[i * 6 + 1] = right_data[i * 4]
            ep_byte[i * 6 + 2] = right_data[i * 4 + 1]
            ep_byte[i * 6 + 3] = right_data[i * 4 + 2]
            ep_byte[i * 6 + 4] = right_data[i * 4 + 3]
            ep_byte[i * 6 + 5] = right_data[0] if i == 7 else right_data[i * 4 + 4]
        
        return ep_byte
    
    @staticmethod
    def xor(byte_one, byte_two):
        """异或运算"""
        return [a ^ b for a, b in zip(byte_one, byte_two)]
    
    @staticmethod
    def get_box_binary(i):
        """获取4位二进制字符串"""
        binary_values = [
            "0000", "0001", "0010", "0011",
            "0100", "0101", "0110", "0111",
            "1000", "1001", "1010", "1011",
            "1100", "1101", "1110", "1111"
        ]
        return binary_values[i]
    
    @staticmethod
    def s_box_permute(expand_byte):
        """S盒置换"""
        s_box_byte = []
        
        for m in range(8):
            i = expand_byte[m * 6] * 2 + expand_byte[m * 6 + 5]
            j = (expand_byte[m * 6 + 1] * 8 + 
                 expand_byte[m * 6 + 2] * 4 + 
                 expand_byte[m * 6 + 3] * 2 + 
                 expand_byte[m * 6 + 4])
            binary = DESEncrypt.get_box_binary(DESEncrypt.S_BOXES[m][i][j])
            
            for char in binary:
                s_box_byte.append(0 if char == '0' else 1)
        
        return s_box_byte
    
    @staticmethod
    def p_permute(s_box_byte):
        """P置换"""
        p_box_permute = [0] * 32
        for i, index in enumerate(DESEncrypt.P_BOX_PERMUTE):
            p_box_permute[i] = s_box_byte[index]
        return p_box_permute
    
    @staticmethod
    def finally_permute(end_byte):
        """最终置换"""
        fp_byte = [0] * 64
        for i, index in enumerate(DESEncrypt.FP_PERMUTE):
            fp_byte[i] = end_byte[index]
        return fp_byte
    
    @staticmethod
    def enc(data_byte, key_byte):
        """DES加密核心函数"""
        keys = DESEncrypt.generate_keys(key_byte)
        ip_byte = DESEncrypt.init_permute(data_byte)
        
        ip_left = ip_byte[:32]
        ip_right = ip_byte[32:]
        
        for i in range(16):
            temp_left = ip_left[:]
            ip_left = ip_right[:]
            key = keys[i]
            temp_right = DESEncrypt.xor(
                DESEncrypt.p_permute(
                    DESEncrypt.s_box_permute(
                        DESEncrypt.xor(
                            DESEncrypt.expand_permute(ip_right), 
                            key
                        )
                    )
                ), 
                temp_left
            )
            ip_right = temp_right
        
        final_data = ip_right + ip_left
        return DESEncrypt.finally_permute(final_data)
    
    @staticmethod
    def bt4_to_hex(binary):
        """4位二进制转16进制"""
        hex_values = "0123456789ABCDEF"
        index = int(binary, 2)
        return hex_values[index]
    
    @staticmethod
    def bt64_to_hex(byte_data):
        """64位二进制数组转16进制字符串"""
        hex_str = ""
        for i in range(16):
            bt = "".join(str(byte_data[i * 4 + j]) for j in range(4))
            hex_str += DESEncrypt.bt4_to_hex(bt)
        return hex_str
    
    @staticmethod
    def str_enc(data, first_key="", second_key="", third_key=""):
        """完整的str_enc函数 - 与Rust版本完全一致"""
        leng = len(data)
        enc_data = ""
        
        first_key_bt = DESEncrypt.get_key_bytes(first_key) if first_key else []
        second_key_bt = DESEncrypt.get_key_bytes(second_key) if second_key else []
        third_key_bt = DESEncrypt.get_key_bytes(third_key) if third_key else []
        
        first_length = len(first_key_bt)
        second_length = len(second_key_bt)
        third_length = len(third_key_bt)
        
        if leng == 0:
            return enc_data
        
        def enc_block(block):
            temp_bt = DESEncrypt.str_to_bt(block)
            
            if first_key:
                for x in range(first_length):
                    temp_bt = DESEncrypt.enc(temp_bt, first_key_bt[x])
            
            if second_key:
                for y in range(second_length):
                    temp_bt = DESEncrypt.enc(temp_bt, second_key_bt[y])
            
            if third_key:
                for z in range(third_length):
                    temp_bt = DESEncrypt.enc(temp_bt, third_key_bt[z])
            
            return temp_bt
        
        if leng < 4:
            enc_data = DESEncrypt.bt64_to_hex(enc_block(data))
        else:
            iterator = leng // 4
            remainder = leng % 4
            
            for i in range(iterator):
                temp_data = data[i * 4:i * 4 + 4]
                enc_data += DESEncrypt.bt64_to_hex(enc_block(temp_data))
            
            if remainder > 0:
                remainder_data = data[iterator * 4:]
                enc_data += DESEncrypt.bt64_to_hex(enc_block(remainder_data))
        
        return enc_data

