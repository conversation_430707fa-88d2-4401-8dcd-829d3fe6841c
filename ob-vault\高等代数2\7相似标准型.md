### § 7.1 多项式矩阵

我们将在这一章里继续探讨上一章中提出的问题：给定一个线性变换，找出一组基，使该线性变换在这组基下的表示矩阵具有比较简单的形状。这个问题等价于寻找一类比较简单的矩阵，使任一同阶方阵均与这类矩阵中的某一个相似。这类比较简单的矩阵就是所谓的相似标准型。

为了解决这个问题，可以分两步走。第一步找出相似矩阵的不变量，这些不变量不仅在相似关系下保持不变，而且是以判断两个矩阵是否相似。我们称这样的不变量为全系不变量。比如秩是两个同阶矩阵在相抵关系下的不变量，反之，若两个同阶矩阵的秩相同，则它们必相抵。因此，秩是矩阵相抵关系的全系不变量。第二步找出一类比较简单的矩阵，利用相似关系的全系不变量就可以判断一个矩阵与这类矩阵中的某一个相似。

相似关系比相抵关系要复杂一些，它的全系不变量也比较复杂。我们在上一章中已经知道，矩阵的特征多项式（从而特征值）是相似不变量，但它并不是全系不变量。因为我们很容易举出例子来证明这一点。比如下面两个矩阵的特征多项式相同但不相似：

$$A=\begin{pmatrix} 0 & 1 \\ 0 & 0 \end{pmatrix}，B=\begin{pmatrix} 0 & 0 \\ 0 & 0 \end{pmatrix}。$$

$A$的特征多项式与$B$的特征多项式都是$λ^2$，但$A$与$B$绝不相似。

人们经过研究终于发现，两个矩阵$A$与$B$之间的相似和$λI_n-A$与$λI_n-B$的相抵有着密切的联系。注意，$λI_n-A$是这样形式的矩阵：

$$\begin{pmatrix} λ-a_{11} & -a_{12} & \cdots & -a_{1n} \\ -a_{21} & λ-a_{22} & \cdots & -a_{2n} \\ \vdots & \vdots & & \vdots \\ -a_{n1} & -a_{n2} & \cdots & λ-a_{nn} \end{pmatrix}，$$

其中的元素含有未定元$λ$。一般地，下列形式的矩阵：

$$A(λ)=\begin{pmatrix} a_{11}(λ) & a_{12}(λ) & \cdots & a_{1n}(λ) \\ a_{21}(λ) & a_{22}(λ) & \cdots & a_{2n}(λ) \\ \vdots & \vdots & & \vdots \\ a_{m1}(λ) & a_{m2}(λ) & \cdots & a_{mn}(λ) \end{pmatrix}，$$

其中$a_{ij}(λ)$是以$λ$为未定元的数域$K$上的多项式，称为多项式矩阵，或$λ$-矩阵。$λ$-矩阵的加法、数乘及乘法与数域上的矩阵运算一样，只需在运算过程中将数的运算代之以多项式运算即可。

现在我们来研究两个$λ$-矩阵的相抵关系。首先我们必须定义什么叫$λ$-矩阵的初等变换。

**定义 7.1.1** 对$λ$-矩阵$A(λ)$施行的下列$3$种变换称为$λ$-矩阵的初等行变换：

(1) 将$A(λ)$的两行对换；
(2) 将$A(λ)$的第$i$行乘以$K$中的非零常数$c$;
(3) 将$A(λ)$的第$i$行乘以$K$上的多项式$f(λ)$后加到第$j$行上去。

同理我们可以定义$3$种$λ$-矩阵的初等列变换.

**定义 7.1.2** 若$A(λ)$，$B(λ)$是同阶$λ$-矩阵且$A(λ)$经过$λ$-矩阵的初等变换后可变为$B(λ)$，则称$A(λ)$与$B(λ)$相抵。

与数字矩阵一样，$λ$-矩阵的相抵关系也是一种等价关系，即
(1) $A(λ)$与自身相抵；
(2) 若$A(λ)$与$B(λ)$相抵，则$B(λ)$与$A(λ)$相抵；
(3) 若$A(λ)$与$B(λ)$相抵，$B(λ)$与$C(λ)$相抵，则$A(λ)$与$C(λ)$相抵。
证明与数域上相同，请读者自己完成。

类似数字矩阵，$λ$-矩阵的初等变换也对应于初等$λ$-矩阵的相乘。

**定义 7.1.3** 下列$3$种矩阵称为初等$λ$-矩阵：
(1) 将$n$阶单位阵的第$i$行与第$j$行对换，记为$P_{ij}$；
(2) 将$n$阶单位阵的第$i$行乘以非零常数$c$，记为$P_i(c)$；
(3) 将$n$阶单位阵的第$i$行乘以多项式$f(λ)$后加到第$j$行上去得到的矩阵，记为$T_{ij}(f(λ))$。

注意，第一类与第二类初等$λ$-矩阵与数域上的第一类与第二类初等矩阵没有什么区别。第三类初等$λ$-矩阵的形状如下：

$$T_{ij}(f(λ))=\begin{pmatrix} 1 & & & & \\ & \ddots & & & \\ & & 1 & & \\ & & \vdots & \ddots & \\ & & f(λ) & \cdots & 1 \\ & & & & \ddots \\ & & & & & 1 \end{pmatrix}。$$

**定理 7.1.1** 对$λ$-矩阵$A(λ)$施行第$k$$(k=1,2,3)$类初等行（列）变换等于用第$k$类初等$λ$-矩阵左（右）乘以$A(λ)$。

证明与定理$2.4.3$完全相同，留给读者作为练习.

注　下列$λ$-矩阵的变换不是$λ$-矩阵的初等变换：

$$\begin{pmatrix} 1 & 1 \\ 0 & 1 \end{pmatrix} \to \begin{pmatrix} λ & λ \\ 0 & 1 \end{pmatrix}。$$

这是因为前面一个矩阵的第一行乘以$λ$不是$λ$-矩阵的初等变换。同理下面的变换需第一行乘以$λ^{-1}$，因此也不是$λ$-矩阵的初等变换：

$$\begin{pmatrix} λ & 0 \\ 0 & 1 \end{pmatrix} \to \begin{pmatrix} 1 & 0 \\ 0 & 1 \end{pmatrix}。$$

对$n$阶$λ$-矩阵，我们可定义可逆$λ$-矩阵的概念.

**定义 7.1.4** 若$A(λ)$，$B(λ)$都是$n$阶$λ$-矩阵，且

$$A(λ)B(λ)=B(λ)A(λ)=I_n，$$

则称$B(λ)$是$A(λ)$的逆$λ$-矩阵。这时称$A(λ)$为可逆$λ$-矩阵，在不引起混淆的情形下，有时简称为可逆矩阵。

注　注意不要将数字矩阵中的一些结论随意搬到$λ$-矩阵上。比如下面的$λ$-矩阵行列式不为零，但它不是可逆$λ$-矩阵：

$$\begin{pmatrix} λ & 0 \\ 0 & 1 \end{pmatrix}。$$

这是因为矩阵

$$\begin{pmatrix} λ^{-1} & 0 \\ 0 & 1 \end{pmatrix}$$

不是$λ$-矩阵之故.

容易证明，有限个可逆$λ$-矩阵之积仍是可逆$λ$-矩阵，而初等$λ$-矩阵都是可逆$λ$-矩阵，因此有限个初等$λ$-矩阵之积也是可逆$λ$-矩阵。下一节我们将证明可逆$λ$-矩阵必可表示为有限个初等$λ$-矩阵之积。

为了把数域上矩阵的相似关系归结为$λ$-矩阵的相抵关系，我们需要证明一个有关$λ$-矩阵带余除法的引理。设$M(λ)$是一个$n$阶$λ$-矩阵，则$M(λ)$可以化为如下形状：

$$M(λ)=M_mλ^m+M_{m-1}λ^{m-1}+\cdots+M_0，$$

其中$M_i$为数域$K$上的$n$阶数字矩阵。因此，一个多项式矩阵可以化为系数为矩阵的多项式，反之亦然。

**引理 7.1.1** 设$M(λ)$与$N(λ)$是两个$n$阶$λ$-矩阵且都不等于零。又设$B$为$n$阶数字矩阵，则必存在$λ$-矩阵$Q(λ)$及$S(λ)$和数字矩阵$R$及$T$，使下式成立：

$$M(λ)=(λI-B)Q(λ)+R， \tag{7.1.1}$$

$$N(λ)=S(λ)(λI-B)+T。 \tag{7.1.2}$$

证明　将$M(λ)$写为

$$M(λ)=M_mλ^m+M_{m-1}λ^{m-1}+\cdots+M_0，$$

其中$M_m \neq O$。可对$m$用归纳法，若$m=0$，则已经合要求（取$Q(λ)=O$）。现设对小于$m$次的矩阵多项式，(7.1.1)式成立。令

$$Q_1(λ)=M_mλ^{m-1}，$$

则

$$M(λ)-(λI-B)Q_1(λ)=(BM_m+M_{m-1})λ^{m-1}+\cdots+M_0。 \tag{7.1.3}$$

上式是一个次数小于$m$的矩阵多项式，由归纳假设得

$$M(λ)-(λI-B)Q_1(λ)=(λI-B)Q_2(λ)+R。$$

于是

$$M(λ)=(λI-B)[Q_1(λ)+Q_2(λ)]+R。$$

令$Q(λ)=Q_1(λ)+Q_2(λ)$即得(7.1.1)式。同理可证(7.1.2)式。□

**定理 7.1.2** 设$A,B$是数域$K$上的矩阵，则$A$与$B$相似的充分必要条件是$λ$-矩阵$λI-A$与$λI-B$相抵。

证明　若$A$与$B$相似，则存在$K$上的非异阵$P$，使$B=P^{-1}AP$，于是

$$P^{-1}(λI-A)P=λI-P^{-1}AP=λI-B。 \tag{7.1.4}$$

把$P$看成是常数$λ$-矩阵，上式表明$λI-A$与$λI-B$相抵。

反过来，若$λI-A$与$λI-B$相抵，则存在$M(λ)$及$N(λ)$，使

$$M(λ)(λI-A)N(λ)=λI-B， \tag{7.1.5}$$

其中$M(λ)$与$N(λ)$都是有限个初等矩阵之积，因而都是可逆矩阵。因此可将(7.1.5)式写为

$$M(λ)(λI-A)=(λI-B)N(λ)^{-1}， \tag{7.1.6}$$

由引理7.1.1可设

$$M(λ)=(λI-B)Q(λ)+R， \tag{7.1.7}$$

代入(7.1.6)式经整理得

$$R(λI-A)=(λI-B)[N(λ)^{-1}-Q(λ)(λI-A)]。 \tag{7.1.8}$$

上式左边是次数小于等于$1$的矩阵多项式，因此上式右边中括号内的矩阵多项式的次数必须小于等于零，也即必是一个常数矩阵，设为$P$。于是

$$R(λI-A)=(λI-B)P。 \tag{7.1.9}$$

(7.1.9)式又可整理为

$$(R-P)λ=RA-BP。$$

再次比较次数得$R=P$，$RA=BP$。现只需证明$P$是一个非异阵即可。由假设

$$P=N(λ)^{-1}-Q(λ)(λI-A),$$

将上式两边右乘$N(λ)$并移项得

$$PN(λ)+Q(λ)(λI-A)N(λ)=I。$$

但

$$(λI-A)N(λ)=M(λ)^{-1}(λI-B),$$

因此

$$PN(λ)+Q(λ)M(λ)^{-1}(λI-B)=I。 \tag{7.1.10}$$

再由引理 7.1.1 可设

$$N(λ)=S(λ)(λI-B)+T,$$

代入(7.1.10)式并整理得

$$[PS(λ)+Q(λ)M(λ)^{-1}](λI-B)=I-PT。$$

上式右边是次数小于等于零的矩阵多项式，因此上式左边中括号内的矩阵多项式必须为零，从而$PT=I$，即$P$是非异阵。□

### 习题 7.1

1. 设$M(λ)$是$λ$-矩阵且可写为

$$M(λ)=M_mλ^m+M_{m-1}λ^{m-1}+\cdots+M_0。$$

求证：若$M(λ)$是可逆$λ$-矩阵，则$M_0$是非异阵。

2. 证明引理 7.1.1 中的余式$R$及$T$是唯一确定的，$Q(λ)$与$S(λ)$也唯一确定。

3. 设$A,B$是数域$K$上的$n$阶矩阵，求证：它们的特征矩阵$λI_n-A$和$λI_n-B$相抵的充分必要条件是存在同阶矩阵$P$和$Q$，使$A=PQ$，$B=QP$，且$P$及$Q$中至少有一个是可逆矩阵。

4. 设$A,B$是数域$K$上的$n$阶矩阵，$λI_n-A$相抵于diag{$f_1(λ),f_2(λ),\cdots,f_n(λ)$}，$λI_n-B$相抵于diag{$f_n(λ),f_{n-1}(λ),\cdots,f_1(λ)$}，其中$f_n(λ),f_{n-1}(λ),\cdots,f_1(λ)$是$f_1(λ),f_2(λ),\cdots,f_n(λ)$的一个排列。求证：$A$与$B$相似。

### § 7.2 矩阵的法式

在上一节中，我们把矩阵的相似归结为$λ$-矩阵的相抵。现在我们来求$λ$-矩阵的相抵标准型。我们自然地希望任一$λ$-矩阵相抵于一个对角$λ$-矩阵。