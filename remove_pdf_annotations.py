#!/usr/bin/env python3
"""
PDF批注去除工具
用于去除GoodNotes或其他应用添加的PDF批注，获得原始PDF内容

基于PyMuPDF (fitz) 库实现
参考资料：
- https://stackoverflow.com/questions/61947880/pymupdf-how-do-i-remove-annotations
- https://github.com/pymupdf/PyMuPDF/discussions/2825
"""

import fitz  # PyMuPDF
import sys
import os
from pathlib import Path


def remove_annotations_from_page(page):
    """
    从单个页面中移除所有批注
    
    Args:
        page: PyMuPDF页面对象
    
    Returns:
        int: 移除的批注数量
    """
    removed_count = 0
    
    # 使用安全的迭代方式删除批注
    # 避免在迭代过程中修改集合导致的问题
    annot = page.first_annot
    while annot:
        annot_type = annot.type[0]
        annot_type_name = annot.type[1]
        
        print(f"  发现批注类型: {annot_type_name} (ID: {annot_type})")
        
        # 删除批注并获取下一个批注
        # delete_annot方法会返回下一个批注或None
        annot = page.delete_annot(annot)
        removed_count += 1
    
    return removed_count


def remove_annotations_from_pdf(input_path, output_path=None, specific_page=None):
    """
    从PDF文件中移除批注
    
    Args:
        input_path (str): 输入PDF文件路径
        output_path (str, optional): 输出PDF文件路径，如果为None则自动生成
        specific_page (int, optional): 指定页面号（从0开始），如果为None则处理所有页面
    
    Returns:
        tuple: (成功标志, 处理的页面数, 移除的批注总数)
    """
    try:
        # 打开PDF文档
        doc = fitz.open(input_path)
        print(f"成功打开PDF文件: {input_path}")
        print(f"总页数: {len(doc)}")
        
        total_annotations_removed = 0
        pages_processed = 0
        
        # 确定要处理的页面范围
        if specific_page is not None:
            if specific_page < 0 or specific_page >= len(doc):
                print(f"错误: 页面号 {specific_page} 超出范围 (0-{len(doc)-1})")
                return False, 0, 0
            page_range = [specific_page]
            print(f"处理指定页面: {specific_page + 1}")
        else:
            page_range = range(len(doc))
            print("处理所有页面")
        
        # 处理每个页面
        for page_num in page_range:
            page = doc[page_num]
            print(f"\n处理第 {page_num + 1} 页...")
            
            # 统计当前页面的批注数量
            annot_count = len(list(page.annots()))
            print(f"  当前页面批注数量: {annot_count}")
            
            if annot_count > 0:
                # 移除批注
                removed = remove_annotations_from_page(page)
                total_annotations_removed += removed
                print(f"  移除了 {removed} 个批注")
            else:
                print("  该页面没有批注")
            
            pages_processed += 1
        
        # 生成输出文件名
        if output_path is None:
            input_file = Path(input_path)
            if specific_page is not None:
                output_path = input_file.parent / f"{input_file.stem}_page{specific_page + 1}_no_annotations{input_file.suffix}"
            else:
                output_path = input_file.parent / f"{input_file.stem}_no_annotations{input_file.suffix}"
        
        # 保存处理后的PDF
        doc.save(str(output_path))
        doc.close()
        
        print(f"\n处理完成!")
        print(f"处理页面数: {pages_processed}")
        print(f"移除批注总数: {total_annotations_removed}")
        print(f"输出文件: {output_path}")
        
        return True, pages_processed, total_annotations_removed
        
    except Exception as e:
        print(f"处理PDF时发生错误: {str(e)}")
        return False, 0, 0


def analyze_pdf_annotations(input_path, specific_page=None):
    """
    分析PDF文件中的批注信息（不删除）
    
    Args:
        input_path (str): 输入PDF文件路径
        specific_page (int, optional): 指定页面号（从0开始）
    """
    try:
        doc = fitz.open(input_path)
        print(f"分析PDF文件: {input_path}")
        print(f"总页数: {len(doc)}")
        
        # 确定要分析的页面范围
        if specific_page is not None:
            if specific_page < 0 or specific_page >= len(doc):
                print(f"错误: 页面号 {specific_page} 超出范围 (0-{len(doc)-1})")
                return
            page_range = [specific_page]
            print(f"分析指定页面: {specific_page + 1}")
        else:
            page_range = range(len(doc))
            print("分析所有页面")
        
        total_annotations = 0
        annotation_types = {}
        
        for page_num in page_range:
            page = doc[page_num]
            print(f"\n第 {page_num + 1} 页:")
            
            page_annotations = 0
            for annot in page.annots():
                annot_type = annot.type[1]  # 获取批注类型名称
                annot_content = annot.content  # 获取批注内容
                annot_rect = annot.rect  # 获取批注位置
                
                print(f"  - 类型: {annot_type}")
                print(f"    内容: {annot_content[:50]}..." if len(annot_content) > 50 else f"    内容: {annot_content}")
                print(f"    位置: ({annot_rect.x0:.1f}, {annot_rect.y0:.1f}, {annot_rect.x1:.1f}, {annot_rect.y1:.1f})")
                
                # 统计批注类型
                if annot_type in annotation_types:
                    annotation_types[annot_type] += 1
                else:
                    annotation_types[annot_type] = 1
                
                page_annotations += 1
                total_annotations += 1
            
            if page_annotations == 0:
                print("  该页面没有批注")
        
        print(f"\n批注统计:")
        print(f"总批注数: {total_annotations}")
        if annotation_types:
            print("批注类型分布:")
            for annot_type, count in annotation_types.items():
                print(f"  {annot_type}: {count}")
        
        doc.close()
        
    except Exception as e:
        print(f"分析PDF时发生错误: {str(e)}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  分析批注: python remove_pdf_annotations.py analyze <PDF文件路径> [页面号]")
        print("  移除批注: python remove_pdf_annotations.py remove <PDF文件路径> [页面号] [输出文件路径]")
        print("")
        print("示例:")
        print("  python remove_pdf_annotations.py analyze document.pdf")
        print("  python remove_pdf_annotations.py analyze document.pdf 18")
        print("  python remove_pdf_annotations.py remove document.pdf")
        print("  python remove_pdf_annotations.py remove document.pdf 18")
        print("  python remove_pdf_annotations.py remove document.pdf 18 output.pdf")
        return
    
    command = sys.argv[1].lower()
    
    if command not in ['analyze', 'remove']:
        print("错误: 命令必须是 'analyze' 或 'remove'")
        return
    
    if len(sys.argv) < 3:
        print("错误: 请提供PDF文件路径")
        return
    
    pdf_path = sys.argv[2]
    
    if not os.path.exists(pdf_path):
        print(f"错误: 文件不存在: {pdf_path}")
        return
    
    # 解析页面号参数
    specific_page = None
    if len(sys.argv) > 3 and sys.argv[3].isdigit():
        specific_page = int(sys.argv[3]) - 1  # 转换为0基索引
    
    if command == 'analyze':
        analyze_pdf_annotations(pdf_path, specific_page)
    
    elif command == 'remove':
        # 解析输出文件路径参数
        output_path = None
        if len(sys.argv) > 4:
            output_path = sys.argv[4]
        elif len(sys.argv) > 3 and not sys.argv[3].isdigit():
            output_path = sys.argv[3]
            specific_page = None
        
        success, pages, annotations = remove_annotations_from_pdf(pdf_path, output_path, specific_page)
        
        if success:
            print("\n✅ 批注移除成功!")
        else:
            print("\n❌ 批注移除失败!")


if __name__ == "__main__":
    main()
