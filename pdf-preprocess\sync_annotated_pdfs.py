import json
import os
import sys
from pathlib import Path
# 导入我们需要的PDF拆分器
from split_pdf import PDFSplitterWithGemini

def process_textbooks(meta_file_path: str):
    """
    根据元数据文件处理教材.

    - 从GoodNotes同步的PDF读取内容.
    - 从对应的 'fitz.pdf' 文件读取目录(TOC).
    - 根据TOC拆分内容PDF.
    - 将拆分后的PDF文件保存到指定的Obsidian目录.
    """
    meta_file = Path(meta_file_path).resolve()
    if not meta_file.exists():
        print(f"❌ 错误: 元数据文件未找到: {meta_file}")
        sys.exit(1)
        
    try:
        with open(meta_file, 'r', encoding='utf-8') as f:
            meta_data = json.load(f)
    except json.JSONDecodeError:
        print(f"❌ 错误: 元数据文件格式不正确: {meta_file}")
        sys.exit(1)

    base_dir = meta_file.parent
    splitter = PDFSplitterWithGemini()

    # --- 用于生成最终报告的统计信息 ---
    total_books = 0
    success_count = 0
    skipped_books = []
    total_files_generated = 0
    
    textbooks_to_process = meta_data.get('textbooks', [])
    total_books = len(textbooks_to_process)
    
    print("🚀 开始处理教材同步任务...")
    print("="*80)

    for textbook in textbooks_to_process:
        title = textbook.get('title')
        note_path_str = textbook.get('note')
        subdir_str = textbook.get('subdir')
        ob_path_str = textbook.get('ob_path')

        if not all([title, note_path_str, subdir_str, ob_path_str]):
            reason = "元数据不完整 (缺少 title, note, subdir, 或 ob_path)"
            print(f"🟡 [{title or '未知条目'}] 跳过 - {reason}")
            skipped_books.append({'title': title or '未知条目', 'reason': reason})
            continue

        # --- 解析和校验路径 ---
        note_pdf_path = (base_dir / note_path_str).resolve()
        toc_pdf_path = (base_dir / subdir_str / f"{title}fitz.pdf").resolve()
        output_dir = (base_dir / ob_path_str).resolve()
        
        # 检查文件和目录是否存在
        if not note_pdf_path.exists():
            reason = f"找不到笔记PDF: {note_pdf_path}"
            print(f"🔴 [{title}] 失败 - {reason}")
            skipped_books.append({'title': title, 'reason': reason})
            continue
        if not toc_pdf_path.exists():
            reason = f"找不到目录PDF: {toc_pdf_path}"
            print(f"🔴 [{title}] 失败 - {reason}")
            skipped_books.append({'title': title, 'reason': reason})
            continue
        
        try:
            chapters = splitter.extract_pdf_toc(str(toc_pdf_path))
            
            if not chapters:
                reason = f"未能从 {toc_pdf_path.name} 提取到目录信息"
                print(f"🔴 [{title}] 失败 - {reason}")
                skipped_books.append({'title': title, 'reason': reason})
                continue
            
            # 调用拆分函数，但关闭其详细日志
            split_files = splitter.split_pdf_by_chapters(
                pdf_path=str(note_pdf_path),
                chapters=chapters,
                output_dir=str(output_dir),
                verbose=False, # <--- 关闭详细日志
                toc_source_pdf_path=str(toc_pdf_path) # <--- 传递TOC来源
            )
            
            num_generated = len(split_files)
            total_files_generated += num_generated
            success_count += 1
            print(f"✅ [{title}] 成功 - 生成了 {num_generated} 个文件到 {output_dir.name}/")

        except Exception as e:
            reason = f"发生意外错误: {e}"
            print(f"🔴 [{title}] 失败 - {reason}")
            skipped_books.append({'title': title, 'reason': reason})
    
    # --- 打印最终总结报告 ---
    failed_count = len(skipped_books)
    print("\n" + "="*80)
    print("🎉 任务处理完成！")
    print("\n📊 总结报告:")
    print(f"   - 总任务数: {total_books}")
    print(f"   - ✅ 成功: {success_count}")
    print(f"   - ❌ 失败/跳过: {failed_count}")
    print(f"   - 📄 总生成文件数: {total_files_generated}")

    if skipped_books:
        print("\n❌ 未成功处理的书籍详情:")
        for item in skipped_books:
            print(f"   - 书名: {item['title']}")
            print(f"     原因: {item['reason']}")
    print("="*80)


def main():
    """
    主函数，用于执行脚本
    """
    if len(sys.argv) > 1:
        meta_file_path = sys.argv[1]
    else:
        # 默认使用脚本所在目录下的 textbook-meta.json
        script_dir = Path(__file__).parent
        meta_file_path = script_dir / 'textbook-meta.json'
        print(f"ℹ️  未指定元数据文件，将使用默认路径: {meta_file_path}")

    process_textbooks(str(meta_file_path))


if __name__ == "__main__":
    main() 