# 全局常量定义
S_BOXES = [
    [
        [14,4,13,1,2,15,11,8,3,10,6,12,5,9,0,7],
        [0,15,7,4,14,2,13,1,10,6,12,11,9,5,3,8],
        [4,1,14,8,13,6,2,11,15,12,9,7,3,10,5,0],
        [15,12,8,2,4,9,1,7,5,11,3,14,10,0,6,13]
    ],
    [
        [15,1,8,14,6,11,3,4,9,7,2,13,12,0,5,10],
        [3,13,4,7,15,2,8,14,12,0,1,10,6,9,11,5],
        [0,14,7,11,10,4,13,1,5,8,12,6,9,3,2,15],
        [13,8,10,1,3,15,4,2,11,6,7,12,0,5,14,9]
    ],
    [
        [10,0,9,14,6,3,15,5,1,13,12,7,11,4,2,8],
        [13,7,0,9,3,4,6,10,2,8,5,14,12,11,15,1],
        [13,6,4,9,8,15,3,0,11,1,2,12,5,10,14,7],
        [1,10,13,0,6,9,8,7,4,15,14,3,11,5,2,12]
    ],
    [
        [7,13,14,3,0,6,9,10,1,2,8,5,11,12,4,15],
        [13,8,11,5,6,15,0,3,4,7,2,12,1,10,14,9],
        [10,6,9,0,12,11,7,13,15,1,3,14,5,2,8,4],
        [3,15,0,6,10,1,13,8,9,4,5,11,12,7,2,14]
    ],
    [
        [2,12,4,1,7,10,11,6,8,5,3,15,13,0,14,9],
        [14,11,2,12,4,7,13,1,5,0,15,10,3,9,8,6],
        [4,2,1,11,10,13,7,8,15,9,12,5,6,3,0,14],
        [11,8,12,7,1,14,2,13,6,15,0,9,10,4,5,3]
    ],
    [
        [12,1,10,15,9,2,6,8,0,13,3,4,14,7,5,11],
        [10,15,4,2,7,12,9,5,6,1,13,14,0,11,3,8],
        [9,14,15,5,2,8,12,3,7,0,4,10,1,13,11,6],
        [4,3,2,12,9,5,15,10,11,14,1,7,6,0,8,13]
    ],
    [
        [4,11,2,14,15,0,8,13,3,12,9,7,5,10,6,1],
        [13,0,11,7,4,9,1,10,14,3,5,12,2,15,8,6],
        [1,4,11,13,12,3,7,14,10,15,6,8,0,5,9,2],
        [6,11,13,8,1,4,10,7,9,5,0,15,14,2,3,12]
    ],
    [
        [13,2,8,4,6,15,11,1,10,9,3,14,5,0,12,7],
        [1,15,13,8,10,3,7,4,12,5,6,11,0,14,9,2],
        [7,11,4,1,9,12,14,2,0,6,10,13,15,3,5,8],
        [2,1,14,7,4,10,8,13,15,12,9,0,3,5,6,11]
    ]
]

PC2 = [13,16,10,23,0,4,2,27,14,5,20,9,
       22,18,11,3,25,7,15,6,26,19,12,1,
       40,51,30,36,46,54,29,39,50,44,32,47,
       43,48,38,55,33,52,45,41,49,35,28,31]

IP_TABLE = [
    57,49,41,33,25,17,9,1,59,51,43,35,27,19,11,3,
    61,53,45,37,29,21,13,5,63,55,47,39,31,23,15,7,
    56,48,40,32,24,16,8,0,58,50,42,34,26,18,10,2,
    60,52,44,36,28,20,12,4,62,54,46,38,30,22,14,6
]

FP_TABLE = [
    39,7,47,15,55,23,63,31,
    38,6,46,14,54,22,62,30,
    37,5,45,13,53,21,61,29,
    36,4,44,12,52,20,60,28,
    35,3,43,11,51,19,59,27,
    34,2,42,10,50,18,58,26,
    33,1,41,9,49,17,57,25,
    32,0,40,8,48,16,56,24
]

E_TABLE = [
    31, 0, 1, 2, 3, 4,
    3, 4, 5, 6, 7, 8,
    7, 8, 9, 10,11,12,
    11,12,13,14,15,16,
    15,16,17,18,19,20,
    19,20,21,22,23,24,
    23,24,25,26,27,28,
    27,28,29,30,31,0
]

P_TABLE = [
    15,6,19,20,28,11,27,16,
    0,14,22,25,4,17,30,9,
    1,7,23,13,31,26,2,8,
    18,12,29,5,21,10,3,24
]

LOOP_TABLE = [1,1,2,2,2,2,2,2,1,2,2,2,2,2,2,1]

def strEnc(data, firstKey, secondKey, thirdKey):
    """
    DES加密（未实现解密）
    @param data: 待加密字符串
    @param firstKey, secondKey, thirdKey: 密钥字符串，可为空
    """
    # 减少重复判断，通过预处理keys
    keys_list = []
    for k in (firstKey, secondKey, thirdKey):
        if k:
            keys_list.append(getKeyBytes(k))
        else:
            keys_list.append(None)
    # keys_list 形如 [ [keyBlocks...], [keyBlocks...], [keyBlocks...] ] 或者 None
    
    encData = []
    leng = len(data)
    if leng == 0:
        return ''
    
    # 定义一个函数执行单次块加密
    def encryptBlock(bt):
        tempBt = bt
        # 对每组key都执行enc
        for kb in keys_list:
            if kb:
                for subKey in kb:
                    tempBt = enc(tempBt, subKey)
        return tempBt

    # 根据长度分块
    block_size = 4
    full_blocks = leng // block_size
    remainder = leng % block_size

    for i in range(full_blocks):
        block_str = data[i*block_size:(i+1)*block_size]
        block_bt = strToBt(block_str)
        encBlock = encryptBlock(block_bt)
        encData.append(bt64ToHex(encBlock))

    if remainder > 0:
        block_str = data[full_blocks*block_size:]
        block_bt = strToBt(block_str)
        encBlock = encryptBlock(block_bt)
        encData.append(bt64ToHex(encBlock))

    return ''.join(encData)


def bt64ToHex(byteData):
    # 将64位bit转换为16位hex
    # 原始逻辑是每4位bit变成1位hex。可以拼接后一次转换
    result = []
    for i in range(0,64,4):
        nibble = (byteData[i] << 3) + (byteData[i+1] << 2) + (byteData[i+2] << 1) + byteData[i+3]
        result.append("{:X}".format(nibble))
    return ''.join(result)


def getKeyBytes(key: str):
    # 将key分为4字符一组(不足4字符用0填补)，each group 转换为64位block
    leng = len(key)
    block_count = (leng + 3) // 4
    keyBytes = []
    for i in range(block_count):
        start = i*4
        end = start+4
        part = key[start:end]
        bt = strToBt(part)
        keyBytes.append(bt)
    return keyBytes


def strToBt(strr):
    # 将最多4个字符(不足补0)转换为64位bit数组
    bt = [0]*64
    chars = strr
    # 若不足4位，用 '\0' 填充
    chars = chars + '\0'*(4-len(chars))
    for i in range(4):
        k = ord(chars[i])
        for j in range(16):
            bt[16*i+j] = (k >> (15-j)) & 1
    return bt


def enc(dataByte, keyByte):
    keys = generateKeys(keyByte)
    ipByte = initPermute(dataByte)
    ipLeft = ipByte[:32]
    ipRight = ipByte[32:]

    for i in range(16):
        tempLeft = ipLeft[:]
        # 取第i轮子密钥
        roundKey = keys[i]
        tempRight = xor(pPermute(sBoxPermute(xor(expandPermute(ipRight), roundKey))), tempLeft)
        ipLeft, ipRight = ipRight, tempRight

    finalData = ipRight + ipLeft
    return finallyPermute(finalData)


def generateKeys(keyByte):
    # 将keyByte(64位)转为56位子密钥，然后产生16组48位子密钥
    key = [0]*56
    keys = [[0]*48 for _ in range(16)]

    # 将keyByte转成56位
    # PC1置换（原代码使用极其复杂的方式），这里简化对应逻辑：
    # PC1表略去直接硬编码，使用原有逻辑
    # 原逻辑通过特殊顺序抽取位，这里保持逻辑不变
    # 按原始作者逻辑, key arrangement:
    for i in range(7):
        for j in range(8):
            key[i*8 + j] = keyByte[8*(7-j)+i]

    # 循环左移并选择PC2
    for i in range(16):
        shift = LOOP_TABLE[i]
        # 左移C、D区
        left_part = key[0:28]
        right_part = key[28:56]
        left_part = left_part[shift:] + left_part[:shift]
        right_part = right_part[shift:] + right_part[:shift]
        key = left_part + right_part

        # PC2选择
        for m in range(48):
            keys[i][m] = key[PC2[m]]

    return keys


def initPermute(originalData):
    # 初始置换
    ipByte = [0]*64
    for i in range(64):
        ipByte[i] = originalData[IP_TABLE[i]]
    return ipByte


def finallyPermute(endByte):
    # 最终置换
    fpByte = [0]*64
    for i in range(64):
        fpByte[i] = endByte[FP_TABLE[i]]
    return fpByte


def xor(byteOne, byteTwo):
    return [b1 ^ b2 for b1,b2 in zip(byteOne, byteTwo)]


def pPermute(sBoxByte):
    return [sBoxByte[P_TABLE[i]] for i in range(32)]


def sBoxPermute(expandByte):
    sBoxByte = [0]*32
    for m in range(8):
        row = (expandByte[m*6]<<1) + expandByte[m*6+5]
        col = (expandByte[m*6+1]<<3) + (expandByte[m*6+2]<<2) + (expandByte[m*6+3]<<1) + expandByte[m*6+4]
        val = S_BOXES[m][row][col]
        # val转4位二进制
        sBoxByte[m*4:(m+1)*4] = [(val>>3)&1,(val>>2)&1,(val>>1)&1,val&1]
    return sBoxByte


def expandPermute(rightData):
    return [rightData[E_TABLE[i]] for i in range(48)]

