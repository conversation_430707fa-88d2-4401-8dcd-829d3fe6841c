{"settings_version": "0.23.0", "debug": false, "obsidian_command_palette_prefix": "Execute: ", "preview_variables_in_command_palette": true, "show_autocomplete_menu": true, "working_directory": "", "default_shells": {}, "environment_variable_path_augmentations": {}, "show_installation_warnings": true, "error_message_duration": 20, "notification_message_duration": 10, "execution_notification_mode": "disabled", "output_channel_clipboard_also_outputs_to_notification": true, "output_channel_notification_decorates_output": true, "enable_events": true, "approve_modals_by_pressing_enter_key": true, "command_palette": {"re_execute_last_shell_command": {"enabled": true, "prefix": "Re-execute: "}}, "max_visible_lines_in_shell_command_fields": false, "shell_commands": [{"id": "efnx11v1hv", "platform_specific_commands": {"default": "powershell -NoLogo -NoProfile -STA -Command \"[Console]::OutputEncoding=[System.Text.Encoding]::UTF8;$path='{{file_path:absolute}}';Set-Clipboard -LiteralPath $path;Write-Host ('Copied: '+$path)\"\nWrite-Host ('Copied: {0}' -f $shadow)"}, "shells": {}, "alias": "Copy file to clipboard", "icon": null, "confirm_execution": false, "ignore_error_codes": [], "input_contents": {"stdin": null}, "output_handlers": {"stdout": {"handler": "notification", "convert_ansi_code": true}, "stderr": {"handler": "notification", "convert_ansi_code": true}}, "output_wrappers": {"stdout": null, "stderr": null}, "output_channel_order": "stdout-first", "output_handling_mode": "buffered", "execution_notification_mode": null, "events": {}, "debounce": null, "command_palette_availability": "enabled", "preactions": [], "variable_default_values": {}}, {"id": "6slzcjaa0n", "platform_specific_commands": {"default": "powershell -NoLogo -NoProfile -STA -Command \"[Console]::OutputEncoding=[System.Text.Encoding]::UTF8;$src='{{file_path:absolute}}';$dir=[System.IO.Path]::GetDirectoryName($src);if([string]::IsNullOrEmpty($dir)){Write-Error '❌ 无活动文件';exit 1};$name=[System.IO.Path]::GetFileName($src);$shadow=Join-Path (Join-Path $dir 'PDF') $name;if(Test-Path $shadow){Set-Clipboard -LiteralPath $shadow;Write-Host ('Copied: '+$shadow)}else{Write-Error ('🚫 未找到 PDF 分身：'+$shadow);exit 1}\""}, "shells": {}, "alias": "Copy shadow file", "icon": null, "confirm_execution": false, "ignore_error_codes": [], "input_contents": {"stdin": null}, "output_handlers": {"stdout": {"handler": "notification", "convert_ansi_code": true}, "stderr": {"handler": "notification", "convert_ansi_code": true}}, "output_wrappers": {"stdout": null, "stderr": null}, "output_channel_order": "stdout-first", "output_handling_mode": "buffered", "execution_notification_mode": null, "events": {}, "debounce": null, "command_palette_availability": "enabled", "preactions": [], "variable_default_values": {}}], "prompts": [], "builtin_variables": {}, "custom_variables": [], "custom_variables_notify_changes_via": {"obsidian_uri": true, "output_assignment": true}, "custom_shells": [], "output_wrappers": []}