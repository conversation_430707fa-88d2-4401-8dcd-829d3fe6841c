# 全局常量定义（与原始实现保持一致）
S_BOXES = [
    [
        [14,4,13,1,2,15,11,8,3,10,6,12,5,9,0,7],
        [0,15,7,4,14,2,13,1,10,6,12,11,9,5,3,8],
        [4,1,14,8,13,6,2,11,15,12,9,7,3,10,5,0],
        [15,12,8,2,4,9,1,7,5,11,3,14,10,0,6,13]
    ],
    [
        [15,1,8,14,6,11,3,4,9,7,2,13,12,0,5,10],
        [3,13,4,7,15,2,8,14,12,0,1,10,6,9,11,5],
        [0,14,7,11,10,4,13,1,5,8,12,6,9,3,2,15],
        [13,8,10,1,3,15,4,2,11,6,7,12,0,5,14,9]
    ],
    [
        [10,0,9,14,6,3,15,5,1,13,12,7,11,4,2,8],
        [13,7,0,9,3,4,6,10,2,8,5,14,12,11,15,1],
        [13,6,4,9,8,15,3,0,11,1,2,12,5,10,14,7],
        [1,10,13,0,6,9,8,7,4,15,14,3,11,5,2,12]
    ],
    [
        [7,13,14,3,0,6,9,10,1,2,8,5,11,12,4,15],
        [13,8,11,5,6,15,0,3,4,7,2,12,1,10,14,9],
        [10,6,9,0,12,11,7,13,15,1,3,14,5,2,8,4],
        [3,15,0,6,10,1,13,8,9,4,5,11,12,7,2,14]
    ],
    [
        [2,12,4,1,7,10,11,6,8,5,3,15,13,0,14,9],
        [14,11,2,12,4,7,13,1,5,0,15,10,3,9,8,6],
        [4,2,1,11,10,13,7,8,15,9,12,5,6,3,0,14],
        [11,8,12,7,1,14,2,13,6,15,0,9,10,4,5,3]
    ],
    [
        [12,1,10,15,9,2,6,8,0,13,3,4,14,7,5,11],
        [10,15,4,2,7,12,9,5,6,1,13,14,0,11,3,8],
        [9,14,15,5,2,8,12,3,7,0,4,10,1,13,11,6],
        [4,3,2,12,9,5,15,10,11,14,1,7,6,0,8,13]
    ],
    [
        [4,11,2,14,15,0,8,13,3,12,9,7,5,10,6,1],
        [13,0,11,7,4,9,1,10,14,3,5,12,2,15,8,6],
        [1,4,11,13,12,3,7,14,10,15,6,8,0,5,9,2],
        [6,11,13,8,1,4,10,7,9,5,0,15,14,2,3,12]
    ],
    [
        [13,2,8,4,6,15,11,1,10,9,3,14,5,0,12,7],
        [1,15,13,8,10,3,7,4,12,5,6,11,0,14,9,2],
        [7,11,4,1,9,12,14,2,0,6,10,13,15,3,5,8],
        [2,1,14,7,4,10,8,13,15,12,9,0,3,5,6,11]
    ]
]

PC2 = [13,16,10,23,0,4,2,27,14,5,20,9,
       22,18,11,3,25,7,15,6,26,19,12,1,
       40,51,30,36,46,54,29,39,50,44,32,47,
       43,48,38,55,33,52,45,41,49,35,28,31]

IP_TABLE = [
    57,49,41,33,25,17,9,1,59,51,43,35,27,19,11,3,
    61,53,45,37,29,21,13,5,63,55,47,39,31,23,15,7,
    56,48,40,32,24,16,8,0,58,50,42,34,26,18,10,2,
    60,52,44,36,28,20,12,4,62,54,46,38,30,22,14,6
]

FP_TABLE = [
    39,7,47,15,55,23,63,31,
    38,6,46,14,54,22,62,30,
    37,5,45,13,53,21,61,29,
    36,4,44,12,52,20,60,28,
    35,3,43,11,51,19,59,27,
    34,2,42,10,50,18,58,26,
    33,1,41,9,49,17,57,25,
    32,0,40,8,48,16,56,24
]

E_TABLE = [
    31, 0, 1, 2, 3, 4,
    3, 4, 5, 6, 7, 8,
    7, 8, 9, 10,11,12,
    11,12,13,14,15,16,
    15,16,17,18,19,20,
    19,20,21,22,23,24,
    23,24,25,26,27,28,
    27,28,29,30,31,0
]

P_TABLE = [
    15,6,19,20,28,11,27,16,
    0,14,22,25,4,17,30,9,
    1,7,23,13,31,26,2,8,
    18,12,29,5,21,10,3,24
]

LOOP_TABLE = [1,1,2,2,2,2,2,2,1,2,2,2,2,2,2,1]

# 预置十六进制字符表与16位字符位缓存
_HEX = '0123456789ABCDEF'
_BIT16_CACHE = {}  # ord(c) & 0xFFFF -> 16位bit列表


def strEnc(data, firstKey, secondKey, thirdKey):
    """
    DES加密（未实现解密）
    @param data: 待加密字符串
    @param firstKey, secondKey, thirdKey: 密钥字符串，可为空
    """
    leng = len(data)
    if leng == 0:
        return ''

    # 预计算每个密钥块的16轮子密钥，避免对每个数据块重复生成
    keys_rounds_list = []
    for k in (firstKey, secondKey, thirdKey):
        if k:
            key_blocks = getKeyBytes(k)  # List[64位bit列表]
            # 为每个64位密钥块生成16组子密钥（每组48位bit列表）
            rounds_for_blocks = [generateKeys(block) for block in key_blocks]
            keys_rounds_list.append(rounds_for_blocks)
        else:
            keys_rounds_list.append(None)

    # 定义一次块加密：按“第一密钥的所有块 -> 第二密钥的所有块 -> 第三密钥的所有块”的顺序层层加密
    def encryptBlock(bt64):
        tmp = bt64
        for round_keys_blocks in keys_rounds_list:
            if round_keys_blocks is None:
                continue
            for rk in round_keys_blocks:
                tmp = enc_with_round_keys(tmp, rk)
        return tmp

    # 分块：每4个字符 -> 64位
    block_size = 4
    full_blocks = leng // block_size
    remainder = leng % block_size

    out_hex = []
    for i in range(full_blocks):
        block_str = data[i*block_size:(i+1)*block_size]
        block_bt = strToBt(block_str)
        encBlock = encryptBlock(block_bt)
        out_hex.append(bt64ToHex(encBlock))

    if remainder:
        block_str = data[full_blocks*block_size:]
        block_bt = strToBt(block_str)
        encBlock = encryptBlock(block_bt)
        out_hex.append(bt64ToHex(encBlock))

    return ''.join(out_hex)


def enc(dataByte, keyByte):
    """保持原API：内部生成子密钥后加密。建议外部优先使用预计算后的 enc_with_round_keys。"""
    return enc_with_round_keys(dataByte, generateKeys(keyByte))


def enc_with_round_keys(dataByte, round_keys):
    """使用已预计算的16轮子密钥进行单块加密。"""
    # 局部绑定减少全局查找
    _initPermute = initPermute
    _expand = expandPermute
    _sbox = sBoxPermute
    _pperm = pPermute
    _finalPermute = finallyPermute

    ip = _initPermute(dataByte)
    L = ip[:32]
    R = ip[32:]

    # 16轮Feistel
    for i in range(16):
        # f(R, K) = P(S(E(R) xor K))
        # 1) E 扩展
        ER = _expand(R)
        # 2) 与轮密钥异或
        rk = round_keys[i]
        for j in range(48):
            ER[j] ^= rk[j]
        # 3) 过S盒 -> 32位
        s_out = _sbox(ER)
        # 4) P 置换
        f = _pperm(s_out)
        # 5) 新的R = L xor f
        newR = [f[j] ^ L[j] for j in range(32)]
        # 交换
        L, R = R, newR

    # 交换后拼接并最终置换
    final64 = R + L
    return _finalPermute(final64)


def bt64ToHex(byteData):
    # 64位bit -> 16位Hex
    out = []
    H = _HEX
    for i in range(0, 64, 4):
        n = (byteData[i] << 3) | (byteData[i+1] << 2) | (byteData[i+2] << 1) | byteData[i+3]
        out.append(H[n])
    return ''.join(out)


def getKeyBytes(key: str):
    # 将密钥按4字符分组（不足4字符补 '\0'），每组转成64位bit列表
    leng = len(key)
    block_count = (leng + 3) // 4
    keyBytes = []
    for i in range(block_count):
        part = key[i*4:(i+1)*4]
        keyBytes.append(strToBt(part))
    return keyBytes


def _char16bits(val):
    """返回字符16位bit列表（使用缓存）"""
    v = val & 0xFFFF
    bits = _BIT16_CACHE.get(v)
    if bits is not None:
        return bits
    # 生成并缓存
    b = [(v >> (15 - j)) & 1 for j in range(16)]
    _BIT16_CACHE[v] = b
    return b


def strToBt(s):
    # 将最多4个字符（不足补 '\0'）转换为64位bit数组
    s = s + '\0' * (4 - len(s))
    bt = [0] * 64
    for i in range(4):
        bits16 = _char16bits(ord(s[i]))
        start = 16 * i
        bt[start:start+16] = bits16
    return bt


def generateKeys(keyByte):
    """
    由64位密钥块生成16轮的48位子密钥（列表形式），
    保持原实现的PC1等逻辑与位序。
    """
    # 原实现的“PC1逻辑”，生成56位 key（去掉每字节的某位）
    key56 = [0] * 56
    # key56[i*8 + j] = keyByte[8*(7-j)+i]
    for i in range(7):
        base_dest = i * 8
        for j in range(8):
            key56[base_dest + j] = keyByte[8 * (7 - j) + i]

    # 拆为 C、D 两部分（各28位）
    C = key56[:28]
    D = key56[28:56]

    # 使用“偏移量”避免每轮真正旋转列表
    c_off = 0
    d_off = 0

    keys = [[0] * 48 for _ in range(16)]
    pc2 = PC2
    loop = LOOP_TABLE

    for rnd in range(16):
        shift = loop[rnd]
        c_off = (c_off + shift) % 28
        d_off = (d_off + shift) % 28

        rk = keys[rnd]
        # 选择PC2，不构造CD，直接索引C或D
        for m in range(48):
            idx = pc2[m]
            if idx < 28:
                rk[m] = C[(idx + c_off) % 28]
            else:
                rk[m] = D[(idx - 28 + d_off) % 28]

    return keys


def initPermute(originalData):
    ipt = IP_TABLE
    return [originalData[i] for i in ipt]


def finallyPermute(endByte):
    fpt = FP_TABLE
    return [endByte[i] for i in fpt]


def pPermute(sBoxByte):
    pt = P_TABLE
    return [sBoxByte[i] for i in pt]


def sBoxPermute(expandByte):
    sBoxByte = [0] * 32
    boxes = S_BOXES
    sb = sBoxByte  # 局部别名
    for m in range(8):
        b = m * 6
        row = (expandByte[b] << 1) | expandByte[b + 5]
        col = (expandByte[b + 1] << 3) | (expandByte[b + 2] << 2) | (expandByte[b + 3] << 1) | expandByte[b + 4]
        val = boxes[m][row][col]
        o = m * 4
        sb[o]   = (val >> 3) & 1
        sb[o+1] = (val >> 2) & 1
        sb[o+2] = (val >> 1) & 1
        sb[o+3] = val & 1
    return sBoxByte


def expandPermute(rightData):
    et = E_TABLE
    return [rightData[i] for i in et]


# 如需简单自测，可使用如下示例：
# if __name__ == '__main__':
#     print(strEnc("HelloWorld", "KeyOne", "KeyTwo", "KeyThree"))
#     print(strEnc("ABC", "K", None, None))
