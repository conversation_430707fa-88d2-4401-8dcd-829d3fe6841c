// Configuration - 可配置选项
const CONFIG = {
  APPROVAL_STRATEGY: "allow_once", // allow_once, allow_always
  VERBOSE_LOGGING: true
};

const SELECTORS = {
  TITLE: 'h2',
  TOOL_NAME: '.font-mono',
  TOOL_DISPLAY: '.font-medium.text-\\[0\\.9rem\\]',
  DIALOG: '[role="dialog"][data-state="open"]'
};

// 统一日志函数
function log(level, message, ...args) {
  if (!CONFIG.VERBOSE_LOGGING) return;
  const logFn = console[level] || console.log;
  logFn(message, ...args);
}

// 工具函数：安全的文本提取
function safeGetText(element) {
  return element?.textContent?.trim() || "";
}

// 工具函数：生成对话框唯一标识
function getDialogId(dialog) {
  return dialog.id || dialog.getAttribute('aria-labelledby') || 'unknown';
}

// 工具函数：验证MCP工具对话框
function isToolPermissionDialog(dialog) {
  const titleElement = dialog.querySelector(SELECTORS.TITLE);
  const titleText = safeGetText(titleElement);
  return titleText.includes("would like to use this tool");
}

// 工具函数：提取工具信息
function extractToolInfo(dialog) {
  const toolNameElement = dialog.querySelector(SELECTORS.TOOL_NAME);
  const toolDisplayElement = dialog.querySelector(SELECTORS.TOOL_DISPLAY);
  
  const toolName = safeGetText(toolNameElement) || "unknown-tool";
  const toolDisplay = safeGetText(toolDisplayElement) || toolName;
  
  return { toolName, toolDisplay };
}

// 工具函数：查找目标按钮
function findApprovalButton(dialog) {
  const buttons = Array.from(dialog.querySelectorAll("button"));
  const buttonTexts = buttons.map(btn => safeGetText(btn));
  
  log('log', "🔘 可用按钮:", buttonTexts);
  
  const searchText = CONFIG.APPROVAL_STRATEGY === "allow_always" ? "allow always" : "allow once";
  return buttons.find(btn => safeGetText(btn).toLowerCase().includes(searchText));
}

// 主要的自动批准逻辑
function processApprovalDialog(dialog) {
  const dialogId = getDialogId(dialog);

  // 验证是否为MCP工具权限对话框
  if (!isToolPermissionDialog(dialog)) {
    log('log', "❌ 跳过：不是工具权限对话框");
    return false;
  }

  // 提取工具信息
  const { toolName, toolDisplay } = extractToolInfo(dialog);
  
  log('log', "🔍 检测到工具权限请求:");
  log('log', "   - 工具名称:", toolName);
  log('log', "   - 显示名称:", toolDisplay);
  log('log', "   - 对话框ID:", dialogId);

  // 查找目标按钮
  const targetButton = findApprovalButton(dialog);
  
  if (!targetButton) {
    console.warn("⚠️ 未找到目标批准按钮");
    return false;
  }

  // 执行自动点击
  return executeApproval(targetButton, toolDisplay);
}

// 工具函数：执行自动批准
function executeApproval(button, toolDisplay) {
  try {
    log('log', "✅ 自动批准工具:", toolDisplay);
    log('log', "   - 点击按钮:", safeGetText(button));
    log('log', "   - 策略:", CONFIG.APPROVAL_STRATEGY);
    
    button.click();
    return true;
  } catch (error) {
    console.error("❌ 自动点击失败:", error);
    return false;
  }
}

// 主要的观察器逻辑
const observer = new MutationObserver(() => {
  const dialogs = document.querySelectorAll(SELECTORS.DIALOG);
  
  if (dialogs.length === 0) return;
  
  log('log', "👁️ 检测到", dialogs.length, "个打开的对话框");

  // 直接处理每个对话框（无冷却机制）
  dialogs.forEach(dialog => {
    const dialogId = getDialogId(dialog);
    log('log', "🔄 开始处理 Dialog:", dialogId);
    processApprovalDialog(dialog);
  });
});

// 启动观察器
observer.observe(document.body, {
  childList: true,
  subtree: true,
  attributes: true,
  attributeFilter: ['data-state', 'aria-hidden']
});

// 清理函数
function stopAutoApproval() {
  observer.disconnect();
  console.log("🛑 自动批准已停止");
}

// 配置更新函数
function updateConfig(newConfig) {
  Object.assign(CONFIG, newConfig);
  console.log("?? 配置已更新:", CONFIG);
}

// 状态查询函数
function getStatus() {
  return {
    isActive: true,
    config: CONFIG,
    observerActive: !!observer,
    message: "实时处理模式：无冷却机制，直接处理所有检测到的dialog"
  };
}

// 导出到全局作用域（用于控制台调试）
window.claudeAutoApproval = {
  stop: stopAutoApproval,
  updateConfig: updateConfig,
  getStatus: getStatus,
  config: CONFIG,
  // 调试功能
  forceProcess: () => {
    const dialogs = document.querySelectorAll(SELECTORS.DIALOG);
    console.log("🔄 强制处理", dialogs.length, "个对话框");
    dialogs.forEach(dialog => processApprovalDialog(dialog));
  }
};

// Launch Message:
console.log("🚀 Claude Desktop Auto-Approve Script 已激活!");
console.log("⚙️ 当前配置:", CONFIG);
console.log("📋 控制命令:");
console.log("   - 停止: claudeAutoApproval.stop()");
console.log("   - 状态: claudeAutoApproval.getStatus()");
console.log("   - 配置: claudeAutoApproval.updateConfig({APPROVAL_STRATEGY: 'allow_always'})");
console.log("   - 强制处理: claudeAutoApproval.forceProcess()");
console.log("👁️ 开始监听MCP工具权限对话框...");
console.log("⚡ 实时处理模式：无冷却限制，立即处理所有检测到的dialog");

// ============================================================================
// 重要说明:
// 3. 实时处理模式：无冷却机制，立即响应所有dialog
// 4. 可通过控制台命令动态调整配置和调试
// 5. 脚本仅在当前会话有效，重启Claude Desktop需要重新运行
// 6. 对于生产环境，建议使用MCP服务器解决方案: claude-autoapprove-mcp
// ============================================================================