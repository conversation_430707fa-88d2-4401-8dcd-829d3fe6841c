﻿#允许rpc
enable-rpc=true
#允许非外部访问
rpc-listen-all=true
# 禁用IPv6, 默认:false
disable-ipv6=true

#最大同时下载数(任务数), 路由建议值: 3
max-concurrent-downloads=8
#断点续传
continue=false
#同服务器连接数
max-connection-per-server=16
#最小文件分片大小, 下载线程数上限取决于能分出多少片, 对于小文件重要
min-split-size=1M
#单文件最大线程数, 路由建议值: 5
split=16
#下载速度限制
max-overall-download-limit=0
#单文件速度限制
max-download-limit=0
#上传速度限制
max-overall-upload-limit=0
#单文件速度限制
max-upload-limit=0
#User-Agent
user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********