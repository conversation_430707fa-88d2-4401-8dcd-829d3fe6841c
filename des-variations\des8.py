# des7.py
# 纯 Python 再微调版 DES（自定义多密钥分层模式，只做加密不做解密）。

# --- 常量定义 （同 des6.py） ---
S_BOXES = [
    [
        [14,4,13,1,2,15,11,8,3,10,6,12,5,9,0,7],
        [0,15,7,4,14,2,13,1,10,6,12,11,9,5,3,8],
        [4,1,14,8,13,6,2,11,15,12,9,7,3,10,5,0],
        [15,12,8,2,4,9,1,7,5,11,3,14,10,0,6,13]
    ],
    [
        [15,1,8,14,6,11,3,4,9,7,2,13,12,0,5,10],
        [3,13,4,7,15,2,8,14,12,0,1,10,6,9,11,5],
        [0,14,7,11,10,4,13,1,5,8,12,6,9,3,2,15],
        [13,8,10,1,3,15,4,2,11,6,7,12,0,5,14,9]
    ],
    [
        [10,0,9,14,6,3,15,5,1,13,12,7,11,4,2,8],
        [13,7,0,9,3,4,6,10,2,8,5,14,12,11,15,1],
        [13,6,4,9,8,15,3,0,11,1,2,12,5,10,14,7],
        [1,10,13,0,6,9,8,7,4,15,14,3,11,5,2,12]
    ],
    [
        [7,13,14,3,0,6,9,10,1,2,8,5,11,12,4,15],
        [13,8,11,5,6,15,0,3,4,7,2,12,1,10,14,9],
        [10,6,9,0,12,11,7,13,15,1,3,14,5,2,8,4],
        [3,15,0,6,10,1,13,8,9,4,5,11,12,7,2,14]
    ],
    [
        [2,12,4,1,7,10,11,6,8,5,3,15,13,0,14,9],
        [14,11,2,12,4,7,13,1,5,0,15,10,3,9,8,6],
        [4,2,1,11,10,13,7,8,15,9,12,5,6,3,0,14],
        [11,8,12,7,1,14,2,13,6,15,0,9,10,4,5,3]
    ],
    [
        [12,1,10,15,9,2,6,8,0,13,3,4,14,7,5,11],
        [10,15,4,2,7,12,9,5,6,1,13,14,0,11,3,8],
        [9,14,15,5,2,8,12,3,7,0,4,10,1,13,11,6],
        [4,3,2,12,9,5,15,10,11,14,1,7,6,0,8,13]
    ],
    [
        [4,11,2,14,15,0,8,13,3,12,9,7,5,10,6,1],
        [13,0,11,7,4,9,1,10,14,3,5,12,2,15,8,6],
        [1,4,11,13,12,3,7,14,10,15,6,8,0,5,9,2],
        [6,11,13,8,1,4,10,7,9,5,0,15,14,2,3,12]
    ],
    [
        [13,2,8,4,6,15,11,1,10,9,3,14,5,0,12,7],
        [1,15,13,8,10,3,7,4,12,5,6,11,0,14,9,2],
        [7,11,4,1,9,12,14,2,0,6,10,13,15,3,5,8],
        [2,1,14,7,4,10,8,13,15,12,9,0,3,5,6,11]
    ]
]

PC2 = [13,16,10,23,0,4,2,27,14,5,20,9,
       22,18,11,3,25,7,15,6,26,19,12,1,
       40,51,30,36,46,54,29,39,50,44,32,47,
       43,48,38,55,33,52,45,41,49,35,28,31]

IP_TABLE = [
    57,49,41,33,25,17,9,1,59,51,43,35,27,19,11,3,
    61,53,45,37,29,21,13,5,63,55,47,39,31,23,15,7,
    56,48,40,32,24,16,8,0,58,50,42,34,26,18,10,2,
    60,52,44,36,28,20,12,4,62,54,46,38,30,22,14,6
]

FP_TABLE = [
    39,7,47,15,55,23,63,31,
    38,6,46,14,54,22,62,30,
    37,5,45,13,53,21,61,29,
    36,4,44,12,52,20,60,28,
    35,3,43,11,51,19,59,27,
    34,2,42,10,50,18,58,26,
    33,1,41,9,49,17,57,25,
    32,0,40,8,48,16,56,24
]

E_TABLE = [
    31,0,1,2,3,4, 3,4,5,6,7,8,
    7,8,9,10,11,12, 11,12,13,14,15,16,
    15,16,17,18,19,20, 19,20,21,22,23,24,
    23,24,25,26,27,28, 27,28,29,30,31,0
]

P_TABLE = [
    15,6,19,20,28,11,27,16,
    0,14,22,25,4,17,30,9,
    1,7,23,13,31,26,2,8,
    18,12,29,5,21,10,3,24
]

LOOP_TABLE = [1,1,2,2,2,2,2,2,1,2,2,2,2,2,2,1]


_HEX_TABLE = tuple(f"{i:02X}" for i in range(256))


# -------------------- 预计算：P 落点 & PC2 分组 & SP 融合表 --------------------
_P_DST_POS = [[0,0,0,0] for _ in range(8)]
for dest, src in enumerate(P_TABLE):
    m, k = divmod(src, 4)
    _P_DST_POS[m][k] = dest

_PC2_GROUPS = [PC2[i*6:(i+1)*6] for i in range(8)]

_SP_BOXES = []
for m in range(8):
    dst = _P_DST_POS[m]
    table = [0] * 64
    sb = S_BOXES[m]
    for v in range(64):
        row = (((v >> 5) & 1) << 1) | (v & 1)
        col = (v >> 1) & 0xF
        nib = sb[row][col]
        val = 0
        if (nib >> 3) & 1: val |= 1 << (31 - dst[0])
        if (nib >> 2) & 1: val |= 1 << (31 - dst[1])
        if (nib >> 1) & 1: val |= 1 << (31 - dst[2])
        if nib & 1:        val |= 1 << (31 - dst[3])
        table[v] = val
    _SP_BOXES.append(table)


# -------------------- 预计算：按字节的 64 位置换表（IP/FP） --------------------
def _build_byte_perm_tables(table):
    T8 = [[0]*256 for _ in range(8)]
    for i in range(8):
        for b in range(256):
            y = 0
            for j, src in enumerate(table):
                if src // 8 == i:
                    k = src % 8
                    if (b >> (7 - k)) & 1:
                        y |= 1 << (63 - j)
            T8[i][b] = y
    return T8

_IP8 = _build_byte_perm_tables(IP_TABLE)
_FP8 = _build_byte_perm_tables(FP_TABLE)


# -------------------- 预计算：E 扩展 32->48，按字节查表 --------------------
def _build_E48_byte_tables(E):
    T4 = [[0]*256 for _ in range(4)]
    for i in range(4):
        for b in range(256):
            y = 0
            for j, src in enumerate(E):
                if src // 8 == i:
                    k = src % 8
                    if (b >> (7 - k)) & 1:
                        y |= 1 << (47 - j)
            T4[i][b] = y
    return T4

_E48 = _build_E48_byte_tables(E_TABLE)


# -------------------- 基础工具 --------------------
def _pack_block4(s):
    s = s + '\0'*(4 - len(s))
    v = (ord(s[0]) & 0xFFFF)
    v = (v << 16) | (ord(s[1]) & 0xFFFF)
    v = (v << 16) | (ord(s[2]) & 0xFFFF)
    v = (v << 16) | (ord(s[3]) & 0xFFFF)
    return v

def _str_to_keyblocks(key):
    if not key:
        return []
    n = (len(key)+3)//4
    return [_pack_block4(key[i*4:(i+1)*4]) for i in range(n)]


# -------------------- 轮密钥生成 --------------------
def _generate_round_keys_from_bits(keyBits):
    # PC1
    key56 = [0]*56
    for i in range(7):
        base = i*8
        for j in range(8):
            key56[base+j] = keyBits[8*(7-j)+i]
    C = key56[:28]
    D = key56[28:]
    c_off = d_off = 0
    keys48 = []
    for rnd, shift in enumerate(LOOP_TABLE):
        c_off = (c_off + shift) & 0x1F
        d_off = (d_off + shift) & 0x1F
        k48 = 0
        for grp in _PC2_GROUPS:
            # 6bit
            b0 = (C[(grp[0]+c_off)%28] if grp[0]<28 else D[(grp[0]-28+d_off)%28])
            b1 = (C[(grp[1]+c_off)%28] if grp[1]<28 else D[(grp[1]-28+d_off)%28])
            b2 = (C[(grp[2]+c_off)%28] if grp[2]<28 else D[(grp[2]-28+d_off)%28])
            b3 = (C[(grp[3]+c_off)%28] if grp[3]<28 else D[(grp[3]-28+d_off)%28])
            b4 = (C[(grp[4]+c_off)%28] if grp[4]<28 else D[(grp[4]-28+d_off)%28])
            b5 = (C[(grp[5]+c_off)%28] if grp[5]<28 else D[(grp[5]-28+d_off)%28])
            v = ((((b0<<1)|b1)<<1|b2)<<1|b3)<<1|b4
            v = (v<<1)|b5
            k48 = (k48<<6)|v
        keys48.append(k48)
    return keys48

def _generate_round_keys_from_u64(key64):
    kb = [ (key64 >> (63-i)) & 1 for i in range(64) ]
    return _generate_round_keys_from_bits(kb)


# -------------------- 转换到只读 tuple-of-tuples --------------------
IP8  = tuple(tuple(row) for row in _IP8)
FP8  = tuple(tuple(row) for row in _FP8)
E48  = tuple(tuple(row) for row in _E48)
SP8  = tuple(tuple(row) for row in _SP_BOXES)
del _IP8, _FP8, _E48, _SP_BOXES


# -------------------- 缓存轮密钥 --------------------
_KEY_SCHED_CACHE = {}


# -------------------- 十六进制快速转换 --------------------
def _u64_to_hex16(x):
    t = _HEX_TABLE
    return (
        t[(x>>56)&0xFF] + t[(x>>48)&0xFF] +
        t[(x>>40)&0xFF] + t[(x>>32)&0xFF] +
        t[(x>>24)&0xFF] + t[(x>>16)&0xFF] +
        t[(x>>8 )&0xFF] + t[ x     &0xFF]
    )


# -------------------- 加密核心（完全内联 IP/FP，无分支 swap） --------------------
def _enc_block_chained_u64(block64, blocks_round_keys):
    if not blocks_round_keys:
        return block64

    # 局部化全局表，省一次全局查找
    T8 = IP8;  F8 = FP8
    E4 = E48; SP = SP8
    t0,t1,t2,t3,t4,t5,t6,t7 = SP
    e0,e1,e2,e3 = E4

    # inline IP
    b0 = T8[0][(block64>>56)&0xFF]; b1 = T8[1][(block64>>48)&0xFF]
    b2 = T8[2][(block64>>40)&0xFF]; b3 = T8[3][(block64>>32)&0xFF]
    b4 = T8[4][(block64>>24)&0xFF]; b5 = T8[5][(block64>>16)&0xFF]
    b6 = T8[6][(block64>> 8)&0xFF]; b7 = T8[7][ block64   &0xFF]
    ip = b0|b1|b2|b3|b4|b5|b6|b7

    L = (ip>>32)&0xFFFFFFFF
    R =  ip      &0xFFFFFFFF

    # 逐块密钥循环，含对每块末尾的无条件 swap
    for rk16 in blocks_round_keys:
        for k48 in rk16:
            # E 扩展 32->48
            E = (
                e0[(R>>24)&0xFF] | e1[(R>>16)&0xFF] |
                e2[(R>> 8)&0xFF] | e3[ R      &0xFF]
            )
            Et = E ^ k48
            # SP + P（已预融合）
            f = (
                t0[(Et>>42)&0x3F] ^ t1[(Et>>36)&0x3F] ^
                t2[(Et>>30)&0x3F] ^ t3[(Et>>24)&0x3F] ^
                t4[(Et>>18)&0x3F] ^ t5[(Et>>12)&0x3F] ^
                t6[(Et>> 6)&0x3F] ^ t7[ Et      &0x3F]
            )
            L, R = R, (L ^ f) & 0xFFFFFFFF
        # 对每个密钥块末尾做一次 swap，包括最后一个
        L, R = R, L

    # inline FP（前一步做过额外一次 swap，这里直接 L<<32|R）
    pre = ((L<<32)&0xFFFFFFFF00000000) | (R&0xFFFFFFFF)
    b0 = F8[0][(pre>>56)&0xFF]; b1 = F8[1][(pre>>48)&0xFF]
    b2 = F8[2][(pre>>40)&0xFF]; b3 = F8[3][(pre>>32)&0xFF]
    b4 = F8[4][(pre>>24)&0xFF]; b5 = F8[5][(pre>>16)&0xFF]
    b6 = F8[6][(pre>> 8)&0xFF]; b7 = F8[7][ pre      &0xFF]
    return b0|b1|b2|b3|b4|b5|b6|b7


# -------------------- 对外 API --------------------
def strEnc(data, firstKey, secondKey, thirdKey):
    """
    DES 加密（自定义多密钥分层，只有加密，不支持解密）
    data: 任意长度字符串，每4字符一块，不足补 '\\0'
    firstKey/secondKey/thirdKey: 每4字符一个密钥块，可多块
    返回: 每块16位大写十六进制拼接的字符串
    """
    if not data:
        return ''

    # 轮密钥缓存，避免多次重复生成
    keyt = (firstKey or '', secondKey or '', thirdKey or '')
    blocks_round_keys = _KEY_SCHED_CACHE.get(keyt)
    if blocks_round_keys is None:
        brk = []
        for k in (firstKey, secondKey, thirdKey):
            if not k:
                continue
            for kb in _str_to_keyblocks(k):
                brk.append(_generate_round_keys_from_u64(kb))
        _KEY_SCHED_CACHE[keyt] = brk
        blocks_round_keys = brk

    pack = _pack_block4
    enc  = _enc_block_chained_u64
    hx   = _u64_to_hex16
    out  = []

    n    = len(data)
    full = n // 4
    rem  = n % 4

    if not blocks_round_keys:
        # 无密钥，直接输出原数据的 16 进制
        for i in range(full):
            out.append(hx(pack(data[i*4:(i+1)*4])))
        if rem:
            out.append(hx(pack(data[full*4:])))
        return ''.join(out)

    # 普通加密流程
    for i in range(full):
        b64 = pack(data[i*4:(i+1)*4])
        out.append(hx(enc(b64, blocks_round_keys)))
    if rem:
        b64 = pack(data[full*4:])
        out.append(hx(enc(b64, blocks_round_keys)))

    return ''.join(out)


# -------------------- 自测示例 --------------------
if __name__ == '__main__':
    print(strEnc("HelloWorld", "KeyOne", "KeyTwo", "KeyThree"))
    print(strEnc("ABC", "K", None, None))
