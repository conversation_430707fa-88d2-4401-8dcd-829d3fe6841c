App Router: Partial Prerendering | Next.js

===============
[Skip to content](https://nextjs.org/learn/dashboard-app/partial-prerendering#geist-skip-nav)

[](https://vercel.com/home?utm_source=next-site&utm_medium=banner&utm_campaign=learn_dashboard-app_partial-prerendering "Go to Vercel homepage")

[![Image 1: Next.js uwu logo by SAWARATSUKI](https://nextjs.org/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1714730590%2Ffront%2Fnextjs%2Fuwu%2Fnext-uwu-logo.png&w=128&q=75)](https://nextjs.org/?uwu=true "Go to the homepage")

[](https://nextjs.org/ "Go to the homepage")

Search documentation...CtrlK Search...⌘K

[](https://vercel.com/home?utm_source=next-site&utm_medium=banner&utm_campaign=learn_dashboard-app_partial-prerendering "Go to Vercel homepage")

[![Image 2: Next.js uwu logo by SAWARATSUKI](https://nextjs.org/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1714730590%2Ffront%2Fnextjs%2Fuwu%2Fnext-uwu-logo.png&w=128&q=75)](https://nextjs.org/?uwu=true "Go to the homepage")

[](https://nextjs.org/ "Go to the homepage")

[Showcase](https://nextjs.org/showcase)[Docs](https://nextjs.org/docs "Documentation")[Blog](https://nextjs.org/blog)[Templates](https://vercel.com/templates/next.js?utm_source=next-site&utm_medium=navbar&utm_campaign=next_site_nav_templates)[Enterprise](https://vercel.com/contact/sales/nextjs?utm_source=next-site&utm_medium=navbar&utm_campaign=next_site_nav_enterprise)

Search documentation...CtrlK Search...⌘K[Deploy](https://vercel.com/new/clone?utm_source=next-site&utm_medium=banner&b=main&s=https%3A%2F%2Fgithub.com%2Fvercel%2Fvercel%2Ftree%2Fmain%2Fexamples%2Fnextjs&showOptionalTeamCreation=false&template=nextjs&teamCreateStatus=hidden&utm_campaign=learn_dashboard-app_partial-prerendering)[Learn](https://nextjs.org/learn)

Chapter 10

Partial Prerendering

[Sign in](https://nextjs.org/api/auth/authorize?slug=dashboard-app/partial-prerendering)

[Sign in to save progress](https://nextjs.org/api/auth/authorize?slug=dashboard-app/partial-prerendering)

10

Chapter 10

Partial Prerendering
====================

So far, you've learned about static and dynamic rendering, and how to stream dynamic content that depends on data. In this chapter, let's learn how to combine static rendering, dynamic rendering, and streaming in the same route with **Partial Prerendering (PPR)**.

> Partial Prerendering is an experimental feature introduced in Next.js 14. The content of this page may be updated as the feature progresses in stability. **PPR is only available with the Next.js canary releases** (`next@canary`), not in the stable version of Next.js. We do not yet recommend using Partial Prerendering in production.

To install the canary release of Next.js, run:

`pnpm install next@canary`

In this chapter...

Here are the topics we'll cover

![Image 3](https://nextjs.org/_next/static/media/layout.524baeef.svg)

What Partial Prerendering is.

![Image 4](https://nextjs.org/_next/static/media/settings-gear.49f9475a.svg)

How Partial Prerendering works.

[Static vs. Dynamic Routes](https://nextjs.org/learn/dashboard-app/partial-prerendering#static-vs-dynamic-routes)
-----------------------------------------------------------------------------------------------------------------

For most web apps built today, you either choose between static and dynamic rendering for your **entire application**, or for a **specific route**. And in Next.js, if you call a [dynamic function](https://nextjs.org/docs/app/building-your-application/routing/route-handlers#dynamic-functions) in a route (like querying your database), the _entire_ route becomes dynamic.

However, most routes are _not_ fully static or dynamic. For example, consider an [ecommerce site](https://partialprerendering.com/). You might want to statically render the majority of the product information page, but you may want to fetch the user's cart and recommended products dynamically, this allows you to show personalized content to your users.

Going back to your dashboard page, what components would you consider static vs. dynamic?

Once you're ready, click the button below to see how we would split the dashboard route:

Reveal the solution

[What is Partial Prerendering?](https://nextjs.org/learn/dashboard-app/partial-prerendering#what-is-partial-prerendering)
-------------------------------------------------------------------------------------------------------------------------

Next.js 14 introduced an experimental version of **Partial Prerendering** – a new rendering model that allows you to combine the benefits of static and dynamic rendering in the same route. For example:

![Image 5: Partially Prerendered Product Page showing static nav and product information, and dynamic cart and recommended products](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Fthinking-in-ppr.png&w=3840&q=75)![Image 6: Partially Prerendered Product Page showing static nav and product information, and dynamic cart and recommended products](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Fthinking-in-ppr.png&w=3840&q=75)
When a user visits a route:

*   A static route shell that includes the navbar and product information is served, ensuring a fast initial load.
*   The shell leaves holes where dynamic content like the cart and recommended products will load in asynchronously.
*   The async holes are streamed in parallel, reducing the overall load time of the page.

### It's time to take a quiz!

Test your knowledge and see what you've just learned.

What are the holes in the context of Partial Prerendering?

A

Locations where JavaScript is disabled

B

Locations where dynamic content will load asynchronously

C

Locations where third-party scripts are loaded

Check Answer

[How does Partial Prerendering work?](https://nextjs.org/learn/dashboard-app/partial-prerendering#how-does-partial-prerendering-work)
-------------------------------------------------------------------------------------------------------------------------------------

Partial Prerendering uses React's [Suspense](https://react.dev/reference/react/Suspense) (which you learned about in the previous chapter) to defer rendering parts of your application until some condition is met (e.g. data is loaded).

The Suspense fallback is embedded into the initial HTML file along with the static content. At build time (or during revalidation), the static content is **prerendered** to create a static shell. The rendering of dynamic content is **postponed** until the user requests the route.

Wrapping a component in Suspense doesn't make the component itself dynamic, but rather Suspense is used as a boundary between your static and dynamic code.

Let's see how you can implement PPR in your dashboard route.

[Implementing Partial Prerendering](https://nextjs.org/learn/dashboard-app/partial-prerendering#implementing-partial-prerendering)
----------------------------------------------------------------------------------------------------------------------------------

Enable PPR for your Next.js app by adding the [`ppr`](https://rc.nextjs.org/docs/app/api-reference/next-config-js/ppr) option to your `next.config.ts` file:

next.config.ts

```
import type { NextConfig } from 'next';
 
const nextConfig: NextConfig = {
  experimental: {
    ppr: 'incremental'
  }
};
 
export default nextConfig;
```

The `'incremental'` value allows you to adopt PPR for specific routes.

Next, add the `experimental_ppr` segment config option to your dashboard layout:

/app/dashboard/layout.tsx

```
import SideNav from '@/app/ui/dashboard/sidenav';
 
export const experimental_ppr = true;
 
// ...
```

That's it. You may not see a difference in your application in development, but you should notice a performance improvement in production. Next.js will prerender the static parts of your route and defer the dynamic parts until the user requests them.

The great thing about Partial Prerendering is that you don't need to change your code to use it. As long as you're using Suspense to wrap the dynamic parts of your route, Next.js will know which parts of your route are static and which are dynamic.

We believe PPR has the potential to [become the default rendering model for web applications](https://vercel.com/blog/partial-prerendering-with-next-js-creating-a-new-default-rendering-model), bringing together the best of static site and dynamic rendering. However, it is still experimental. We hope to stabilize it in the future and make it the default way of building with Next.js.

You can now revert these changes and move on to the next chapter.

[Summary](https://nextjs.org/learn/dashboard-app/partial-prerendering#summary)
------------------------------------------------------------------------------

To recap, you've done a few things to optimize data fetching in your application so far:

1.   Created a database in the same region as your application code to reduce latency between your server and database.
2.   Fetched data on the server with React Server Components. This allows you to keep expensive data fetches and logic on the server, reduces the client-side JavaScript bundle, and prevents your database secrets from being exposed to the client.
3.   Used SQL to only fetch the data you needed, reducing the amount of data transferred for each request and the amount of JavaScript needed to transform the data in-memory.
4.   Parallelize data fetching with JavaScript - where it made sense to do so.
5.   Implemented Streaming to prevent slow data requests from blocking your whole page, and to allow the user to start interacting with the UI without waiting for everything to load.
6.   Move data fetching down to the components that need it, thus isolating which parts of your routes should be dynamic.

In the next chapter, we'll look at two common patterns you might need to implement when fetching data: search and pagination.

10

You've Completed Chapter 10
---------------------------

You've learned about Partial Prerendering, a new rendering model introduced in Next.js 14.

Next Up

11: Adding Search and Pagination

Learn how to implement search and pagination with Next.js APIs.

[Start Chapter 11](https://nextjs.org/learn/dashboard-app/adding-search-and-pagination)

Was this helpful?

supported.

Send

[](https://vercel.com/home?utm_source=next-site&utm_medium=footer&utm_campaign=next-website "Go to the Vercel website")

[](https://github.com/vercel/next.js)

* * *

[](https://x.com/nextjs)

* * *

[](https://bsky.app/profile/nextjs.org)

#### Resources

[Docs](https://nextjs.org/docs)[Support Policy](https://nextjs.org/support-policy)[Learn](https://nextjs.org/learn)[Showcase](https://nextjs.org/showcase)[Blog](https://nextjs.org/blog)[Team](https://nextjs.org/team)[Analytics](https://vercel.com/analytics?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_partial-prerendering)[Next.js Conf](https://nextjs.org/conf)[Previews](https://vercel.com/products/previews?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_partial-prerendering)

#### More

[Next.js Commerce](https://vercel.com/templates/next.js/nextjs-commerce?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_partial-prerendering)[Contact Sales](https://vercel.com/contact/sales?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_partial-prerendering)[Community](https://community.vercel.com/)[GitHub](https://github.com/vercel/next.js)[Releases](https://github.com/vercel/next.js/releases)[Telemetry](https://nextjs.org/telemetry)[Governance](https://nextjs.org/governance)

#### About Vercel

[Next.js + Vercel](https://vercel.com/solutions/nextjs?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_partial-prerendering)[Open Source Software](https://vercel.com/oss?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_partial-prerendering)[GitHub](https://github.com/vercel)[Bluesky](https://bsky.app/profile/vercel.com)[X](https://x.com/vercel)

#### Legal

[Privacy Policy](https://vercel.com/legal/privacy-policy)Cookie Preferences

#### Subscribe to our newsletter

Stay updated on new releases and features, guides, and case studies.

Subscribe

© 2025 Vercel, Inc.

[](https://github.com/vercel/next.js)

* * *

[](https://x.com/nextjs)

* * *

[](https://bsky.app/profile/nextjs.org)