#!/usr/bin/env python3
"""
PDF文档视觉内容嵌入处理程序
使用Jina Embeddings v4 API处理PDF文档中的视觉信息（图表、表格、图像等）
并生成高质量的多模态嵌入向量

Jina Embeddings v4在处理visually rich content方面表现优异，
特别适合处理包含大量图表、表格、混合媒体的技术文档和报告。
"""

import argparse
import base64
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

import numpy as np
import requests
from requests.exceptions import RequestException


class PDFEmbeddingProcessor:
    """PDF嵌入处理器"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.api_url = "https://api.jina.ai/v1/embeddings"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
    
    def validate_pdf(self, pdf_path: Path) -> None:
        """验证PDF文件"""
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF文件不存在: {pdf_path}")
        
        if not pdf_path.suffix.lower() == '.pdf':
            raise ValueError(f"文件不是PDF格式: {pdf_path}")
        
        # 检查文件大小（8MB限制）
        file_size_mb = pdf_path.stat().st_size / (1024 * 1024)
        if file_size_mb > 8:
            raise ValueError(f"PDF文件超过8MB限制: {file_size_mb:.2f}MB")
        
        print(f"✓ PDF文件验证通过: {pdf_path.name} ({file_size_mb:.2f}MB)")
    
    def encode_pdf_to_base64(self, pdf_path: Path) -> str:
        """将PDF文件编码为base64"""
        print(f"正在编码PDF文件...")
        with open(pdf_path, 'rb') as pdf_file:
            pdf_bytes = pdf_file.read()
            base64_encoded = base64.b64encode(pdf_bytes).decode('utf-8')
        print(f"✓ PDF编码完成，编码后大小: {len(base64_encoded) / 1024:.2f}KB")
        return base64_encoded
    
    def process_pdf(self, pdf_path: Path, task: str = "retrieval", 
                   output_dims: int = 2048, multi_vector: bool = False) -> Dict[str, Any]:
        """处理PDF文件并获取嵌入
        
        对于包含图表、表格等视觉内容的PDF文档，使用retrieval任务类型
        可以更好地捕获文档的视觉特征。
        """
        # 验证PDF
        self.validate_pdf(pdf_path)
        
        # 编码PDF
        base64_pdf = self.encode_pdf_to_base64(pdf_path)
        
        # 构建请求数据
        request_data = {
            "model": "jina-embeddings-v4",
            "task": task,
            "input": {
                "pdf": f"data:application/pdf;base64,{base64_pdf}"
            },
            "return_multivector": True,
            # "late_chunking": True  # for text inputs
        }
        
        # 发送请求
        print(f"\n正在调用Jina Embeddings API处理PDF视觉内容...")
        print(f"- 任务类型: {task} {'(推荐用于视觉内容)' if task == 'retrieval' else ''}")
        print(f"- 输出维度: {output_dims}")
        print(f"- 多向量模式: {'开启' if multi_vector else '关闭'}")
        if multi_vector:
            print(f"  (多向量模式可以更好地捕获文档中不同位置的视觉特征)")
        
        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=request_data,
                timeout=60
            )
            response.raise_for_status()
            
            print("✓ API调用成功")
            return response.json()
            
        except RequestException as e:
            print(f"✗ API调用失败: {e}")
            if hasattr(e.response, 'text'):
                print(f"错误详情: {e.response.text}")
            raise
    
    def analyze_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析响应数据结构"""
        print("\n=== 响应数据分析 ===")
        
        # 基本信息
        print(f"\n基本信息:")
        print(f"- 对象类型: {response_data.get('object', 'N/A')}")
        print(f"- 使用模型: {response_data.get('model', 'N/A')}")
        
        # Token使用情况
        usage = response_data.get('usage', {})
        print(f"\nToken使用:")
        print(f"- Prompt tokens: {usage.get('prompt_tokens', 'N/A')}")
        print(f"- Total tokens: {usage.get('total_tokens', 'N/A')}")
        
        # 嵌入数据分析
        data_list = response_data.get('data', [])
        print(f"\n嵌入数据:")
        print(f"- 数据条目数: {len(data_list)}")
        
        analysis = {
            'num_embeddings': len(data_list),
            'embeddings_info': []
        }
        
        for i, item in enumerate(data_list):
            embedding = item.get('embedding', [])
            
            # 检查是否为多向量嵌入
            if isinstance(embedding[0], list) if embedding else False:
                # 多向量嵌入
                num_vectors = len(embedding)
                vector_dim = len(embedding[0]) if embedding else 0
                embedding_array = np.array(embedding)
                print(f"\n  嵌入 #{i}:")
                print(f"  - 类型: 多向量嵌入")
                print(f"  - 形状: {embedding_array.shape} (vectors × dimensions)")
                print(f"  - 向量数量: {num_vectors}")
                print(f"  - 每个向量维度: {vector_dim}")
                
                analysis['embeddings_info'].append({
                    'index': i,
                    'type': 'multi-vector',
                    'shape': embedding_array.shape,
                    'num_vectors': num_vectors,
                    'vector_dim': vector_dim
                })
            else:
                # 单向量嵌入
                vector_dim = len(embedding)
                print(f"\n  嵌入 #{i}:")
                print(f"  - 类型: 单向量嵌入")
                print(f"  - 维度: {vector_dim}")
                print(f"  - 数据类型: {type(embedding[0]).__name__ if embedding else 'N/A'}")
                
                # 计算一些统计信息
                if embedding:
                    embedding_array = np.array(embedding)
                    print(f"  - 最小值: {embedding_array.min():.6f}")
                    print(f"  - 最大值: {embedding_array.max():.6f}")
                    print(f"  - 均值: {embedding_array.mean():.6f}")
                    print(f"  - 标准差: {embedding_array.std():.6f}")
                
                analysis['embeddings_info'].append({
                    'index': i,
                    'type': 'single-vector',
                    'dimension': vector_dim,
                    'stats': {
                        'min': float(embedding_array.min()),
                        'max': float(embedding_array.max()),
                        'mean': float(embedding_array.mean()),
                        'std': float(embedding_array.std())
                    } if embedding else None
                })
        
        return analysis
    
    def save_results(self, pdf_path: Path, response_data: Dict[str, Any], 
                    analysis: Dict[str, Any]) -> Path:
        """保存结果到本地"""
        # 创建输出目录
        output_dir = Path("pdf_embeddings_output")
        output_dir.mkdir(exist_ok=True)
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = f"{pdf_path.stem}_{timestamp}"
        
        # 保存完整响应
        response_file = output_dir / f"{base_name}_response.json"
        with open(response_file, 'w', encoding='utf-8') as f:
            json.dump(response_data, f, indent=2, ensure_ascii=False)
        print(f"\n✓ 完整响应已保存到: {response_file}")
        
        # 保存分析结果
        analysis_file = output_dir / f"{base_name}_analysis.json"
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        print(f"✓ 分析结果已保存到: {analysis_file}")
        
        # 保存嵌入向量（numpy格式）
        embeddings_data = response_data.get('data', [])
        for i, item in enumerate(embeddings_data):
            embedding = item.get('embedding', [])
            if embedding:
                # 保存为.npy格式
                npy_file = output_dir / f"{base_name}_embedding_{i}.npy"
                np.save(npy_file, np.array(embedding))
                print(f"✓ 嵌入向量 #{i} 已保存到: {npy_file}")
        
        # 创建摘要文件
        summary_file = output_dir / f"{base_name}_summary.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"PDF嵌入处理摘要\n")
            f.write(f"={'=' * 50}\n\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"PDF文件: {pdf_path.name}\n")
            f.write(f"文件大小: {pdf_path.stat().st_size / 1024:.2f}KB\n\n")
            f.write(f"API响应:\n")
            f.write(f"- 模型: {response_data.get('model', 'N/A')}\n")
            f.write(f"- Token使用: {response_data.get('usage', {}).get('total_tokens', 'N/A')}\n")
            f.write(f"- 嵌入数量: {len(embeddings_data)}\n\n")
            
            for info in analysis['embeddings_info']:
                f.write(f"嵌入 #{info['index']}:\n")
                f.write(f"- 类型: {info['type']}\n")
                if info['type'] == 'single-vector':
                    f.write(f"- 维度: {info['dimension']}\n")
                    if info['stats']:
                        f.write(f"- 统计信息:\n")
                        f.write(f"  - 最小值: {info['stats']['min']:.6f}\n")
                        f.write(f"  - 最大值: {info['stats']['max']:.6f}\n")
                        f.write(f"  - 均值: {info['stats']['mean']:.6f}\n")
                        f.write(f"  - 标准差: {info['stats']['std']:.6f}\n")
                else:
                    f.write(f"- 形状: {info['shape']}\n")
                    f.write(f"- 向量数量: {info['num_vectors']}\n")
                    f.write(f"- 向量维度: {info['vector_dim']}\n")
                f.write("\n")
        
        print(f"✓ 处理摘要已保存到: {summary_file}")
        
        return output_dir


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="使用Jina Embeddings v4 API处理PDF文档",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  # 处理包含图表的技术报告
  %(prog)s technical_report.pdf --api-key YOUR_API_KEY
  
  # 处理包含表格和图表的研究论文（使用多向量模式）
  %(prog)s research_paper.pdf --api-key YOUR_API_KEY --multi-vector
  
  # 处理视觉内容丰富的PDF，使用较小的输出维度
  %(prog)s visual_document.pdf --api-key YOUR_API_KEY --output-dims 512
  
注意：Jina Embeddings v4特别擅长处理包含表格、图表、流程图等视觉元素的PDF文档
        """
    )
    
    parser.add_argument('pdf_path', type=str, help='PDF文件路径')
    parser.add_argument('--api-key', type=str, required=True, 
                       help='Jina API密钥（或设置JINA_API_KEY环境变量）')
    parser.add_argument('--task', type=str, default='retrieval.passage',
                       choices=['text-matching', 'retrieval.passage'],
                       help='任务类型（默认: retrieval，适合PDF视觉内容处理）')
    parser.add_argument('--output-dims', type=int, default=2048,
                       help='输出嵌入维度（默认: 2048）')
    parser.add_argument('--multi-vector', action='store_true',
                       help='启用多向量嵌入输出')
    
    args = parser.parse_args()
    
    # 从环境变量获取API密钥（如果未在参数中提供）
    api_key = args.api_key or os.environ.get('JINA_API_KEY')
    if not api_key:
        print("错误: 请提供API密钥（通过--api-key参数或JINA_API_KEY环境变量）")
        sys.exit(1)
    
    # 处理PDF
    try:
        pdf_path = Path(args.pdf_path)
        processor = PDFEmbeddingProcessor(api_key)
        
        print(f"\n开始处理PDF文档: {pdf_path}")
        print("=" * 60)
        
        # 处理PDF并获取嵌入
        response_data = processor.process_pdf(
            pdf_path, 
            task=args.task,
            output_dims=args.output_dims,
            multi_vector=args.multi_vector
        )
        
        # 分析响应
        analysis = processor.analyze_response(response_data)
        
        # 保存结果
        output_dir = processor.save_results(pdf_path, response_data, analysis)
        
        print(f"\n{'=' * 60}")
        print(f"✓ 处理完成！所有结果已保存到: {output_dir}")
        
    except Exception as e:
        print(f"\n✗ 处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
