{"rules": [{"regexp": "g:([a-zA-Z0-9.-]*)", "link": "http://google.com/search?q=$1", "cssclass": ""}, {"regexp": "gh:([a-zA-Z0-9./-]*)", "link": "http://github.com/$1", "cssclass": ""}, {"regexp": "@([a-zA-Z0-9]*)", "link": "http://twitter.com/$1", "cssclass": ""}, {"regexp": "^\\^([0-9]{13})-?[0-9a-zA-Z\\-]*$", "link": "anki-card://$1", "cssclass": "linkified-anki-theme"}, {"regexp": " \\^([0-9]{13})-?[0-9a-zA-Z\\-]*$", "link": "anki-card://$1", "cssclass": "linkified-anki-theme"}]}