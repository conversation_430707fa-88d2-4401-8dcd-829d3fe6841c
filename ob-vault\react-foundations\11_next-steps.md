React Foundations: Next Steps | Next.js

===============

[Skip to content](https://nextjs.org/learn/react-foundations/next-steps#geist-skip-nav)

11

Chapter 11

Next Steps
==========

Congratulations on creating your first Next.js application!

To summarize, you explored the foundational knowledge for React and Next.js, and you migrated from a simple React Application to a Next.js application.

[What's next?](https://nextjs.org/learn/react-foundations/next-steps#whats-next)
--------------------------------------------------------------------------------

### [Continue learning React](https://nextjs.org/learn/react-foundations/next-steps#continue-learning-react)

Over the years, many courses, videos, and articles have been created to help developers learn React. While it's hard to recommend resources that will fit your learning style, one invaluable reference is the [React Documentation](https://react.dev/) which contains interactive sandboxes to help you practice the topics.

### [Learn Next.js by building a dashboard app](https://nextjs.org/learn/react-foundations/next-steps#learn-nextjs-by-building-a-dashboard-app)

Continue learning Next.js by [creating a dashboard app](https://nextjs.org/learn/dashboard-app) - this course will introduce you to the **main** Next.js features and get you practicing by building a more complex project.

You've Completed the Course!
----------------------------

Congratulations on finishing the React Foundations for Next.js course!

Now that you've completed the final chapter, you're ready for the next steps.

[Start Next.js course](https://nextjs.org/learn/dashboard-app)
