{"cells": [{"cell_type": "markdown", "id": "0979619b", "metadata": {}, "source": ["7/25 16:08"]}, {"cell_type": "code", "execution_count": null, "id": "514672fc-0bbb-4577-b337-590a6e272916", "metadata": {}, "outputs": [], "source": ["!pip install transformers>=4.52.0 torch>=2.6.0 peft>=0.15.2 torchvision pillow"]}, {"cell_type": "code", "execution_count": 1, "id": "b83a2e07-fec6-4279-b1f6-4753acd73699", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'"]}, {"cell_type": "code", "execution_count": null, "id": "4cd9a9cb-26fe-4ee2-9bf0-53ca4d7ef89b", "metadata": {"scrolled": true}, "outputs": [], "source": ["from transformers import AutoModel\n", "import torch\n", "\n", "# Initialize the model\n", "model = AutoModel.from_pretrained(\n", "    \"jinaai/jina-embeddings-v4\",\n", "    trust_remote_code=True,\n", "    torch_dtype=torch.float16\n", ")\n", "\n", "model.to(\"cuda\")"]}, {"cell_type": "code", "execution_count": null, "id": "0c1c63b4-5309-4d33-b3ab-dd5e0a08bd7a", "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [], "source": ["# ========================\n", "# 1. Retrieval Task\n", "# ========================\n", "# Configure truncate_dim, max_length (for texts), max_pixels (for images), vector_type, batch_size in the encode function if needed\n", "\n", "# Encode query\n", "query_embeddings = model.encode_text(\n", "    texts=[\"Overview of climate change impacts on coastal cities\"],\n", "    task=\"retrieval\",\n", "    prompt_name=\"query\",\n", ")\n", "\n", "# Encode passage (text)\n", "passage_embeddings = model.encode_text(\n", "    texts=[\n", "        \"Climate change has led to rising sea levels, increased frequency of extreme weather events...\"\n", "    ],\n", "    task=\"retrieval\",\n", "    prompt_name=\"passage\",\n", ")\n", "\n", "# Encode image/document\n", "image_embeddings = model.encode_image(\n", "    images=[\"https://i.ibb.co/nQNGqL0/beach1.jpg\"],\n", "    task=\"retrieval\",\n", ")"]}, {"cell_type": "code", "execution_count": 13, "id": "d6d3d55c-3535-4892-9308-8af9f6deba0f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Encoding texts...: 100%|██████████| 1/1 [00:00<00:00, 12.27it/s]\n", "Encoding images...: 100%|██████████| 1/1 [00:01<00:00,  1.43s/it]\n"]}], "source": ["# ========================\n", "# 1. Retrieval Task (2)\n", "# ========================\n", "import requests\n", "from PIL import Image\n", "import io\n", "\n", "def download_image(url):\n", "    \"\"\"下载图片并返回PIL Image对象\"\"\"\n", "    response = requests.get(url, headers={\"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64)\"})\n", "    response.raise_for_status()\n", "    return Image.open(io.BytesIO(response.content))\n", "\n", "# Configure truncate_dim, max_length (for texts), max_pixels (for images), vector_type, batch_size in the encode function if needed\n", "\n", "# Encode query\n", "query_embeddings = model.encode_text(\n", "    texts=[\n", "        'How much percentage of Germanys population died in the 2nd World War?',\n", "        'How many million tons CO2 were captured from Gas processing in 2018?',\n", "        'What is the average CO2 emission of someone in Japan?'\n", "    ],\n", "    task=\"retrieval\",\n", "    prompt_name=\"query\",\n", "    return_multivector=True,\n", ")\n", "\n", "# Encode image/document\n", "image_embeddings = model.encode_image(\n", "    images=[\n", "        download_image('https://wiki-upload.yayeah.xyz/wikipedia/commons/3/35/Human_losses_of_world_war_two_by_country.png'),\n", "        download_image('https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/7/76/20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg/2560px-20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg.png'),\n", "        download_image('https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/f/f3/20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg/2880px-20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg.png')\n", "    ],\n", "    task=\"retrieval\",\n", "    return_multivector=True,\n", ")"]}, {"cell_type": "code", "execution_count": 14, "id": "b1e25b28-d67b-419f-88e4-88c64af8fa69", "metadata": {}, "outputs": [{"data": {"text/plain": ["(torch.<PERSON><PERSON>([372, 128]), torch.<PERSON><PERSON>([18, 128]), 3, 3)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["image_embeddings[0].shape, query_embeddings[0].shape, len(image_embeddings), len(query_embeddings)"]}, {"cell_type": "code", "execution_count": 15, "id": "e3641a45-50f0-48c8-b70a-8651bc42a21c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query-Document Similarity Scores:\n", "tensor([[12.4328,  4.9845,  8.2872],\n", "        [ 6.8097, 13.8597,  9.0601],\n", "        [ 6.8614,  7.7098,  9.2222]])\n"]}], "source": ["import torch.nn.functional as F\n", "\n", "# 归一化所有向量（确保余弦相似度计算正确）\n", "def normalize_embeddings(embeddings_list):\n", "    return [F.normalize(emb, p=2, dim=1) for emb in embeddings_list]\n", "\n", "# 归一化查询和文档嵌入\n", "query_embeddings = normalize_embeddings(query_embeddings)\n", "image_embeddings = normalize_embeddings(image_embeddings)\n", "\n", "# 计算相似度矩阵\n", "scores = torch.zeros(len(query_embeddings), len(image_embeddings))\n", "\n", "for i, q_emb in enumerate(query_embeddings):\n", "    for j, d_emb in enumerate(image_embeddings):\n", "        # 计算查询向量和文档向量间的余弦相似度\n", "        # (q_emb: [m, 128], d_emb: [n, 128] -> sim_matrix: [m, n])\n", "        sim_matrix = q_emb @ d_emb.T\n", "        \n", "        # Late interaction: 取每个查询向量的最大相似度\n", "        max_sim_per_query = torch.max(sim_matrix, dim=1).values\n", "        \n", "        # 求和得到最终分数\n", "        scores[i, j] = torch.sum(max_sim_per_query)\n", "\n", "# 打印相似度矩阵\n", "print(\"Query-Document Similarity Scores:\")\n", "print(scores)"]}, {"cell_type": "code", "execution_count": null, "id": "b0969f29-2041-4923-a233-88f2dc792e19", "metadata": {}, "outputs": [], "source": ["# ========================\n", "# 2. Text Matching Task\n", "# ========================\n", "texts = [\n", "    \"غروب جميل على الشاطئ\",  # Arabic\n", "    \"海滩上美丽的日落\",  # Chinese\n", "    \"Un beau coucher de soleil sur la plage\",  # French\n", "    \"Ein wunderschöner Sonnenuntergang am Strand\",  # German\n", "    \"Ένα όμορφο ηλιοβασίλεμα πάνω από την παραλία\",  # Greek\n", "    \"समुद्र तट पर एक खूबसूरत सूर्यास्त\",  # Hindi\n", "    \"Un bellissimo tramonto sulla spiaggia\",  # Italian\n", "    \"浜辺に沈む美しい夕日\",  # Japanese\n", "    \"해변 위로 아름다운 일몰\",  # Korean\n", "]\n", "\n", "text_embeddings = model.encode_text(texts=texts, task=\"text-matching\")"]}, {"cell_type": "code", "execution_count": null, "id": "c7b29d4c-64d6-402f-8c2f-d1251ef2b081", "metadata": {}, "outputs": [], "source": ["# ========================\n", "# 3. Code Understanding Task\n", "# ========================\n", "\n", "# Encode query\n", "query_embedding = model.encode_text(\n", "    texts=[\"Find a function that prints a greeting message to the console\"],\n", "    task=\"code\",\n", "    prompt_name=\"query\",\n", ")\n", "\n", "# Encode code\n", "code_embeddings = model.encode_text(\n", "    texts=[\"def hello_world():\\n    print('Hello, World!')\"],\n", "    task=\"code\",\n", "    prompt_name=\"passage\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ba7f5730-6265-437a-b129-3c4c060065a5", "metadata": {}, "outputs": [], "source": ["# ========================\n", "# 4. Use multivectors\n", "# ========================\n", "\n", "multivector_embeddings = model.encode_text(\n", "    texts=texts,\n", "    task=\"retrieval\",\n", "    prompt_name=\"query\",\n", "    return_multivector=True,\n", ")\n", "\n", "images = [\"https://i.ibb.co/nQNGqL0/beach1.jpg\", \"https://i.ibb.co/r5w8hG8/beach2.jpg\"]\n", "multivector_image_embeddings = model.encode_image(\n", "    images=images,\n", "    task=\"retrieval\",\n", "    return_multivector=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ad18109a-0659-41f6-8355-e120ceff1ca1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2d71f5a1-6433-4d91-a873-23560670d107", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}