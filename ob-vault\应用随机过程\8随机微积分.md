## 第八章 随机微积分
作者 郭旭
统计学院
北京师范大学

***

## 第8章 随机微积分
本章的目的是引入关于Brown运动的积分, 讨论其性质并给出在随机分析及金融学中有着重要应用的Itô公式.

### 8.1 关于随机游动的积分
我们从讨论关于简单的随机游动的积分开始.设$X_1, X_2, \dots$, 是独立的随机变量, $P(X_i = 1) = P(X_i = -1) = \frac{1}{2}$, 令$S_n$表示相应的游动
$$
S_n = X_1 + X_2 + \dots + X_n.
$$
我们可以这样看这组独立随机变量, $X_n$为第$n$次公平赌博的结果($X_n = 1$为赢1元, $X_n = -1$为输掉1元). $\mathcal{F}_n = \sigma(X_1, \dots, X_n)$(由$\{X_i, 1 \le i \le n\}$生成的$\sigma$代数), 也可以理解为包含$X_1, \dots, X_n$的信息.令$B_n$是$\mathcal{F}_{n-1}$可测的随机变量序列, 比如它表示第$n$次赌博时所下赌注, 则它只能利用第$n-1$次及以前的信息, 而不能利用第$n$次赌博的结果.于是到时刻$n$的收益$Z_n$为
$$
Z_n = \sum_{i=1}^n B_i X_i = \sum_{i=1}^n B_i(S_i - S_{i-1}) = \sum_{i=1}^n B_i \Delta S_i,
$$
这里$\Delta S_i = S_i - S_{i-1}, S_0=0$.我们称$Z_n$为$B_n$关于$S_n$的积分. 容易看出$\{Z_n\}$是关于$\mathcal{F}_n$的鞅, 即, 若$m < n$, 则
$$
E(Z_n|\mathcal{F}_m) = Z_m.
$$
特别地, $E(Z_n)=0$.此外, 如果假定$E(B_i^2) < \infty$, 则
$$
\text{var}(Z_n) = E(Z_n^2) = \sum_{i=1}^n E(B_i^2).
$$
事实上,
$$
Z_n^2 = \sum_{i=1}^n B_i^2 X_i^2 + 2 \sum_{1 \le i < j \le n} B_i B_j X_i X_j.
$$
再注意到$X_i^2 = 1$, 如果$i < j$, 则$B_i, X_i, B_j$都是$\mathcal{F}_{j-1}$可测的, 且$X_j$独立于$\mathcal{F}_{j-1}$, 于是由定理1.12, 得
$$
E(B_i B_j X_i X_j) = E[E(B_i B_j X_i X_j | \mathcal{F}_{j-1})] = E[B_i B_j X_i E(X_j)] = 0.
$$

### 8.2 关于Brown运动的积分
本节定义关于Brown运动的积分$\int_0^T X(t)dB(t)$(或简记为$\int XdB$), 这里$\{B(t)\}$是一维标准Brown运动, 有时也记为$\{W_t\}$. 首先考虑一个非随机的简单过程$X(t)$, 即$X(t)$是一个简单函数(不依赖于$B(t)$).由简单函数的定义, 存在$[0, T]$的分割$0=t_0 < t_1 < \dots < t_n = T$及常数$c_0, c_1, \dots, c_{n-1}$, 使得
$$
X(t) = 
\begin{cases}
c_0, & \text{如果}t=0, \\
c_i, & \text{如果}t_i < t \le t_{i+1}, i = 0, 1, \dots, n-1,
\end{cases}
$$
或表示为
$$
X(t) = c_0 I_0(t) + \sum_{i=0}^{n-1} c_i I_{(t_i, t_{i+1}]}(t).
\tag{1}
$$
于是, 可定义其积分为
$$
\int_0^T X(t)dB(t) = \sum_{i=0}^{n-1} c_i(B(t_{i+1}) - B(t_i)).
\tag{2}
$$
由Brown运动的独立增量性可知, 公式(2)所定义的积分是Gauss分布的随机变量, 其均值为0, 方差为
$$
\begin{aligned}
\text{var}(\int XdB) &= E\left(\sum_{i=0}^{n-1} c_i(B(t_{i+1}) - B(t_i))\right)^2 \\
&= \sum_{i=0}^{n-1} c_i^2 E[(B(t_{i+1}) - B(t_i))^2] \\
&= \sum_{i=0}^{n-1} c_i^2(t_{i+1} - t_i).
\end{aligned}
\tag{3}
$$
用取极限的方法可以将这一定义推广到一般的非随机函数$X(t)$.但是我们要定义的是随机过程的积分, 因此将简单函数中的常数$c_i$用随机变量$\xi_i$来代替, 并要求$\xi_i$是$\mathcal{F}_{t_i}$可测的. 这里$\mathcal{F}_t = \sigma\{B(u), 0 \le u \le t\}$.于是,由Brown运动的鞅性质得
$$
E[\xi_i(B(t_{i+1}) - B(t_i))|\mathcal{F}_{t_i}] = \xi_i E[(B(t_{i+1}) - B(t_i))|\mathcal{F}_{t_i}] = 0,
\tag{4}
$$
因此
$$
E[\xi_i(B(t_{i+1}) - B(t_i))] = 0.
$$

**定义8.1**
设$\{X(t), 0 \le t \le T\}$是一个简单随机过程, 即存在$[0, T]$的分割$0=t_0 < t_1 < \dots < t_n = T$, 随机变量$\xi_0, \xi_1, \dots, \xi_{n-1}$使得$\xi_0$是常数, $\xi_i$依赖于$B(t), t \le t_i$, 但不依赖于$B(t), t > t_i, i = 0, 1, \dots, n-1$, 并且
$$
X(t) = \xi_0 I_0(t) + \sum_{i=0}^{n-1} \xi_i I_{(t_i, t_{i+1}]}(t).
\tag{5}
$$
此时, Itô积分$\int_0^T XdB$定义为
$$
\int_0^T X(t)dB(t) = \sum_{i=0}^{n-1} \xi_i(B(t_{i+1}) - B(t_i)).
\tag{6}
$$

简单过程的积分是一个随机变量, 满足下述性质:
**性质8.1**
(1) **线性** 如果$X(t), Y(t)$是简单过程, 则
$$
\int_0^T (\alpha X(t) + \beta Y(t))dB(t) = \alpha \int_0^T X(t)dB(t) + \beta \int_0^T Y(t)dB(t),
$$
这里$\alpha, \beta$是常数.
(2)
$$
\int_0^T I_{[a,b]}(t)dB(t) = B(b) - B(a),
$$
其中$I_{[a,b]}(t)$是区间$[a,b]$的示性函数.
(3) **零均值性** 如果$E[\xi_i^2] < \infty, (i=0, 1, \dots, n-1)$, 则
$$
E[\int_0^T X(t)dB(t)] = 0.
$$
(4) **等距性** 如果$E[\xi_i^2] < \infty, (i=0, 1, \dots, n-1)$, 则
$$
E\left(\int_0^T X(t)dB(t)\right)^2 = \int_0^T E(X^2(t))dt.
$$

**证明** 性质(1),(2)和(3)是简单的, 读者可自行证之, 这里只证明性质(4). 利用Cauchy-Schwarz不等式, 得到
$$
E[|\xi_i(B(t_{i+1}) - B(t_i))|] \le \sqrt{E(\xi_i^2)E(B(t_{i+1}) - B(t_i))^2} < \infty,
$$
于是
$$
\begin{aligned}
\text{var}(\int_0^T XdB) &= E\left(\sum_{i=0}^{n-1} \xi_i(B(t_{i+1}) - B(t_i))\right)^2 \\
&= E\left(\sum_{i=0}^{n-1} \xi_i(B(t_{i+1}) - B(t_i)) \sum_{j=0}^{n-1} \xi_j(B(t_{j+1}) - B(t_j))\right) \\
&= \sum_{i=0}^{n-1} E[\xi_i^2(B(t_{i+1}) - B(t_i))^2] + \\
&\quad 2\sum_{i<j} E[\xi_i \xi_j(B(t_{i+1}) - B(t_i))(B(t_{j+1}) - B(t_j))].
\end{aligned}
\tag{7}
$$
由Brown运动的独立增量性以及关于$\xi_i$的假定, 利用定理1.12(1), 有
$$
E[\xi_i \xi_j(B(t_{i+1}) - B(t_i))(B(t_{j+1}) - B(t_j))] = 0,
$$
所以, 式(7)中的最后一项为零. 由Brown运动的独立增量性, 得
$$
\begin{aligned}
\text{var}(\int_0^T XdB) &= \sum_{i=0}^{n-1} E[\xi_i^2(B(t_{i+1}) - B(t_i))^2] \\
&= \sum_{i=0}^{n-1} E[E(\xi_i^2(B(t_{i+1}) - B(t_i))^2|\mathcal{F}_{t_i})] \\
&= \sum_{i=0}^{n-1} E[\xi_i^2 E((B(t_{i+1}) - B(t_i))^2|\mathcal{F}_{t_i})] \\
&= \sum_{i=0}^{n-1} E[\xi_i^2(t_{i+1} - t_i)] \\
&= \int_0^T E[X^2(t)]dt.
\end{aligned}
$$
$\Box$

有了前面的准备, 我们现在可以将上述随机积分的定义扩展到更一般的可测适应随机过程类.
**定义8.2**
设$\{X(t), t \ge 0\}$是随机过程, $\{\mathcal{F}_t, t \ge 0\}$是$\sigma$代数流, 如果对任何$t$, $X(t)$是$\mathcal{F}_t$可测的, 则称$\{X(t)\}$是$\{\mathcal{F}_t\}$适应的.
记$\mathcal{B}$为$[0, \infty)$上的Borel $\sigma$代数,
$$
\mathcal{V} = \{h: h\text{是定义在}[0,\infty)\text{上的}\mathcal{B} \times \mathcal{F}\text{可测的适应过程}, \\ \text{满足} E[\int_0^\infty h^2(s)ds] < \infty\}.
$$
我们将随机积分的定义按下述步骤扩展到$\mathcal{V}$.
首先, 令$h \in \mathcal{V}$有界, 并且对每个$\omega \in \Omega$, $h(\cdot, \omega)$连续. 则存在简单过程$\{\phi_n\}$, 其中
$$
\phi_n = \sum_j h(t_j, \omega) \cdot \mathbf{1}_{[t_j, t_{j+1})}(t) \in \mathcal{V},
$$
使得, 当$n \to \infty$时, 对每个$\omega \in \Omega$,
$$
\int_0^T (h-\phi_n)^2 dt \to 0.
$$
因此, 由有界收敛定理得$E[\int_0^T (h-\phi_n)^2 dt] \to 0$.

其次, 令$h \in \mathcal{V}$有界, 可以证明, 存在$h_n \in \mathcal{V}$有界, 并且对每个$\omega \in \Omega, \forall n, h_n(\cdot, \omega)$连续, 使得
$$
E[\int_0^T (h - h_n)^2 dt] \to 0.
\tag{8}
$$
事实上, 不妨设$|h(t,\omega)| \le M, \forall(t, \omega)$. 定义
$$
h_n(t, \omega) = \int_t^\infty \psi_n(s-t)h(s, \omega)ds,
$$
这里, $\psi_n$是$\mathbb{R}$上非负连续函数, 使得对所有的$x \notin (-1/n, 0)$, $\psi_n(x)=0$且$\int_{-\infty}^\infty \psi_n(x)dx=1$. 则对每个$\omega \in \Omega, h_n(\cdot, \omega)$连续且$|h_n(t,\omega)| \le M$. 由$h \in \mathcal{V}$可以看出$h_n \in \mathcal{V}$, 并且, 当$n \to \infty$时, 对每个$\omega \in \Omega$, 有
$$
\int_0^T (h_n(s,\omega) - h(s,\omega))^2 ds \to 0.
$$
因此再次利用有界收敛定理得式(8).

最后, 对任意的$f \in \mathcal{V}$, 存在有界列$h_n \in \mathcal{V}$, 使得
$$
E[\int_0^T (f(t,\omega) - h_n(t,\omega))^2 dt] \to 0.
$$
事实上, 只要令
$$
h_n(t, \omega) = 
\begin{cases}
-n, & \text{若}f(t,\omega) < -n, \\
f(t, \omega), & \text{若}-n \le f(t,\omega) \le n, \\
n, & \text{若}f(t,\omega) > n,
\end{cases}
$$
利用控制收敛定理即得.

现在, 我们可以完成Itô积分$\int_0^T f(t)dB(t), f \in \mathcal{V}$的定义.
**定义8.3**
设$f \in \mathcal{V}(0,T)$. 则$f$的Itô积分定义为
$$
\int_0^T f(t,\omega)dB_t(\omega) = \lim_{n\to\infty} \int_0^T \phi_n(t,\omega)dB_t(\omega) \quad (L^2(P)\text{中极限}),
\tag{9}
$$
这里$\{\phi_n\}$是初等随机过程的序列, 使得当$n \to \infty$时
$$
E[\int_0^T (f(t,\omega) - \phi_n(t,\omega))^2 dt] \to 0.
\tag{10}
$$

**注**
在实际问题中, 我们常常遇到的过程并不满足$\mathcal{V}$中的可积性条件而仅仅满足下述的$\mathcal{V}^*$中的条件. 事实上, Itô积分的定义可以推广到更广泛的过程$\{h(s): s \ge 0\}$类:
$$
\mathcal{V}^* = \{h: h\text{为}\mathcal{B} \times \mathcal{F}\text{可测的适应过程}, \\ \text{且}\forall T > 0\text{满足}\int_0^T h^2(s)ds < \infty, a.s.\}.
$$
详细证明请参阅[13]或[17].

**例8.1** 设$f$是连续函数, 考虑$\int_0^1 f(B(t))dB(t)$. 因为$B(t)$有连续的路径, 所以$f(B(t))$也在$[0,1]$上连续. 因此$\int_0^1 f(B(t))dB(t)$有定义. 然而, 根据$f$的不同, 这个积分可以有(或没有)有限的矩, 例如:
(1) 取$f(t)=t$, 则由于$\int_0^1 E[B^2(t)]dt < \infty$, $E[\int_0^1 B(t)dB(t)]=0$并且由性质8.1(3)~(4)有
$$
E\left(\int_0^1 B(t)dB(t)\right)^2 = \int_0^1 E(B^2(t))dt = \frac{1}{2}.
$$
(2) 取$f(t)=e^{t^2}$, 此时考虑$\int_0^1 e^{B^2(t)}dB(t)$. 虽然积分存在, 但由于当$t \ge \frac{1}{2}$时, $E[e^{2B^2(t)}] = \infty$, 所以$\int_0^1 E[e^{2B^2(t)}] = \infty$, 即$\int_0^1 E[f^2(B(t))]dt < \infty$不成立, 说明积分的二阶矩不存在.

**例8.2** 求积分$J = \int_0^1 tdB(t)$的均值与方差.
因为$\int_0^1 t^2 dt < \infty$, 且$t$是$\mathcal{F}_t = \sigma\{B(s), 0 \le s \le t\}$适应的. 所以, Itô积分$J$是适定的并且其均值为$EJ=0$, 方差为$E(J^2) = \int_0^1 t^2 dt = \frac{1}{3}$.

**例8.3** 估计使得积分$\int_0^1 (1-t)^{-\alpha}dB(t)$适定的$\alpha$的值.
只要$\int_0^1 [(1-t)^{-\alpha}]^2 dt < \infty$, 即$\int_0^1 (1-t)^{-2\alpha}dt < \infty$, 上述Itô积分就适定. 所以只要$\alpha < \frac{1}{2}$即可.

### 8.3 Itô积分过程
设对任何实数$T > 0, X \in \mathcal{V}^*$, 则对任何$t \le T$, 积分$\int_0^t X(s)dB(s)$是适定的. 因为对任何固定的$t$, $\int_0^t X(s)dB(s)$是一个随机变量, 所以作为上限$t$的函数, 它定义了一个随机过程$\{Y(t)\}$, 其中
$$
Y(t) = \int_0^t X(s)dB(s).
\tag{11}
$$
可以证明, Itô积分$Y(t)$存在连续的样本路径, 即存在一个连续随机过程$\{Z(t)\}$, 使得对所有的$t$, 有$Y(t)=Z(t), a.s$. 因此, 从现在起我们所讨论的积分都假定是其连续的样本路径. 本节将讨论这一积分过程的各种随机性质.

现在我们可以证明其鞅性质.
**定理8.1**
设$X(t) \in \mathcal{V}^*$, 并且$\int_0^T EX^2(s)ds < \infty$, 则
$$
Y(t) = \int_0^t X(s)dB(s), \quad 0 \le t \le T
$$
是零均值的连续的平方可积鞅(即$\sup_{t \le T} E(Y^2(t)) < \infty$).
**证明** 由定义8.3后注, 可知$Y(t) = \int_0^t X(s)dB(s), 0 \le t \le T$是适定的, 并且具有一阶及二阶矩. 如果$\{X(t)\}$是简单过程, 则由式(4)之同样的证明方法可得
$$
E(\int_s^t X(u)dB(u)|\mathcal{F}_s) = E\left(\sum_{i=0}^{n-1} \xi_i[B(t_{i+1}) - B(t_i)]|\mathcal{F}_s\right) = \sum_{i=0}^{n-1} E(\xi_i[B(t_{i+1}) - B(t_i)]|\mathcal{F}_{t_i}|\mathcal{F}_s) = 0, \forall s < t.
$$
于是,
$$
\begin{aligned}
E(Y(t)|\mathcal{F}_s) &= E(\int_0^t X(u)dB(u)|\mathcal{F}_s) \\
&= \int_0^s X(u)dB(u) + E(\int_s^t X(u)dB(u)|\mathcal{F}_s) \\
&= \int_0^s X(u)dB(u) = Y(s).
\end{aligned}
$$
所以$\{Y(t)\}$是鞅. 由等距性可得其二阶矩
$$
E\left(\int_0^t X(s)dB(s)\right)^2 = \int_0^t E(X^2(s))ds.
$$
$\Box$

**推论8.1**
对任何有界的Borel-可测函数$f$(即对任何$a \in \mathbb{R}$, 有$\{x \in \mathbb{R}: f(x) \le a\} \in \mathcal{B}(\mathbb{R})$), $\int_0^t f(B(s))dB(s)$是平方可积鞅.
**证明** $X(t)=f(B(t))$是可测适应的并且有常数$K>0$使得$|f(x)| \le K$, 于是$\int_0^T E[f^2(B(s))]ds \le K^2T$. 由定理8.1, 此命题得证.
上述的定理提供了构造鞅的方法. 在8.2节中我们已经证明, 非随机的简单过程的Itô积分是正态分布的随机变量. 更一般的, 我们有下述定理.

**定理8.2**
如果$X$是非随机的, 且$\int_0^T X^2(s)ds < \infty$, 则对任何$t$, $Y(t) = \int_0^t X(s)dB(s)$是正态分布的随机变量. 即$\{Y(t), 0 \le t \le T\}$是Gauss过程, 均值为零, 协方差函数为
$$
\text{cov}(Y(t), Y(t+u)) = \int_0^t X^2(s)ds, \quad u \ge 0.
$$
$\{Y(t)\}$也是平方可积鞅.
**证明** 因为被积函数是非随机的, 所以$\int_0^T E(X^2(s))ds = \int_0^T X^2(s)ds < \infty$. 由定理8.1知$Y$具有零均值, 再由积分及$Y(t)$的鞅性质, 有
$$
\begin{aligned}
E\left[\int_0^t X(s)dB(s) \int_t^{t+u} X(s)dB(s)\right] &= E\left[E\left(\int_0^t X(s)dB(s) \int_t^{t+u} X(s)dB(s) \bigg| \mathcal{F}_t\right)\right] \\
&= E\left[\int_0^t X(s)dB(s) E\left(\int_t^{t+u} X(s)dB(s) \bigg| \mathcal{F}_t\right)\right] \\
&= 0.
\end{aligned}
$$
- **注意到**
$Y(t+u) - Y(t) = \int_t^{t+u} X(s)dB(s) = \sum_{i=0}^{n-1} c_i[B(t_{i+1}) - B(t_i)]$. $B(t_{i+1}) - B(t_i)$与$\mathcal{F}_t$独立, 因而$Y(t+u)-Y(t)$与$Y(t)$独立.
- **若X是随机的**, 则$\int_t^{t+u} X(s)dB(s) = \sum_{i=0}^{n-1} \xi_i[B(t_{i+1}) - B(t_i)]$. 而$\xi_i$能依赖于$\mathcal{F}_t$.
因此
$$
\begin{aligned}
\text{cov}(Y(t), Y(t+u)) &= E\left[\int_0^t X(s)dB(s) \int_0^{t+u} X(s)dB(s)\right] \\
&= E\left[\int_0^t X(s)dB(s) \left(\int_0^t X(s)dB(s) + \int_t^{t+u} X(s)dB(s)\right)\right] \\
&= E\left[\left(\int_0^t X(s)dB(s)\right)^2\right] \\
&= \int_0^t E(X^2(s))ds = \int_0^t X^2(s)ds.
\end{aligned}
$$
$\Box$

令$h(t) = \int_0^t X^2(a)da$, 则$\eta_t = Y^2(t)-h(t)$和$\xi_t=e^{Y(t)-h(t)/2}$都是鞅.
**证明:**
$$
\begin{aligned}
E[Y^2(t+s)|\mathcal{F}_t] &= E[Y^2(t) + 2Y(t)\{Y(t+s)-Y(t)\} + \{Y(t+s)-Y(t)\}^2 | \mathcal{F}_t] \\
&= Y^2(t) + 2Y(t)E[Y(t+s)-Y(t)] + E[\{Y(t+s)-Y(t)\}^2] \\
&= Y^2(t) + \int_t^{t+s} X^2(a)da.
\end{aligned}
$$

**例8.4**
根据上述定理可得$J=\int_0^1 sdB(s) \sim N(0, \frac{1}{3})$. 在之前的例子中, 可知$\int_0^1 B(s)ds \sim N(0, 1/3)$. 那么$(\int_0^1 sdB(s), \int_0^1 B(s)ds) \sim$ ??

**定义8.4**
设$Y(t) = \int_0^t X(s)dB(s), 0 \le t \le T$是Itô积分, 如果在依概率的意义下, 极限
$$
\lim_{\delta_n \to 0} \sum_{i=0}^{n-1} |Y(t_{i+1}^n) - Y(t_i^n)|^2
\tag{12}
$$
当$\{t_i^n\}$遍取$[0,t]$的分割, 且其模
$$
\delta_n = \max_{0 \le i < n-1}(t_{i+1}^n - t_i^n) \to 0
$$
时存在, 则称此极限为$Y$的二次变差, 记为$[Y,Y](t)$.

**定理8.3**
设$Y(t) = \int_0^t X(s)dB(s), 0 \le t \le T$是Itô积分, 则$Y$的二次变差为
$$
[Y,Y](t) = \int_0^t X^2(s)ds.
\tag{13}
$$
**说明** 由$dY(t) = X(t)dB(t)$, 则$[dY(t)]^2 = X^2(t)[dB(t)]^2 = X^2(t)dt$.

对关于同一个Brown运动$\{B(t)\}$的两个不同的Itô积分$Y_1(t) = \int_0^t X_1(s)dB(s)$和$Y_2(t) = \int_0^t X_2(s)dB(s)$, 由于$Y_1(t)+Y_2(t) = \int_0^t (X_1(s)+X_2(s))dB(s)$, 我们可以定义$Y_1$和$Y_2$的二次协变差,
$$
[Y_1, Y_2](t) = \frac{1}{2}([Y_1+Y_2, Y_1+Y_2](t) - [Y_1, Y_1](t) - [Y_2, Y_2](t)).
$$
由定理8.3, 有
$$
[Y_1, Y_2](t) = \int_0^t X_1(s)X_2(s)ds.
$$

### 8.4 Itô公式
Itô公式, 随机分析中的变量替换公式或链锁法则, 是随机分析中的一个主要工具, 许多重要的公式, 例如Dynkin公式, Feynman-Kac公式以及分部积分公式, 都是由Itô公式导出的.
因为Brown运动在$[0,t]$上的二次变差为$t$, 即在依概率收敛的意义下
$$
\lim_{\delta_n \to 0} \sum_{i=0}^{n-1} (B(t_{i+1}^n) - B(t_i^n))^2 = t,
$$
这里$\{t_i^n\}$是$[0,t]$的分割, $\delta_n = \max_{0 \le i \le n-1}(t_{i+1}^n-t_i^n)$. 形式上地, 上式可表示为
$$
\int_0^t (dB(s))^2 = \int_0^t ds = t,
$$
或
$$
(dB(t))^2 = dt.
$$
更一般地, 我们有
**定理8.4**
设$g$是有界连续函数, $\{t_i^n\}$是$[0,t]$的分割, 则对任何$\theta_i^n \in (B(t_i^n), B(t_{i+1}^n))$ (即$B(t_i^n), B(t_{i+1}^n)$之间的任意值), 依概率收敛意义下的极限
$$
\lim_{\delta_n \to 0} \sum_{i=0}^{n-1} g(\theta_i^n)(B(t_{i+1}^n) - B(t_i^n))^2 = \int_0^t g(B(s))ds.
\tag{14}
$$
**证明**
首先取$\theta_i^n = B(t_i^n)$, 由$g$的连续性和积分的定义, 有
$$
\sum_{i=0}^{n-1} g(B(t_i^n))(t_{i+1}^n - t_i^n) \to \int_0^t g(B(s))ds.
\tag{15}
$$
我们证明
$$
\sum_{i=0}^{n-1} g(B(t_i^n))((B(t_{i+1}^n) - B(t_i^n))^2 - (t_{i+1}^n - t_i^n)) \to 0
\tag{16}
$$
在$L^2$中成立. 记$\Delta B_i = B(t_{i+1}^n) - B(t_i^n)$, $\Delta t_i = t_{i+1}^n - t_i^n$, 则由Brown运动的独立增量性和取条件期望的方法得到
$$
\begin{aligned}
E&\left(\sum_{i=0}^{n-1} g(B(t_i^n))((\Delta B_i)^2 - \Delta t_i)\right)^2 \\
&= E\left[\sum_{i=0}^{n-1} g^2(B(t_i^n))((\Delta B_i)^2 - \Delta t_i)^2 | \mathcal{F}_{t_i}\right] \\
&= E\left[\sum_{i=0}^{n-1} g^2(B(t_i^n)) E(((\Delta B_i)^2 - \Delta t_i)^2 | \mathcal{F}_{t_i})\right] \\
&= 2E\left[\sum_{i=0}^{n-1} g^2(B(t_i^n))(\Delta t_i)^2\right] \\
&\le \delta_n 2E\left[\sum_{i=0}^{n-1} g^2(B(t_i^n))\Delta t_i\right] \to 0, \quad (\text{当}\delta \to 0\text{时}).
\end{aligned}
$$
**交叉项的处理**
下设$i<j$
$$
\begin{aligned}
&E(g(B(t_i^n))((\Delta B_i)^2 - \Delta t_i)g(B(t_j^n))((\Delta B_j)^2 - \Delta t_j)) \\
&= E[E(g(B(t_i^n))((\Delta B_i)^2 - \Delta t_i)g(B(t_j^n))((\Delta B_j)^2 - \Delta t_j) | \mathcal{F}_{t_j})] \\
&= E[g(B(t_i^n))((\Delta B_i)^2 - \Delta t_i)g(B(t_j^n))E((\Delta B_j)^2 - \Delta t_j | \mathcal{F}_{t_j})] \\
&= 0.
\end{aligned}
$$
因此, 在均方收敛的意义下
$$
\sum_{i=0}^{n-1} g(B(t_i^n))((\Delta B_i)^2 - \Delta t_i) \to 0,
$$
这样, $\sum_{i=0}^{n-1} g(B(t_i^n))(B(t_{i+1}^n) - B(t_i^n))^2$与$\sum_{i=0}^{n-1} g(B(t_i^n))(t_{i+1}^n - t_i^n)$有相同的极限$\int_0^t g(B(s))ds$.
对任意的$\theta_i^n \in (B(t_i^n), B(t_{i+1}^n))$, 当$\delta_n \to 0$时,
$$
\begin{aligned}
&\sum_{i=0}^{n-1} (g(\theta_i^n) - g(B(t_i^n)))(\Delta B_i)^2 \\
&\le \max_i|g(\theta_i^n) - g(B(t_i^n))| \sum_{i=0}^{n-1} (B(t_{i+1}^n) - B(t_i^n))^2.
\end{aligned}
\tag{17}
$$
由$g$和$B$的连续性, 我们有$\max_i|g(\theta_i^n) - g(B(t_i^n))| \to 0, a.s$. 由Brown运动二次变差的定义得$\sum_{i=0}^{n-1} (B(t_{i+1}^n) - B(t_i^n))^2 \to t$. 于是当$\delta_n \to 0$时, $\sum_{i=0}^{n-1} (g(\theta_i^n) - g(B(t_i^n)))(\Delta B_i)^2 \to 0$. 因此$\sum_{i=0}^{n-1} g(\theta_i^n)(\Delta B_i)^2$与$\sum_{i=0}^{n-1} g(B(t_i^n))(\Delta B_i)^2$具有相同的依概率收敛意义的极限$\int_0^t g(B(s))ds$. $\Box$

现在, 我们给出Itô公式.
**定理8.5**
如果$f$是二次连续可微函数, 则对任何$t$, 有
$$
f(B(t)) = f(0) + \int_0^t f'(B(s))dB(s) + \frac{1}{2}\int_0^t f''(B(s))ds.
\tag{18}
$$
**证明** 易见式(18)中的积分都是适定的. 取$[0,t]$的分割$\{t_i^n\}$, 有
$$
f(B(t)) = f(0) + \sum_{i=0}^{n-1} (f(B(t_{i+1}^n)) - f(B(t_i^n))).
$$
对$f(B(t_{i+1}^n)) - f(B(t_i^n))$应用Taylor公式得
$$
f(B(t_{i+1}^n)) - f(B(t_i^n)) = f'(B(t_i^n))(B(t_{i+1}^n) - B(t_i^n)) + \frac{1}{2}f''(\theta_i^n)(B(t_{i+1}^n) - B(t_i^n))^2
\tag{19}
$$
其中$\theta_i^n \in (B(t_i^n), B(t_{i+1}^n))$. 于是
$$
f(B(t)) = f(0) + \sum_{i=0}^{n-1} f'(B(t_i^n))(B(t_{i+1}^n) - B(t_i^n)) + \frac{1}{2}\sum_{i=0}^{n-1} f''(\theta_i^n)(B(t_{i+1}^n) - B(t_i^n))^2.
\tag{20}
$$
令$\delta_n \to 0$取极限, 则式(20)中的第一个和收敛于Itô积分$\int_0^t f'(B(s))dB(s)$. 利用定理8.4可知式(20)中的第二个和收敛于$\frac{1}{2}\int_0^t f''(B(s))ds$. $\Box$

式(18)称为Brown运动的Itô公式, 由此看出, Brown运动的函数可以表示为一个Itô积分加上一个具有有界变差的绝对连续过程, 我们称这类过程为Itô过程, 严格地, 我们有下面定义.
**定义8.5**
如果过程$\{Y(t), 0 \le t \le T\}$可以表示为
$$
Y(t) = Y(0) + \int_0^t \mu(s)ds + \int_0^t \sigma(s)dB(s), \quad 0 \le t \le T,
\tag{21}
$$
其中过程$\{\mu(t)\}$和$\{\sigma(t)\}$满足:
(1) $\mu(t)$是适应的并且$\int_0^T |\mu(t)|dt < \infty, a.s.$
(2) $\sigma(t) \in \mathcal{V}^*$.
则称$\{Y(t)\}$为Itô过程.

有时我们也将Itô过程(21)记为微分的形式
$$
dY(t) = \mu(t)dt + \sigma(t)dB(t), \quad 0 \le t \le T,
\tag{22}
$$
其中函数$\mu(t)$称为漂移系数, $\sigma(t)$称为扩散系数, 它们可以依赖于$Y(t)$或$B(t)$, 甚至整个路径$\{B(s), s \le t\}$, (例如$\mu(t) = \cos(M(t)+t)$, 这里$M(t) = \max_{s \le t} B(s)$). 一类非常重要的情形是$\mu$与$\sigma(t)$仅仅通过$Y(t)$依赖于$t$, 在这种情况下, 式(22)应改写为
$$
dY(t) = \mu(Y(t))dt + \sigma(Y(t))dB(t), \quad 0 \le t \le T.
\tag{23}
$$
如果用微分形式表示, Itô公式(18)变为
$$
d(f(B(t))) = f'(B(t))dB(t) + \frac{1}{2}f''(B(t))dt.
\tag{24}
$$

**例8.5** 求$d(e^{B(t)})$.
**解** 对函数$f(x)=e^x$用Itô公式, 此时$f'(x)=e^x, f''(x)=e^x$, 所以
$$
\begin{aligned}
d(e^{B(t)}) &= df(B(t)) = f'(B(t))dB(t) + \frac{1}{2}f''(B(t))dt \\
&= e^{B(t)}dB(t) + \frac{1}{2}e^{B(t)}dt.
\end{aligned}
\tag{25}
$$
于是, $X(t)=e^{B(t)}$具有随机微分形式
$$
dX(t) = X(t)dB(t) + \frac{1}{2}X(t)dt.
$$
在金融应用中, 股票的价格$S(t)$是用随机微分$dS(t)=\mu S(t)dt + \sigma S(t)dB(t)$描述的. 如果$\alpha(t)$表示在时刻$t$投资者的股票各股收益, 那么在整个时间区间$[0,T]$内的收益为
$$
\int_0^T \alpha(t)dS(t) = \int_0^T \alpha(t)\mu S(t)dt + \int_0^T \alpha(t)\sigma S(t)dB(t).
$$

下面定理给出了关于Itô过程的Itô公式, 其证明见[17].
**定理8.6**
设$\{X(t)\}$是由
$$
dX(t) = \mu(t)dt + \sigma(t)dB(t)
$$
给出的Itô过程, $g(t,x)$是$[0, \infty) \times \mathbb{R}$上的二次连续可微函数. 则
$$
\{Y(t) = g(t, X(t))\}
$$
仍为Itô过程, 并且
$$
dY(t) = \frac{\partial g}{\partial t}(t, X(t))dt + \frac{\partial g}{\partial x}(t, X(t))dX(t) + \frac{1}{2}\frac{\partial^2 g}{\partial x^2}(t, X(t)) \cdot (dX(t))^2.
\tag{26}
$$
其中$(dX(t))^2 = (dX(t)) \cdot (dX(t))$按照下面规则计算:
$$
dt \cdot dt = dt \cdot dB(t) = dB(t) \cdot dt = 0, \quad dB(t) \cdot dB(t) = dt,
$$
即(26)可以改写为
$$
dY(t) = \left(\frac{\partial g}{\partial t}(t, X(t)) + \frac{\partial g}{\partial x}(t, X(t))\mu(t) + \frac{1}{2}\frac{\partial^2 g}{\partial x^2}(t, X(t))\sigma^2(t)\right)dt + \frac{\partial g}{\partial x}(t, X(t))\sigma(t)dB(t).
$$
特别地, 如果$g(t,x) = g(x)$只是$x$的函数, (27)简化为
$$
dY(t) = [g'(X(t))\mu(t) + \frac{1}{2}g''(X(t))\sigma^2(t)]dt + g'(X(t))\sigma(t)dB(t)
\tag{27}
$$
公式(26)称为关于Itô过程的Itô公式.

在金融应用中, 股票的价格$S(t)$是用随机微分$dS(t)=\mu S(t)dt + \sigma S(t)dB(t)$描述的. 注意到
$$
\begin{aligned}
d\ln S(t) &= \left[\frac{\mu(t)}{S(t)} + \frac{1}{2}\left(-\frac{1}{S^2(t)}\right)\sigma^2(t)\right]dt + \frac{\sigma(t)}{S(t)}dB(t) \\
&= (\mu - \frac{1}{2}\sigma^2)dt + \sigma dB(t).
\end{aligned}
$$
于是有
$$
\ln S(t) = \ln S(0) + \int_0^t (\mu - \frac{1}{2}\sigma^2)ds + \int_0^t \sigma dB(s) \\
= \ln S(0) + (\mu - \frac{1}{2}\sigma^2)t + \sigma B(t).
$$
从而得到
$$
S(t) = S(0)e^{(\mu - \frac{1}{2}\sigma^2)t + \sigma B(t)}.
$$

- 设$X(t), Y(t)$是Ito过程, 则对$X(t)Y(t)$使用Ito公式可得:
  $d[X(t)Y(t)] = X(t)dY(t) + Y(t)dX(t) + dX(t) \cdot dY(t)$.
- $dB^k(t) = k B^{k-1}(t)dB(t) + \frac{1}{2}k(k-1)B^{k-2}(t)dt$. 特别地$\int_0^t dB^2(s) = 2\int_0^t B(s)dB(s) + \int_0^t ds$, 从而得到$\int_0^t B(s)dB(s) = \frac{B^2(t)-t}{2}$.
- 注意到$d[tB(t)] = B(t)dt + tdB(t)$. 从而$tB(t) = \int_0^t d(sB(s)) = \int_0^t B(s)ds + \int_0^t sdB(s)$.
- 特别地, $B(1) = \int_0^1 B(s)ds + \int_0^1 sdB(s)$. 所以
$$
\begin{aligned}
\text{Cov}(\int_0^1 B(s)ds, \int_0^1 sdB(s)) &= \text{Cov}(\int_0^1 B(s)ds, B(1) - \int_0^1 B(s)ds) \\
&= \text{Cov}(\int_0^1 B(s)ds, B(1)) - \text{Var}(\int_0^1 B(s)ds) \\
&= 1/2 - 1/3 = 1/6.
\end{aligned}
$$
- $\text{Cov}(\int_0^1 B(s)dB(s), \int_0^1 sdB(s)) =????$
- $H(t) = \int_0^t B^2(s)dB(s) =????$