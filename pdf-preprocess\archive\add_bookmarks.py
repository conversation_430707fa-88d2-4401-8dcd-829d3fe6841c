import fitz  # pymupdf

def add_bookmarks_to_pdf(pdf_path, output_path, bookmark_data, page_offset=0):
    """
    向PDF添加书签目录结构
    
    Args:
        pdf_path: 原始PDF文件路径
        output_path: 输出PDF文件路径
        bookmark_data: 书签数据列表
        page_offset: 页码偏移量（实际页码 + offset = PDF页码）
    """
    doc = fitz.open(pdf_path)
    
    # 清除现有书签（可选）
    # doc.delete_toc()
    
    # 构建新的目录结构
    toc = []
    
    for item in bookmark_data:
        level = item['level']
        title = item['title']
        # 应用页码偏移，并确保页码从0开始（PDF标准）
        pdf_page = item['page'] + page_offset - 1
        # 确保页码不小于0
        pdf_page = max(0, pdf_page)
        
        toc.append([level, title, pdf_page])
    
    # 设置目录
    doc.set_toc(toc)
    
    # 保存PDF
    doc.save(output_path)
    doc.close()
    
    print(f"已成功添加书签到PDF: {output_path}")
    print(f"使用页码偏移: {page_offset}")

def calculate_page_offset(actual_page, pdf_page_index):
    """
    计算页码偏移量
    
    Args:
        actual_page: 书中显示的实际页码
        pdf_page_index: 对应的PDF页面索引（从0开始）
    
    Returns:
        页码偏移量
    """
    return pdf_page_index - actual_page + 1

# 完整的书签数据（保持title中的页码不变）
bookmark_data_ma = [
    # 第九章 定积分
    {'level': 1, 'title': '第九章 定积分 202', 'page': 202},
    {'level': 2, 'title': 'S9.1 定积分概念 202', 'page': 202},
    {'level': 3, 'title': '一、问题提出', 'page': 202},
    {'level': 3, 'title': '二、定积分的定义', 'page': 203},
    {'level': 2, 'title': 'S9.2 牛顿-莱布尼茨公式 206', 'page': 206},
    {'level': 2, 'title': 'S9.3 可积条件 209', 'page': 209},
    {'level': 3, 'title': '一、可积的必要条件', 'page': 209},
    {'level': 3, 'title': '二、可积的充要条件', 'page': 210},
    {'level': 3, 'title': '三、可积函数类', 'page': 211},
    {'level': 2, 'title': 'S9.4 定积分的性质 215', 'page': 215},
    {'level': 3, 'title': '一、定积分的基本性质', 'page': 215},
    {'level': 3, 'title': '二、积分中值定理', 'page': 220},
    {'level': 2, 'title': 'S9.5 微积分学基本定理·定积分计算(续) 223', 'page': 223},
    {'level': 3, 'title': '一、变限积分与原函数的存在性', 'page': 223},
    {'level': 3, 'title': '二、换元积分法与分部积分法', 'page': 227},
    {'level': 3, 'title': '三、泰勒公式的积分型余项', 'page': 231},
    {'level': 2, 'title': 'S9.6 可积性理论补叙 234', 'page': 234},
    {'level': 3, 'title': '一、上和与下和的性质', 'page': 234},
    {'level': 3, 'title': '二、可积的充要条件', 'page': 237},

    # 第十章 定积分的应用
    {'level': 1, 'title': '第十章 定积分的应用 243', 'page': 243},
    {'level': 2, 'title': 'S10.1 平面图形的面积 243', 'page': 243},
    {'level': 2, 'title': 'S10.2 由平行截面面积求体积 247', 'page': 247},
    {'level': 2, 'title': 'S10.3 平面曲线的弧长与曲率 251', 'page': 251},
    {'level': 3, 'title': '一、平面曲线的弧长', 'page': 251},
    {'level': 3, 'title': '二、曲率', 'page': 255},
    {'level': 2, 'title': 'S10.4 旋转曲面的面积 259', 'page': 259},
    {'level': 3, 'title': '一、微元法', 'page': 259},
    {'level': 3, 'title': '二、旋转曲面的面积', 'page': 260},
    {'level': 2, 'title': 'S10.5 定积分在物理中的某些应用 262', 'page': 262},
    {'level': 3, 'title': '一、液体静压力', 'page': 262},
    {'level': 3, 'title': '二、引力', 'page': 263},
    {'level': 3, 'title': '三、功与平均功率', 'page': 265},
    {'level': 2, 'title': 'S10.6 定积分的近似计算 267', 'page': 267},
    {'level': 3, 'title': '一、梯形法', 'page': 267},
    {'level': 3, 'title': '二、抛物线法', 'page': 268},

    # 第十一章 反常积分
    {'level': 1, 'title': '第十一章 反常积分 271', 'page': 271},
    {'level': 2, 'title': 'S11.1 反常积分概念 271', 'page': 271},
    {'level': 3, 'title': '一、问题提出', 'page': 271},
    {'level': 3, 'title': '二、两类反常积分的定义', 'page': 272},
    {'level': 2, 'title': 'S11.2 无穷积分的性质与收敛判别 277', 'page': 277},
    {'level': 3, 'title': '一、无穷积分的性质', 'page': 277},
    {'level': 3, 'title': '二、非负函数无穷积分的收敛判别法', 'page': 278},
    {'level': 3, 'title': '三、一般无穷积分的收敛判别法', 'page': 280},
    {'level': 2, 'title': 'S11.3 瑕积分的性质与收敛判别 283', 'page': 283},

    # 附录
    {'level': 1, 'title': '附录 288', 'page': 288},
    {'level': 2, 'title': '附录I 微积分学简史', 'page': 288},
    {'level': 2, 'title': '附录II 实数理论', 'page': 296},
    {'level': 2, 'title': '附录III 积分表', 'page': 310},
    {'level': 1, 'title': '习题答案 317', 'page': 317},
    {'level': 1, 'title': '索引 338', 'page': 338},
    {'level': 1, 'title': '人名索引 343', 'page': 343},

    # 下册目录
    # 第十二章 数项级数
    # {'level': 1, 'title': '第十二章 数项级数 1', 'page': 1},
    # {'level': 2, 'title': 'S12.1 级数的收敛性 1', 'page': 1},
    # {'level': 2, 'title': 'S12.2 正项级数 6', 'page': 6},
    # {'level': 3, 'title': '一、正项级数收敛性的一般判别原则', 'page': 6},
    # {'level': 3, 'title': '二、比式判别法和根式判别法', 'page': 9},
    # {'level': 3, 'title': '三、积分判别法', 'page': 14},
    # {'level': 3, 'title': '四、拉贝判别法', 'page': 15},
    # {'level': 2, 'title': 'S12.3 一般项级数 18', 'page': 18},
    # {'level': 3, 'title': '一、交错级数', 'page': 18},
    # {'level': 3, 'title': '二、绝对收敛级数及其性质', 'page': 19},
    # {'level': 3, 'title': '三、阿贝尔判别法和狄利克雷判别法', 'page': 23},

    # # 第十三章 函数列与函数项级数
    # {'level': 1, 'title': '第十三章 函数列与函数项级数 28', 'page': 28},
    # {'level': 2, 'title': 'S13.1 一致收敛性 28', 'page': 28},
    # {'level': 3, 'title': '一、函数列及其一致收敛性', 'page': 28},
    # {'level': 3, 'title': '二、函数项级数及其一致收敛性', 'page': 33},
    # {'level': 3, 'title': '三、函数项级数的一致收敛性判别法', 'page': 34},
    # {'level': 2, 'title': 'S13.2 一致收敛函数列与函数项级数的性质 39', 'page': 39},

    # # 第十四章 幂级数
    # {'level': 1, 'title': '第十四章 幂级数 47', 'page': 47},
    # {'level': 2, 'title': 'S14.1 幂级数 47', 'page': 47},
    # {'level': 3, 'title': '一、幂级数的收敛区间', 'page': 47},
    # {'level': 3, 'title': '二、幂级数的性质', 'page': 51},
    # {'level': 3, 'title': '三、幂级数的运算', 'page': 53},
    # {'level': 2, 'title': 'S14.2 函数的幂级数展开 55', 'page': 55},
    # {'level': 3, 'title': '一、泰勒级数', 'page': 55},
    # {'level': 3, 'title': '二、初等函数的幂级数展开式', 'page': 57},
    # {'level': 2, 'title': 'S14.3 复变量的指数函数·欧拉公式 64', 'page': 64},

    # # 第十五章 傅里叶级数
    # {'level': 1, 'title': '第十五章 傅里叶级数 67', 'page': 67},
    # {'level': 2, 'title': 'S15.1 傅里叶级数 67', 'page': 67},
    # {'level': 3, 'title': '一、三角级数·正交函数系', 'page': 67},
    # {'level': 3, 'title': '二、以2π为周期的函数的傅里叶级数', 'page': 69},
    # {'level': 3, 'title': '三、收敛定理', 'page': 70},
    # {'level': 2, 'title': 'S15.2 以2l为周期的函数的展开式 77', 'page': 77},
    # {'level': 3, 'title': '一、以2l为周期的函数的傅里叶级数', 'page': 77},
    # {'level': 3, 'title': '二、偶函数与奇函数的傅里叶级数', 'page': 79},
    # {'level': 2, 'title': 'S15.3 收敛定理的证明 84', 'page': 84},

    # # 第十六章 多元函数的极限与连续
    # {'level': 1, 'title': '第十六章 多元函数的极限与连续 92', 'page': 92},
    # {'level': 2, 'title': 'S16.1 平面点集与多元函数 92', 'page': 92},
    # {'level': 3, 'title': '一、平面点集', 'page': 92},
    # {'level': 3, 'title': '二、R2上的完备性定理', 'page': 95},
    # {'level': 3, 'title': '三、二元函数', 'page': 97},
    # {'level': 3, 'title': '四、n元函数', 'page': 99},
    # {'level': 2, 'title': 'S16.2 二元函数的极限 100', 'page': 100},
    # {'level': 3, 'title': '一、二元函数的极限', 'page': 101},
    # {'level': 3, 'title': '二、累次极限', 'page': 104},
    # {'level': 2, 'title': 'S16.3 二元函数的连续性 108', 'page': 108},
    # {'level': 3, 'title': '一、二元函数的连续性概念', 'page': 108},
    # {'level': 3, 'title': '二、有界闭域上连续函数的性质', 'page': 110},
]

bookmark_data = [
    # # =============== 上册内容 ===============
    # # 第一章 实数集与函数
    # {'level': 1, 'title': '第一章 实数集与函数 (1)', 'page': 1},
    # {'level': 2, 'title': '本章知识结构及内容小结', 'page': 1},
    # {'level': 2, 'title': '经典例题解析', 'page': 7},
    # {'level': 2, 'title': '历年考研真题评析', 'page': 11},
    # {'level': 2, 'title': '本章教材习题全解', 'page': 13},
    # {'level': 2, 'title': '同步自测题及参考答案', 'page': 33},

    # # 第二章 数列极限
    # {'level': 1, 'title': '第二章 数列极限 (36)', 'page': 36},
    # {'level': 2, 'title': '本章知识结构及内容小结', 'page': 36},
    # {'level': 2, 'title': '经典例题解析', 'page': 39},
    # {'level': 2, 'title': '历年考研真题评析', 'page': 43},
    # {'level': 2, 'title': '本章教材习题全解', 'page': 45},
    # {'level': 2, 'title': '同步自测题及参考答案', 'page': 67},

    # # 第三章 函数极限
    # {'level': 1, 'title': '第三章 函数极限 (70)', 'page': 70},
    # {'level': 2, 'title': '本章知识结构及内容小结', 'page': 70},
    # {'level': 2, 'title': '经典例题解析', 'page': 73},
    # {'level': 2, 'title': '历年考研真题评析', 'page': 81},
    # {'level': 2, 'title': '本章教材习题全解', 'page': 84},
    # {'level': 2, 'title': '同步自测题及参考答案', 'page': 109},

    # # 第四章 函数的连续性
    # {'level': 1, 'title': '第四章 函数的连续性 (113)', 'page': 113},
    # {'level': 2, 'title': '本章知识结构及内容小结', 'page': 113},
    # {'level': 2, 'title': '经典例题解析', 'page': 115},
    # {'level': 2, 'title': '历年考研真题评析', 'page': 121},
    # {'level': 2, 'title': '本章教材习题全解', 'page': 124},
    # {'level': 2, 'title': '同步自测题及参考答案', 'page': 142},

    # # 第五章 导数和微分
    # {'level': 1, 'title': '第五章 导数和微分 (145)', 'page': 145},
    # {'level': 2, 'title': '本章知识结构及内容小结', 'page': 145},
    # {'level': 2, 'title': '经典例题解析', 'page': 149},
    # {'level': 2, 'title': '历年考研真题评析', 'page': 154},
    # {'level': 2, 'title': '本章教材习题全解', 'page': 157},
    # {'level': 2, 'title': '同步自测题及参考答案', 'page': 183},

    # # 第六章 微分中值定理及其应用
    # {'level': 1, 'title': '第六章 微分中值定理及其应用 (187)', 'page': 187},
    # {'level': 2, 'title': '本章知识结构及内容小结', 'page': 187},
    # {'level': 2, 'title': '经典例题解析', 'page': 190},
    # {'level': 2, 'title': '历年考研真题评析', 'page': 197},
    # {'level': 2, 'title': '本章教材习题全解', 'page': 200},
    # {'level': 2, 'title': '同步自测题及参考答案', 'page': 237},

    # # 第七章 实数的完备性
    # {'level': 1, 'title': '第七章 实数的完备性 (243)', 'page': 243},
    # {'level': 2, 'title': '本章知识结构及内容小结', 'page': 243},
    # {'level': 2, 'title': '经典例题解析', 'page': 245},
    # {'level': 2, 'title': '历年考研真题评析', 'page': 248},
    # {'level': 2, 'title': '本章教材习题全解', 'page': 250},
    # {'level': 2, 'title': '同步自测题及参考答案', 'page': 259},

    # # 第八章 不定积分
    # {'level': 1, 'title': '第八章 不定积分 (262)', 'page': 262},
    # {'level': 2, 'title': '本章知识结构及内容小结', 'page': 262},
    # {'level': 2, 'title': '经典例题解析', 'page': 266},
    # {'level': 2, 'title': '历年考研真题评析', 'page': 272},
    # {'level': 2, 'title': '本章教材习题全解', 'page': 274},
    # {'level': 2, 'title': '同步自测题及参考答案', 'page': 300},

    # # 第九章 定积分
    # {'level': 1, 'title': '第九章 定积分 (303)', 'page': 303},
    # {'level': 2, 'title': '本章知识结构及内容小结', 'page': 303},
    # {'level': 2, 'title': '经典例题解析', 'page': 306},
    # {'level': 2, 'title': '历年考研真题评析', 'page': 310},
    # {'level': 2, 'title': '本章教材习题全解', 'page': 313},
    # {'level': 2, 'title': '同步自测题及参考答案', 'page': 343},

    # # 第十章 定积分的应用
    # {'level': 1, 'title': '第十章 定积分的应用 (351)', 'page': 351},
    # {'level': 2, 'title': '本章知识结构及内容小结', 'page': 351},
    # {'level': 2, 'title': '经典例题解析', 'page': 353},
    # {'level': 2, 'title': '历年考研真题评析', 'page': 356},
    # {'level': 2, 'title': '本章教材习题全解', 'page': 358},
    # {'level': 2, 'title': '同步自测题及参考答案', 'page': 372},

    # # 第十一章 反常积分
    # {'level': 1, 'title': '第十一章 反常积分 (376)', 'page': 376},
    # {'level': 2, 'title': '本章知识结构及内容小结', 'page': 376},
    # {'level': 2, 'title': '经典例题解析', 'page': 378},
    # {'level': 2, 'title': '历年考研真题评析', 'page': 381},
    # {'level': 2, 'title': '本章教材习题全解', 'page': 384},
    # {'level': 2, 'title': '同步自测题及参考答案', 'page': 404},

    # =============== 下册内容 ===============
    # 第十二章 数项级数
    {'level': 1, 'title': '第十二章 数项级数 (1)', 'page': 1},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 1},
    {'level': 2, 'title': '经典例题解析', 'page': 4},
    {'level': 2, 'title': '历年考研真题评析', 'page': 8},
    {'level': 2, 'title': '本章教材习题全解', 'page': 9},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 32},

    # 第十三章 函数列与函数项级数
    {'level': 1, 'title': '第十三章 函数列与函数项级数 (35)', 'page': 35},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 35},
    {'level': 2, 'title': '经典例题解析', 'page': 39},
    {'level': 2, 'title': '历年考研真题评析', 'page': 42},
    {'level': 2, 'title': '本章教材习题全解', 'page': 44},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 63},

    # 第十四章 幂级数
    {'level': 1, 'title': '第十四章 幂级数 (65)', 'page': 65},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 65},
    {'level': 2, 'title': '经典例题解析', 'page': 68},
    {'level': 2, 'title': '历年考研真题评析', 'page': 71},
    {'level': 2, 'title': '本章教材习题全解', 'page': 72},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 90},

    # 第十五章 傅里叶级数
    {'level': 1, 'title': '第十五章 傅里叶级数 (93)', 'page': 93},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 93},
    {'level': 2, 'title': '经典例题解析', 'page': 95},
    {'level': 2, 'title': '历年考研真题评析', 'page': 98},
    {'level': 2, 'title': '本章教材习题全解', 'page': 100},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 128},

    # 第十六章 多元函数的极限与连续
    {'level': 1, 'title': '第十六章 多元函数的极限与连续 (132)', 'page': 132},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 132},
    {'level': 2, 'title': '经典例题解析', 'page': 135},
    {'level': 2, 'title': '历年考研真题评析', 'page': 139},
    {'level': 2, 'title': '本章教材习题全解', 'page': 140},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 166},

    # 第十七章 多元函数微分学
    {'level': 1, 'title': '第十七章 多元函数微分学 (169)', 'page': 169},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 169},
    {'level': 2, 'title': '经典例题解析', 'page': 172},
    {'level': 2, 'title': '历年考研真题评析', 'page': 180},
    {'level': 2, 'title': '本章教材习题全解', 'page': 182},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 211},

    # 第十八章 隐函数定理及其应用
    {'level': 1, 'title': '第十八章 隐函数定理及其应用 (214)', 'page': 214},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 214},
    {'level': 2, 'title': '经典例题解析', 'page': 216},
    {'level': 2, 'title': '历年考研真题评析', 'page': 222},
    {'level': 2, 'title': '本章教材习题全解', 'page': 224},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 250},

    # 第十九章 含参量积分
    {'level': 1, 'title': '第十九章 含参量积分 (254)', 'page': 254},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 254},
    {'level': 2, 'title': '经典例题解析', 'page': 257},
    {'level': 2, 'title': '历年考研真题评析', 'page': 260},
    {'level': 2, 'title': '本章教材习题全解', 'page': 262},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 278},

    # 第二十章 曲线积分
    {'level': 1, 'title': '第二十章 曲线积分 (281)', 'page': 281},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 281},
    {'level': 2, 'title': '经典例题解析', 'page': 284},
    {'level': 2, 'title': '历年考研真题评析', 'page': 288},
    {'level': 2, 'title': '本章教材习题全解', 'page': 289},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 299},

    # 第二十一章 重积分
    {'level': 1, 'title': '第二十一章 重积分 (302)', 'page': 302},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 302},
    {'level': 2, 'title': '经典例题解析', 'page': 306},
    {'level': 2, 'title': '历年考研真题评析', 'page': 312},
    {'level': 2, 'title': '本章教材习题全解', 'page': 314},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 350},

    # 第二十二章 曲面积分
    {'level': 1, 'title': '第二十二章 曲面积分 (354)', 'page': 354},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 354},
    {'level': 2, 'title': '经典例题解析', 'page': 357},
    {'level': 2, 'title': '历年考研真题评析', 'page': 362},
    {'level': 2, 'title': '本章教材习题全解', 'page': 365},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 381},

    # 第二十三章 向量函数微分学
    {'level': 1, 'title': '第二十三章 向量函数微分学 (383)', 'page': 383},
    {'level': 2, 'title': '本章知识结构及内容小结', 'page': 383},
    {'level': 2, 'title': '经典例题解析', 'page': 384},
    {'level': 2, 'title': '本章教材习题全解', 'page': 386},
    {'level': 2, 'title': '同步自测题及参考答案', 'page': 405},
]

bookmark_data_la = [
    # 第一章 行列式
    {'level': 1, 'title': '第一章 行列式 1', 'page': 1},
    {'level': 2, 'title': '§1.1 二阶行列式 1', 'page': 1},
    {'level': 2, 'title': '§1.2 三阶行列式 7', 'page': 7},
    {'level': 2, 'title': '§1.3 n阶行列式 12', 'page': 12},
    {'level': 2, 'title': '§1.4 行列式的展开和转置 21', 'page': 21},
    {'level': 2, 'title': '§1.5 行列式的计算 28', 'page': 28},
    {'level': 2, 'title': '§1.6 行列式的等价定义 38', 'page': 38},
    {'level': 2, 'title': '§1.7 Laplace定理 44', 'page': 44},

    # 第二章 矩阵
    {'level': 1, 'title': '第二章 矩阵 57', 'page': 57},
    {'level': 2, 'title': '§2.1 矩阵的概念 57', 'page': 57},
    {'level': 2, 'title': '§2.2 矩阵的运算 60', 'page': 60},
    {'level': 2, 'title': '§2.3 方阵的逆阵 71', 'page': 71},
    {'level': 2, 'title': '§2.4 矩阵的初等变换与初等矩阵 76', 'page': 76},
    {'level': 2, 'title': '§2.5 矩阵乘积的行列式与初等变换法求逆阵 88', 'page': 88},
    {'level': 2, 'title': '§2.6 分块矩阵 95', 'page': 95},
    {'level': 2, 'title': '§2.7 Cauchy-Binet公式 105', 'page': 105},

    # 第三章 线性空间
    {'level': 1, 'title': '第三章 线性空间 116', 'page': 116},
    {'level': 2, 'title': '§3.1 数域 116', 'page': 116},
    {'level': 2, 'title': '§3.2 行向量和列向量 118', 'page': 118},
    {'level': 2, 'title': '§3.3 线性空间 122', 'page': 122},
    {'level': 2, 'title': '§3.4 向量的线性关系 126', 'page': 126},
    {'level': 2, 'title': '§3.5 向量组的秩 132', 'page': 132},
    {'level': 2, 'title': '§3.6 矩阵的秩 138', 'page': 138},
    {'level': 2, 'title': '§3.7 坐标向量 147', 'page': 147},
    {'level': 2, 'title': '§3.8 基变换与过渡矩阵 153', 'page': 153},
    {'level': 2, 'title': '§3.9 子空间 159', 'page': 159},
    {'level': 2, 'title': '§3.10 线性方程组的解 166', 'page': 166},

    # 第四章 线性映射
    {'level': 1, 'title': '第四章 线性映射 183', 'page': 183},
    {'level': 2, 'title': '§4.1 线性映射的概念 183', 'page': 183},
    {'level': 2, 'title': '§4.2 线性映射的运算 187', 'page': 187},
    {'level': 2, 'title': '§4.3 线性映射与矩阵 191', 'page': 191},
    {'level': 2, 'title': '§4.4 线性映射的像与核 200', 'page': 200},
    {'level': 2, 'title': '§4.5 不变子空间 205', 'page': 205},

    # 第五章 多项式
    {'level': 1, 'title': '第五章 多项式 213', 'page': 213},
    {'level': 2, 'title': '§5.1 一元多项式代数 213', 'page': 213},
    {'level': 2, 'title': '§5.2 整除 215', 'page': 215},
    {'level': 2, 'title': '§5.3 最大公因式 219', 'page': 219},
    {'level': 2, 'title': '§5.4 因式分解 226', 'page': 226},
    {'level': 2, 'title': '§5.5 多项式函数 231', 'page': 231},
    {'level': 2, 'title': '§5.6 复系数多项式 234', 'page': 234},
    {'level': 2, 'title': '§5.7 实系数多项式和有理系数多项式 239', 'page': 239},
    {'level': 2, 'title': '§5.8 多元多项式 244', 'page': 244},
    {'level': 2, 'title': '§5.9 对称多项式 248', 'page': 248},
    {'level': 2, 'title': '§5.10 结式和判别式 254', 'page': 254},

    # 第六章 特征值
    {'level': 1, 'title': '第六章 特征值 267', 'page': 267},
    {'level': 2, 'title': '§6.1 特征值和特征向量 267', 'page': 267},
    {'level': 2, 'title': '§6.2 对角化 276', 'page': 276},
    {'level': 2, 'title': '§6.3 极小多项式与Cayley-Hamilton定理 283', 'page': 283},
    {'level': 2, 'title': '§6.4 特征值的估计 288', 'page': 288},

    # 第七章 相似标准型
    {'level': 1, 'title': '第七章 相似标准型 297', 'page': 297},
    {'level': 2, 'title': '§7.1 多项式矩阵 297', 'page': 297},
    {'level': 2, 'title': '§7.2 矩阵的法式 302', 'page': 302},
    {'level': 2, 'title': '§7.3 不变因子 307', 'page': 307},
    {'level': 2, 'title': '§7.4 有理标准型 311', 'page': 311},
    {'level': 2, 'title': '§7.5 初等因子 315', 'page': 315},
    {'level': 2, 'title': '§7.6 Jordan标准型 318', 'page': 318},
    {'level': 2, 'title': '§7.7 Jordan标准型的进一步讨论和应用 326', 'page': 326},
    {'level': 2, 'title': '§7.8 矩阵函数 334', 'page': 334},

    # 第八章 二次型
    {'level': 1, 'title': '第八章 二次型 346', 'page': 346},
    {'level': 2, 'title': '§8.1 二次型的化简与矩阵的合同 346', 'page': 346},
    {'level': 2, 'title': '§8.2 二次型的化简 351', 'page': 351},
    {'level': 2, 'title': '§8.3 惯性定理 357', 'page': 357},
    {'level': 2, 'title': '§8.4 正定型与正定矩阵 361', 'page': 361},
    {'level': 2, 'title': '§8.5 Hermite型 366', 'page': 366},

    # 第九章 内积空间
    {'level': 1, 'title': '第九章 内积空间 373', 'page': 373},
    {'level': 2, 'title': '§9.1 内积空间的概念 373', 'page': 373},
    {'level': 2, 'title': '§9.2 内积的表示和正交基 379', 'page': 379},
    {'level': 2, 'title': '§9.3 伴随 387', 'page': 387},
    {'level': 2, 'title': '§9.4 内积空间的同构、正交变换和酉变换 390', 'page': 390},
    {'level': 2, 'title': '§9.5 自伴随算子 399', 'page': 399},
    {'level': 2, 'title': '§9.6 复正规算子 406', 'page': 406},
    {'level': 2, 'title': '§9.7 实正规矩阵 410', 'page': 410},
    {'level': 2, 'title': '§9.8 谱分解与极分解 417', 'page': 417},
    {'level': 2, 'title': '§9.9 奇异值分解 424', 'page': 424},
    {'level': 2, 'title': '§9.10 最小二乘解 430', 'page': 430},

    # 第十章 双线性型
    {'level': 1, 'title': '第十章 双线性型 442', 'page': 442},
    {'level': 2, 'title': '§10.1 对偶空间 442', 'page': 442},
    {'level': 2, 'title': '§10.2 双线性型 447', 'page': 447},
    {'level': 2, 'title': '§10.3 纯量积 453', 'page': 453},
    {'level': 2, 'title': '§10.4 辛错型与辛几何 458', 'page': 458},
    {'level': 2, 'title': '§10.5 对称型与正交几何 461', 'page': 461},

    # 附录
    {'level': 1, 'title': '参考文献 467', 'page': 467},
    {'level': 1, 'title': '索引 468', 'page': 468},
]

# 使用示例
if __name__ == "__main__":
    # 方法1: 直接设置偏移量
    # 例如：如果书中第202页对应PDF的第180页（索引179），偏移量为 179 - 202 + 1 = -22
    # page_offset = -22  # 根据实际情况调整
    
    # add_bookmarks_to_pdf(
    #     pdf_path='input.pdf', 
    #     output_path='output_with_bookmarks.pdf', 
    #     bookmark_data=bookmark_data, 
    #     page_offset=page_offset
    # )
    
    # 方法2: 通过已知对应关系计算偏移量
    # 例如：已知书中第202页对应PDF的第180页
    actual_page = 2
    pdf_page_index = 18  # PDF页面索引（从0开始）
    calculated_offset = calculate_page_offset(actual_page, pdf_page_index)
    
    print(f"计算出的页码偏移量: {calculated_offset}")
    
    add_bookmarks_to_pdf(
        pdf_path='1_textbook.pdf', 
        output_path='1_textbookfitz.pdf', 
        bookmark_data=bookmark_data_la, 
        page_offset=calculated_offset
    )
