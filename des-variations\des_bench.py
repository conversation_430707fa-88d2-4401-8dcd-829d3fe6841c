#!/usr/bin/env python3
import time
import gc
import statistics
import platform

#from des2 import strEnc as strEnc2
from des9 import strEnc as strEnc3
from des2 import strEnc as strEnc2

def build_data(n: int) -> str:
    base = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz"
    if n <= len(base):
        return base[:n]
    times = (n + len(base) - 1) // len(base)
    return (base * times)[:n]


def time_calls(func, data, keys, loops: int) -> float:
    # 计量 func(data, *keys) 连续 loops 次的总耗时（秒）
    f = func  # 绑定局部，减少查找
    d = data
    k1, k2, k3 = keys
    gc_was_enabled = gc.isenabled()
    if gc_was_enabled:
        gc.disable()
    try:
        t0 = time.perf_counter()
        for _ in range(loops):
            f(d, k1, k2, k3)
        t1 = time.perf_counter()
    finally:
        if gc_was_enabled:
            gc.enable()
    return t1 - t0


def calibrate_loops(data: str, keys) -> int:
    # 基于 des2 的平均单次耗时估算 loops，使一次测量约 0.2s，范围 [2000, 50000]
    pre_n = 50
    t = time_calls(strEnc2, data, keys, pre_n) / pre_n
    t = max(t, 1e-6)  # 防止除零
    loops = int(0.2 / t)
    return max(2000, min(50000, loops))


def bench_one(data_len: int, repeat: int = 3):
    data = build_data(data_len)
    keys = ("1", "2", "3")

    # 正确性检查
    out2 = strEnc2(data, *keys)
    out3 = strEnc3(data, *keys)
    same = (out2 == out3)

    # 预热
    for _ in range(3):
        strEnc2(data, *keys)
        strEnc3(data, *keys)

    loops = calibrate_loops(data, keys)

    # 计时
    t2_list, t3_list = [], []
    for _ in range(repeat):
        t2 = time_calls(strEnc2, data, keys, loops)
        t3 = time_calls(strEnc3, data, keys, loops)
        t2_list.append(t2)
        t3_list.append(t3)

    # 统计（平均单次耗时，微秒）
    avg2_us = (statistics.mean(t2_list) / loops) * 1e6
    avg3_us = (statistics.mean(t3_list) / loops) * 1e6
    speedup = (avg2_us / avg3_us) if avg3_us > 0 else float('inf')

    print(f"\n--- 数据长度 {data_len} ---")
    print(f"输出一致性: {'通过' if same else '不一致！'}")
    print(f"loops/测次: {loops}, 重复次数: {repeat}")
    print(f"des2 平均单次: {avg2_us:.2f} µs")
    print(f"des3 平均单次: {avg3_us:.2f} µs")
    print(f"加速比(des3 vs des2): {speedup:.2f}x")


def main():
    print(f"Python: {platform.python_version()} ({platform.python_implementation()})")
    print("小数据片段基准：调用模式 strEnc(data, '1', '2', '3')")
    for n in (10, 15, 20, 25, 30):
        bench_one(n, repeat=3)


if __name__ == "__main__":
    main()

