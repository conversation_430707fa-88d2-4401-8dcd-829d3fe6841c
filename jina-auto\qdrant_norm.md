### 参考实现目标
- 用 Jina v4 生成文本多向量（ColBERT 128 维），入库到 Qdrant 的多向量字段（MAX_SIM）。
- 文档级聚合与“长度偏置”归一化（基于 num_patches）。
- 两阶段检索：先用文档“centroid”做粗召回，再用多向量 MAX_SIM 精排。

### 依赖
```
pip install qdrant-client requests
```

### 最小可用样例：入库 + 多向量检索 + 归一化
```python
import os, math, requests
from statistics import mean
from qdrant_client import QdrantClient, models

JINA_API_KEY = os.environ.get("JINA_API_KEY")
HEADERS = {"Content-Type": "application/json", "Authorization": f"Bearer {JINA_API_KEY}"}
API_URL = "https://api.jina.ai/v1/embeddings"

def embed_text_multivector(text: str):
    payload = {
        "model": "jina-embeddings-v4",
        "return_multivector": True,
        "input": {"text": text},
    }
    r = requests.post(API_URL, headers=HEADERS, json=payload, timeout=60)
    r.raise_for_status()
    body = r.json()
    embs = ((body.get("data") or [])[0] or {}).get("embeddings") or ((body.get("data") or [])[0] or {}).get("embedding")
    if not embs or not isinstance(embs, list) or not isinstance(embs[0], list):
        raise RuntimeError("未返回多向量(二维)嵌入")
    return embs  # List[List[float]]  =>  [patches, 128]

def compute_centroid(patches):
    # 简单均值作为粗召回向量，维度=128
    dim = len(patches[0])
    return [mean(col) for col in zip(*patches)]

# 1) 连接 Qdrant
client = QdrantClient(url="http://localhost:6333")

# 2) 创建集合：多向量 + centroid（两阶段用）
collection = "colbert_collection"
client.recreate_collection(
    collection_name=collection,
    vectors_config={
        "centroid": models.VectorParams(size=128, distance=models.Distance.COSINE),
        "colbert": models.VectorParams(
            size=128,
            distance=models.Distance.COSINE,
            multivector_config=models.MultiVectorConfig(
                comparator=models.MultiVectorComparator.MAX_SIM
            ),
        ),
    },
)

# 3) 索引若干文档
docs = {
    "docA": "北京是中国的首都，也是政治与文化中心。",
    "docB": "柏林是德国首都，欧洲重要的历史与文化名城。",
    "docC": "东京位于日本本州关东平原，是日本的政治与经济中心。",
}
points = []
for doc_id, text in docs.items():
    patches = embed_text_multivector(text)
    centroid = compute_centroid(patches)
    points.append(
        models.PointStruct(
            id=doc_id,
            vector={"centroid": centroid, "colbert": patches},
            payload={"doc_id": doc_id, "text": text, "num_patches": len(patches)},
        )
    )
client.upsert(collection_name=collection, points=points)

# 4) 查询：多向量 + 归一化（示例用 sqrt/对数归一化）
query_text = "德国的首都是哪里？"
q_patches = embed_text_multivector(query_text)

# 直接多向量检索（MAX_SIM），先看原始分数
hits = client.search(
    collection_name=collection,
    query_vector=("colbert", q_patches),
    limit=5,
    with_payload=True,
)
print("原始分数：")
for h in hits:
    print(h.id, h.score, h.payload.get("num_patches"))

# 简单归一化（可选）：除以 sqrt(num_patches) 或 log1p(num_patches)
def normalize(h):
    n = max(1, int(h.payload.get("num_patches", 1)))
    return h.score / math.sqrt(n)  # 或 math.log1p(n)

hits_sorted = sorted(hits, key=normalize, reverse=True)
print("归一化后：")
for h in hits_sorted:
    print(h.id, normalize(h), h.payload.get("num_patches"))
```

### 进阶：两阶段检索（centroid 粗召回 → 多向量精排）
```python
# 阶段1：用 query patches 的 centroid 做粗召回（更快）
q_centroid = compute_centroid(q_patches)
coarse = client.search(
    collection_name=collection,
    query_vector=("centroid", q_centroid),
    limit=50,  # 候选池大小按延迟预算取值
    with_payload=True,
)

candidate_ids = [h.id for h in coarse]

# 阶段2：在候选集内用多向量 MAX_SIM 精排（并做归一化）
flt = models.Filter(
    must=[models.FieldCondition(key="doc_id", match=models.MatchAny(any=candidate_ids))]
)
refined = client.search(
    collection_name=collection,
    query_vector=("colbert", q_patches),
    limit=10,
    with_payload=True,
    query_filter=flt,
)

refined_sorted = sorted(refined, key=normalize, reverse=True)
print("两阶段 + 归一化：")
for h in refined_sorted:
    print(h.id, normalize(h), h.payload.get("num_patches"))
```

### 要点总结
- 多向量字段需启用 `multivector_config=comparator=MAX_SIM` 才能直接传入二维向量做检索。
- 为缓解“长文/多 patch 更占优”，常见做法：
  - 入库时统一预算（num_patches 上限/统一分块粒度）。
  - 评分归一化（除以 sqrt(n) 或 log1p(n)），在应用层做。
  - 两阶段检索：`centroid` 粗召回 → 多向量 MAX_SIM 精排，再加归一化。
- 若要“top-M 命中聚合”等更细策略，需要更细的 token-level 命中信息；Qdrant 返回文档级分数，常在应用层近似处理（如仅用 top-K 候选参与聚合、或统一 patch 预算）。