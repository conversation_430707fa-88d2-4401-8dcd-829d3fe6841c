## 第三章 泊松过程
<EMAIL> 郭旭
北京师范大学 统计学院

### Poisson过程
1. Poisson过程
2. 与Poisson过程相联系的若干分布
3. Poisson过程的推广

### 泊松轶事
泊松的父亲是退役军人,退役后在村里作小职员,法国革命爆发时任村长.泊松最初奉父命学医,但他对医学并无兴趣,不久便转向数学.于1798年进入巴黎综合工科学校,成为拉格朗日、拉普拉斯的得意门生.在毕业时由于其学业优异,又得到拉普拉斯的大力推荐,故1806年留校任辅导教师,1802年任巴黎理学院教授.1812年当选为巴黎科学院院士.1816年应聘为索邦大学教授.1826年被选为彼得堡科学院名誉院士.1837年被封为男爵.著名数学家阿贝尔说:“泊松知道怎样做到举止非常高贵.”

### 泊松轶事, cont.
泊松认为人生只有两件美好的事情:发现数学和讲授数学。在法国科学院,泊松和其他同事就概率论应用于法律而展开了长期和激烈的争论。泊松一生对摆极感兴趣,他的科学生涯就是研究摆开始,直到晚年,他仍用大部分时间和精力从事摆的研究,他为什么对摆如此着迷呢?据说泊松小时候由于身体羸弱,母亲曾把他托付给一个保姆,保姆一离开,就把泊松放在一个摇篮式的布袋里,并把布袋挂在棚顶的钉子上,吊着他摆来摆去。泊松后来风趣的说,吊着我摆来摆去,不但是我孩提时的身体锻炼,并且使我在孩提时代就熟悉了摆。

### 计数过程
**定义**
随机过程${N(t),t \ge 0}$称为计数过程,如果$N(t)$表示从0到$t$时刻,某一特定事件A发生的次数,它具备以下两个特点:
(1) $N(t) \ge 0$且取值为整数;
(2) $s < t$时, $N(s) \le N(t)$且$N(t) - N(s)$表示$(s,t]$时间内事件A发生的次数。

### Poisson过程
**定义**
计数过程${N(t),t \ge 0}$称为参数为$\lambda(\lambda > 0)$的Poisson过程,如果
(1) $N(0) = 0$;
(2) 过程有独立增量;
(3) 对任意的$s, t \ge 0$,
$$
P(N(t + s) – N(s) = n) = e^{-\lambda t} \frac{(\lambda t)^n}{n!}, n = 0,1,2,...
$$
**注**: (1) Poisson过程是独立平稳增量的计数过程;
(2) 由于$E[N(t)] = \lambda t$, 于是可认为$\lambda$是单位时间内发生的事件的平均次数,故一般称$\lambda$是Poisson过程的强度或速率

### 例3.1 Poisson过程在排队论中的应用
**例**
在随机服务系统中排队现象的研究中,经常用到Poisson 过程模型,例如,到达电话总机的呼叫数目,到达某服务设施的顾客数,都可以用Poisson过程来描述,以某火车站售票处为例,设从早上8:00开始,此售票处连续售票,乘客依10人/h的平均速率到达,则从9:00到10:00这1小时内最多有5名乘客来此购票的概率是多少? 从10:00-11:00没有人来买票的概率是多少?
我们用一个Poisson过程来描述。设8:00为0时刻,则9:00为1时刻,参数$\lambda = 10$.由Poisson过程的平稳性知
$$
P(N(2) – N(1) \le 5) = \sum_{n=0}^{5} e^{-10 \cdot 1} \frac{(10 \cdot 1)^n}{n!}
$$
$$
P(N(3) – N(2) = 0) = e^{-10} \frac{(10)^0}{0!} = e^{-10}
$$

### 例3.2 事故的发生次数及保险公司接到的索赔数
**例**
若以$N(t)$表示某场所在$(0,t]$时间内发生不幸事故的数目,则Poisson过程就是${N(t),t \ge 0}$的一种很好近似,例如,保险公司接到赔偿请求的次数(设一次事故就导致一次索赔)都是可以应用Poisson过程的模型。我们考虑一种最简单情况,设保险公司每次的赔付都是1,每月平均接到索赔要求4次,则一年中它要付出的金额平均为多少?
设一年开始为0时刻,1月末为时刻1,2月末为时刻2,…,则年末为时刻12
$$
P(N(12) – N(0) = n) = \frac{(4 \times 12)^n}{n!} e^{-4 \times 12}
$$
均值
$$
E[N(12) – N(0)] = 4 \times 12 = 48
$$

### Poisson过程的另一等价定义
**定义**
设${N(t), t \ge 0}$是一个计数过程,若满足
(1)' $N(0) = 0$;
(2)' 过程有平稳独立增量;
(3)' 存在$\lambda > 0$,当$h \downarrow 0$时
$$
P(N(t + h) – N(t) = 1) = \lambda h + o(h);
$$
(4)' 当$h \downarrow 0$时,
$$
P(N(t + h) – N(t) \ge 2) = o(h).
$$
则称${N(t), t \ge 0}$为Poisson过程

### Poisson过程两定义等价的证明
**定理**
满足上述条件(1)' – (4)'的计数过程${N(t), t \ge 0}$是Poisson过程,反过来Poisson 过程一定满足这4个条件。
证明: 设计数过程${N(t),t \ge 0}$满足条件(1)'~(4)',证明它是Poisson过程,可以看到,其实只需验证$N(t)$服从参数为$\lambda t$的Poisson分布即可.
记
$$
P_n(t) = P(N(t) = n), \quad n = 0,1,2 \cdots
$$
$$
\begin{aligned}
P(h) &= P(N(h) \ge 1) \\
&= P_1(h) + P_2(h) + \cdots \\
&= 1 - P_0(h)
\end{aligned}
$$

### Poisson过程两定义等价的证明
$$
\begin{aligned}
P_0(t+h) &= P(N(t + h) = 0) \\
&= P(N(t + h) – N(t) = 0, N(t) = 0) \\
&= P(N(t) = 0)P(N(t + h) – N(t) = 0) \\
&= P_0(t) P_0(h) \\
&= P_0(t)(1 – \lambda h + o(h)) \quad (条件(3)', (4)').
\end{aligned}
$$
因此
$$
\frac{P_0(t + h) - P_0(t)}{h} = -\lambda P_0(t) + \frac{o(h)}{h},
$$
令$h \to 0$, 得
$$
P_0'(t) = -\lambda P_0(t).
$$

### Poisson过程两定义等价的证明
解此微分方程,得
$$
P_0(t) = Ke^{-\lambda t},
$$
其中$K$为常数. 由$P_0(0) = P(N(0) = 0) = 1$得$K = 1$, 故
$$
P_0(t) = e^{-\lambda t}.
$$
同理,当$n \ge 1$时,有
$$
\begin{aligned}
P_n(t+h) &= P(N(t + h) = n) \\
&= P(N(t) = n, N(t + h) – N(t) = 0) \\
& \quad +P(N(t) = n − 1, N(t + h) – N(t) = 1) \\
& \quad +P(N(t + h) = n, N(t + h) – N(t) \ge 2) \\
&= P_n(t)P_0(h) + P_{n−1}(t) \cdot P_1(h) + o(h) \\
&= (1 – \lambda h)P_n(t) + \lambda h P_{n−1}(t) + o(h).
\end{aligned}
$$

### Poisson过程两定义等价的证明
于是
$$
\frac{P_n(t + h) - P_n(t)}{h} = -\lambda P_n(t) + \lambda P_{n−1}(t) + o(h),
$$
令$h \to 0$得
$$
P_n'(t) = -\lambda P_n(t) + \lambda P_{n−1}(t)
$$
等价地
$$
e^{\lambda t}[P_n'(t) + \lambda P_n(t)] = e^{\lambda t} \lambda P_{n-1}(t).
$$
因此,
$$
[e^{\lambda t} P_n(t)]' = e^{\lambda t} \lambda P_{n-1}(t).
$$

### Poisson过程两定义等价的证明
采用数学归纳法,假定$(n - 1)$时
$$
P_{n-1}(t) = e^{-\lambda t} (\lambda t)^{n-1}/(n-1)!.
$$
于是
$$
[e^{\lambda t} P_n(t)]' = \lambda \frac{(\lambda t)^{n-1}}{(n-1)!}.
$$
从而
$$
e^{\lambda t} P_n(t) = \frac{(\lambda t)^n}{n!} + c.
$$
又由于$P_n(0) = 0$,所以$c = 0$,得证。

### Poisson过程两定义等价的证明
反过来,证明Poisson过程满足条件(1)' ~ (4)',只需验证条件(3)', (4)'成立
由定义中第三个条件可得
$$
\begin{aligned}
P(N(t + h) – N(t) = 1) &= P(N(h) – N(0) = 1) \\
&= e^{-\lambda h} \frac{\lambda h}{1!} = \lambda h \sum_{n=0}^\infty \frac{(-\lambda h)^n}{n!} \\
&= \lambda h[1 – \lambda h + o(h)] \\
&= \lambda h + o(h)
\end{aligned}
$$
$$
\begin{aligned}
P(N(t + h) – N(t) \ge 2) &= P(N(h) – N(0) \ge 2) \\
&= \sum_{n=2}^\infty e^{-\lambda h} \frac{(-\lambda h)^n}{n!} \\
&= o(h).
\end{aligned}
$$

设$Y$服从参数为$\lambda$的Possion分布,则其满足
$$
E[Y (Y − 1) \cdots (Y − k + 1)] = \lambda^k.
$$
试计算Poisson过程的协方差函数
$$
\gamma(t,s) = E[\{N(t) – \lambda t\}\{N(s) – \lambda s\}].
$$

### 例3.3
**例**
事件A的发生形成强度为$\lambda$的Poisson过程${N(t),t \ge 0}$。如果每次事件发生时以概率$p$能够被记录下来,并以$M(t)$表示到$t$时刻被记录下来的事件总数,则${M(t),t \ge 0}$是一个强度为$\lambda p$的Poisson过程。
事实上,由于每次事件发生时,对它的记录和不记录都与其他的事件能否被记录独立,而且事件发生服从Poisson分布。所以$M(t)$也是具有平稳独立增量的,故只需验证$M(t)$服从均值为$\lambda pt$的Poisson分布。即对$t > 0$, 有
$$
P(M(t) = m) = \frac{(\lambda pt)^m}{m!} e^{-\lambda pt}.
$$

### 例3.3
由于
$$
\begin{aligned}
P(M(t) = m) &= \sum_{n=0}^\infty P(M(t) = m|N(t) = m + n) \cdot P(N(t) = m + n) \\
&= \sum_{n=0}^\infty C_{m+n}^{m} p^m (1-p)^n \cdot \frac{(\lambda t)^{m+n}}{(m+n)!} e^{-\lambda t} \\
&= e^{-\lambda t} \sum_{n=0}^\infty \frac{(\lambda pt)^m (\lambda(1-p)t)^n}{m!n!} \\
&= e^{-\lambda t} \frac{(\lambda pt)^m}{m!} \sum_{n=0}^\infty \frac{(\lambda(1-p)t)^n}{n!} \\
&= e^{-\lambda t} \frac{(\lambda pt)^m}{m!} e^{\lambda(1-p)t} = e^{-\lambda pt} \frac{(\lambda pt)^m}{m!}
\end{aligned}
$$
结论得证。

假设每月在北京找到第一份工作的人数服从强度为1000的泊松过程,其中有10%的概率是山东人,问在第一季度没有山东人在北京找到第一份工作的概率是多少?
注意到$\lambda p = 1000 * 0.1 = 100$, $t = 3$,故所求概率为$e^{-300}$。

### Poisson过程的样本路径
[Image: A step function graph showing N(t) increasing by 1 at discrete time points T1, T2, T3. The y-axis is N(t) and the x-axis is time. The steps occur at X1, X1+X2, X1+X2+X3, which correspond to T1, T2, T3.]
图3.1 Poisson 过程的样本路径

### $X_n$和$T_n$的分布
**定理**
$X_n, n = 1,2,\dots$服从参数为$\lambda$的指数分布,且相互独立。
证明:首先考虑$X_1$的分布,注意到事件${X_1 > t}$等价于事件${N(t) = 0}$, 即$(0,t]$内没有事件发生,因此
$$
P(X_1 > t) = P(N(t) = 0) = e^{-\lambda t},
$$
从而
$$
P(X_1 \le t) = 1 - e^{-\lambda t}.
$$
再来看$X_2$
$$
\begin{aligned}
P(X_2 > t | X_1 = s) &= P(N(s+ t) – N(s) = 0|X_1 = s) \\
&= P(N(s+t) – N(s) = 0) \quad (独立增量性) \\
&= e^{-\lambda t}.
\end{aligned}
$$
所以$X_2$与$X_1$独立, 且都服从参数为$\lambda$的指数分布.重复同样的推导,可得定理结论。

### 指数分布的性质
**定理**
指数分布是唯一具有无记忆性的连续型随机变量
$$
Pr(X > t + s|X > t) = Pr(X > s).
$$
危险率函数
$$
r(x) = \frac{f(x)}{S(x)} = \lim_{h \to 0} \frac{Pr(x \le X \le x+h|X > x)}{h}
$$
* $X_i \sim E(\lambda_i)$且相互独立,则$min(X_1,\dots,X_n) \sim E(\sum_{i=1}^n \lambda_i)$
* $Pr(X_1 < X_2) = \lambda_1/(\lambda_1 + \lambda_2)$.
* $Pr(X_i = min(X_1,\dots,X_n)) = Pr(X_i < min_{j\ne i} X_j) = \frac{\lambda_i}{\sum_{k=1}^n \lambda_k}$.

$$
\begin{aligned}
Pr(X_1 < X_2) &= \int_0^\infty Pr(X_1 < X_2|X_1 = x)\lambda_1 e^{-\lambda_1 x} dx \\
&= \int_0^\infty Pr(x < X_2)\lambda_1 e^{-\lambda_1 x} dx \\
&= \int_0^\infty e^{-\lambda_2 x} \lambda_1 e^{-\lambda_1 x} dx \\
&= \frac{\lambda_1}{\lambda_1 + \lambda_2}
\end{aligned}
$$

### $X_n$和$T_n$的分布
**定理**
$T_n, n = 1,2,3,\dots$服从参数为$n$和$\lambda$的$\Gamma$分布。
证明:由于$T_n = \sum_{i=1}^n X_i$,而由上述定理知道, $X_i$是相互独立的且有同指数分布,同时指数分布是$\Gamma$分布的一种特殊情形($n=1$)由$\Gamma$分布可加性,易得$T_n$服从参数为$n$和$\lambda$的$\Gamma$分布。
采用数学归纳法论证,注意到$T_{n+1} = T_n+X_{n+1}$,且$T_n$和$X_{n+1}$相互独立,
$$
\begin{aligned}
f_{T_{n+1}}(t) &= \int_0^t f_{T_n}(s) f_{X_{n+1}}(t-s) ds \\
&= \int_0^t \frac{\lambda e^{-\lambda s} (\lambda s)^{n-1}}{(n-1)!} \cdot \lambda e^{-\lambda(t-s)} ds \\
&= e^{-\lambda t} \lambda^{n+1} \int_0^t \frac{s^{n-1}}{(n-1)!} ds = \lambda e^{-\lambda t} \frac{(\lambda t)^n}{n!}.
\end{aligned}
$$

我们在这里用另外的方法导出. 注意到
$$
N(t) \ge n \iff T_n \le t
$$
即第$n$次事件发生在时刻$t$或之前相当于到时刻$t$已经发生的事件数目至少是$n$,

### $X_n$和$T_n$的分布
因此
$$
P(T_n \le t) = P(N(t) \ge n) = \sum_{j=n}^\infty e^{-\lambda t} \frac{(\lambda t)^j}{j!}.
$$
对第上式两端求导可得$T_n$的密度函数
$$
\begin{aligned}
f(t) &= -\sum_{j=n}^\infty \lambda e^{-\lambda t} \frac{(\lambda t)^j}{j!} + \sum_{j=n}^\infty \lambda e^{-\lambda t} \frac{(\lambda t)^{j-1}}{(j-1)!} \\
&= \frac{\lambda e^{-\lambda t} (\lambda t)^{n-1}}{(n-1)!} \\
&= \frac{\lambda^n}{\Gamma(n)} t^{n-1} e^{-\lambda t}.
\end{aligned}
$$
$\square$

### $X_n$和$T_n$的分布
注意到
$$
\begin{aligned}
Pr(t < T_n < t+h) &= Pr\{N(t) = n-1, \text{one event in } (t,t+h)\} + o(h) \\
&= Pr\{N(t) = n-1\} Pr\{\text{one event in } (t,t+h)\} + o(h) \\
&= e^{-\lambda t} \frac{(\lambda t)^{n-1}}{(n-1)!} [\lambda h + o(h)] + o(h) \\
&= \lambda e^{-\lambda t} \frac{(\lambda t)^{n-1}}{(n-1)!} h + o(h).
\end{aligned}
$$
从而有
$$
f(t) = \lambda e^{-\lambda t} \frac{(\lambda t)^{n-1}}{(n-1)!}
$$
$\square$

### Poisson过程又一定义方法
**定义**
计数过程${N(t), t \ge 0}$是参数为$\lambda$的Poisson过程,如果每次事件发生的时间间隔$X_1,X_2,\dots$相互独立,且服从同一参数为$\lambda$的指数分布。

**例**
设从早上8:00开始有无穷多的人排队等候服务,只有一名服务员,且每个人接受服务的时间是独立的并服从均值为20min的指数分布,则到中午12:00为止平均有多少人已经离去,已有9个人接受服务的概率是多少?

### 例3.4
由所设条件可知,离去的人数${N(t)}$是强度为3的Poisson过程(这里以小时为单位)。设8:00为零时刻,则
$$
P(N(4) – N(0) = n) = e^{-12} \frac{(12)^n}{n!}
$$
其均值为12,即到12:00为止,离去的人平均是12名。而有9个人接受过服务的概率是
$$
P(N(4) = 9) = e^{-12} \frac{(12)^9}{9!}
$$

### 事件发生时刻的条件分布
**定理**
在已知$[0,t]$内A只发生一次的前提下,A发生的时刻在$[0,t]$上是均匀分布
事实上,对于$n=1$时的情形,对于$s \le t$
$$
\begin{aligned}
P(T_1 \le s | N(t) = 1) &= \frac{P(T_1 \le s, N(t)=1)}{P(N(t)=1)} \\
&= \frac{P(\text{A发生在s时刻之前}, (s,t]\text{内A没有发生})}{P(N(t)=1)} \\
&= \frac{P(N(s)=1) \cdot P(N(t)-N(s)=0)}{P(N(t)=1)} \\
&= \frac{\lambda s e^{-\lambda s} \cdot e^{-\lambda(t-s)}}{\lambda t e^{-\lambda t}} \\
&= \frac{s}{t}.
\end{aligned}
$$

### 事件发生时刻的条件分布
**定理**
在已知$N(t) = n$的条件下,事件发生的$n$个时刻$T_1,T_2,\dots,T_n$的联合分布密度是
$$
f(t_1, t_2, \dots, t_n) = \frac{n!}{t^n}, \quad 0 < t_1 < t_2 < \dots < t_n.
$$
证明: 设$0 < t_1 < t_2 < \dots < t_n < t_{n+1} = t$。取$h_i$充分小使得$t_i + h_i < t_{i+1}, i=1,2,\dots,n$,
$$
\begin{aligned}
& P(t_i < T_i \le t_i + h_i, i = 1,2,\dots,n | N(t)=n) \\
&= \frac{P(N(t_i+h_i)-N(t_i)=1,N(t_{i+1})-N(t_i+h_i)=0, 1 \le i \le n, N(t_1)=0)}{P(N(t)=n)} \\
&= \frac{\lambda h_1 e^{-\lambda h_1} \cdots \lambda h_n e^{-\lambda h_n} e^{-\lambda(t-h_1-h_2-\dots-h_n)}}{e^{-\lambda t}(\lambda t)^n/n!} \\
&= \frac{n!}{t^n} h_1 \cdots h_n.
\end{aligned}
$$

### 事件发生时刻的条件分布
故按定义,给定$N(t)=n$时, $(T_1, \dots, T_n)$的$n$维条件分布密度函数
$$
f(t_1,\dots,t_n) = \lim_{\substack{h_i \to 0 \\ 1 \le i \le n}} \frac{P(t_i<T_i \le t_i+h_i, 1 \le i \le n | N(t)=n)}{h_1 h_2 \cdots h_n} = \frac{n!}{t^n}, \quad 0 < t_1 < t_2 < \dots < t_n.
$$
证毕。
**注**：在已知$[0,t]$内发生了$n$次事件的前提下,各次事件发生的时刻$T_1, T_2,\dots,T_n$(不排序)可看做相互独立的随机变量,且都服从$[0,t]$上的均匀分布。

**例**
试计算$(T_1,\dots,T_{n-1})|T_n$的联合分布,并基于此计算$E(X^2|X+Y)$和$Var(2X+Y|X+Y+Z)$。这里$X,Y,Z$独立同分布指数分布$E(\lambda)$。

### 例3.5
**例**
乘客按照强度为$\lambda$的Poisson过程来到某火车站,火车在时刻$t$启程,计算在$(0,t]$ 内到达的乘客等待时间的总和的期望值,即要求$E(\sum_{i=1}^{N(t)} (t-T_i))$, 其中$T_i$是第$i$个乘客来到的时刻。
**解** 在$N(t)$给定条件下,取条件期望
$$
\begin{aligned}
E \left[ \sum_{i=1}^{N(t)} (t-T_i) \middle| N(t)=n \right] &= E \left[ \sum_{i=1}^n (t-T_i) \middle| N(t)=n \right] \\
&= nt - E \left[ \sum_{i=1}^n T_i \middle| N(t)=n \right]
\end{aligned}
$$

### 例3.5
由上定理, 记$U_1, U_2, \dots, U_n$为$n$个独立的服从$(0,t]$上的均匀分布的随机变量, 有
$$
E \left[ \sum_{i=1}^n T_i \middle| N(t)=n \right] = E \left[ \sum_{i=1}^n U_i \right] = \frac{nt}{2}
$$
则
$$
E \left[ \sum_{i=1}^n (t-T_i) \middle| N(t)=n \right] = nt - \frac{nt}{2} = \frac{nt}{2}.
$$
所以
$$
\begin{aligned}
E \left[ \sum_{i=1}^{N(t)} (t-T_i) \right] &= E \left[ E \left[ \sum_{i=1}^{N(t)} (t-T_i) \middle| N(t)=n \right] \right] \\
&= E \left[ \frac{t}{2} N(t) \right] \\
&= \frac{\lambda t^2}{2}.
\end{aligned}
$$

### 例3.6
**例**
考虑例3.3中每次事件发生时被记录到的概率随时间发生变化时的情况,设事件A在$s$时刻发生被记录到的概率是$P(s)$, 若以$M(t)$表示到$t$时刻被记录的事件数,那么它还是Poisson过程吗? 试给出$M(t)$的分布。
**解** 很容易看出$M(t)$已不能形成一个Poisson过程,因为虽然它仍然具有独立增量性,但由于$P(s)$的影响,它已不再有平稳增量性。但可以证明,对$\forall t, M(t)$依然是Poisson分布,参数与$t$和$P(s)$有关. 实际上, $M(t)$的均值为$\lambda t \bar{p}$,其中
$$
\bar{p} = \frac{1}{t} \int_0^t P(s)ds.
$$

### 例3.6
事实上,若对$N(t)$给定的条件下,取条件期望, 则有
$$
\begin{aligned}
P(M(t) = m) &= \sum_{k=0}^\infty P(M(t)=m|N(t)=m+k) P(N(t)=m+k) \\
&= \sum_{k=0}^\infty P(\text{已知}[0,t]\text{中发生了}m+k\text{次事件}, \text{只有}m\text{件被记录}) \\
& \quad \times P(N(t) = m+k).
\end{aligned}
$$
由于每次事件是否被记录是独立的,所以上式中$P(M(t)=m|N(t)=m+k)$可以看做在$m+k$次独立试验中有$m$次成功(被记录)和$k$次失败(不被记录)的概率

### 例3.6
故
$$
P(M(t) = m|N(t) = m+k) = \binom{m+k}{m} p^m (1-p)^k,
$$
其中$p$是每次试验成功的概率,由定理3.4, 并且已知道事件的发生和被记录是独立的,所以
$$
\begin{aligned}
p &= P(\text{事件在}[0,t]\text{内发生且被记录}) \\
&= \int_0^t P(\text{事件在}s\text{时刻发生且被记录})ds \\
&= \int_0^t \frac{1}{t} P(s)ds = \frac{1}{t} \int_0^t P(s)ds,
\end{aligned}
$$

### 例3.6
$$
\begin{aligned}
&= \sum_{k=0}^\infty P(M(t)=m|N(t)=m+k) \times P(N(t)=m+k) \\
&= \sum_{k=0}^\infty \binom{m+k}{m} p^m (1-p)^k \frac{(\lambda t)^{m+k}}{(m+k)!} e^{-\lambda t} \\
&= \sum_{k=0}^\infty \frac{(m+k)!}{m!k!} p^m (1-p)^k \frac{(\lambda t)^{m+k}}{(m+k)!} e^{-\lambda t} \\
&= \frac{(\lambda tp)^m}{m!} e^{-\lambda t} \sum_{k=0}^\infty \frac{(\lambda t(1-p))^k}{k!} \\
&= \frac{(\lambda t p)^m}{m!} e^{-\lambda t} e^{\lambda t(1-p)} \\
&= \frac{(\lambda t p)^m}{m!} e^{-\lambda t p}
\end{aligned}
$$
故$P(M(t)=m) = \frac{(\lambda t \bar{p})^m}{m!} e^{-\lambda t \bar{p}}$ 其中$\bar{p} = \frac{1}{t} \int_0^t P(s)ds$。

### 例
**例**
假设一部仪器承受到冲击,冲击遵循参数为$\lambda$的泊松过程来到,第$i$次冲击造成的损失为$D_i$。假定$D_i, i \ge 1$独立同分布且与${N(t), t \ge 0}$独立。这里$N(t)$表示$[0,t]$时间内冲击的次数。假定冲击引起的损失随时间而指数地衰减,即若一个冲击造成的初始损失为$D$,时间$t$之后造成的损失为$De^{-\alpha t}, \alpha > 0$。假定损失是可加的,则在$t$时的损失$D(t)$为
$$
D(t) = \sum_{i=1}^{N(t)} D_i e^{-\alpha(t-T_i)}.
$$
其中$T_i$表示第$i$次冲击来到的时刻。试求$E[D(t)]$。

### 例(分解)
参数为$\lambda$的Poisson过程$N(t)$可分解为$k$个相互独立的Poisson过程,且参数分别为$\lambda p_i$。其中$0 < p_i < 1, \sum_{i=1}^k p_i = 1$.
**例**
考虑一个道路上汽车行驶的问题。已知每分钟,在某地经过的汽车数目服从速率为1的泊松过程。其中10%是卡车,90%是轿车。如果知道刚才的一个小时内,经过了10辆卡车,问刚才的一个小时内,经过的车的数量的期望是多少?
$$
E[N_1(t) + N_2(t)|N_1(t) = 10] = 10 + E[N_2(t)].
$$

### 例(叠加)
$k$个相互独立的Poisson过程$N_i(t)$,且参数分别为$\lambda_i$,则他们的和$N(t) = \sum_{i=1}^k N_i(t)$服从参数为$\lambda = \sum_{i=1}^k \lambda_i$的Poisson过程。
**例**
有红、黑和白三种颜色的汽车,分别以强度为$\lambda_1, \lambda_2, \lambda_3$的Poisson过程到达某收费站。设它们相互独立。
* 求两辆汽车之间的时间间隔的分布;
* 在0时刻观察到一辆红色汽车,下一辆车是红色的概率。
$$
N(t) = N_1(t) + N_2(t) + N_3(t).
$$
$$
X = \min(X_1, X_2, X_3), Pr(X_1 < \min(X_2, X_3)).
$$

### 例
**例**
购买某商品的订单数形成一个强度是$\lambda$的Poisson过程,店主在每个订单上的收益为$X$,其概率密度函数为$f(x)$. 店主采用一种策略,即当$X > y$时接受订单,否则拒绝,并等待下一个订单,假设店主等待的时间成本为$c$, 求在该策略下,店主在每份订单上的平均利润。
被接受的订单数形成强度为$\lambda S(y)$的Poisson过程。
$$
E[R(y)] = E(X|X>y) - \frac{c}{\lambda S(y)}
$$

### 例(条件)
若$s < t, 0 \le m \le n$, 则有:
$$
Pr(N(s)=m|N(t)=n) = \frac{Pr(N(s)=m, N(t)=n)}{Pr(N(t)=n)} = \binom{n}{m} (\frac{s}{t})^m (1-\frac{s}{t})^{n-m}.
$$

**例**
设$N(t)$服从一个速率为2的泊松过程。计算
$Pr(N(3)=4|N(1)=1); E[N(3)|N(1)];$
$Pr(N(1)=1|N(3)=4); E[N(1)|N(3)].$

### 非齐次Poisson过程
**定义**
计数过程${N(t), t \ge 0}$称做强度函数为$\lambda(t) > 0(t \ge 0)$的非齐次Poisson过程,如果
(1) $N(0) = 0$;
(2) 过程有独立增量;
(3) $P(N(t+h) – N(t) = 1) = \lambda(t)h + o(h)$;
(4) $P(N(t+h) – N(t) \ge 2) = o(h)$。

### 非齐次Poisson过程等价定义
**定义**
计数过程${N(t), t \ge 0}$称为强度函数为$\lambda(t) > 0(t \ge 0)$的非齐次Poisson过程,若
(1) $N(0) = 0$;
(2) 过程有独立增量;
(3) 对任意实数$t \ge 0, s \ge 0, N(t+s)-N(t)$为具有参数为$m(t+s)-m(t) = \int_t^{t+s} \lambda(u)du$ 的Poisson分布。
**注**：$m(t) = \int_0^t \lambda(s)ds$

### Poisson过程与非齐次Poisson过程之间转换关系
**定理**
设${N(t),t \ge 0}$是一个强度函数为$\lambda(t)$的非齐次Poisson过程。对任意$t \ge 0$, 令$N^*(t) = N(m^{-1}(t))$,则${N^*(t)}$是一个强度为1的Poisson过程。
证明：首先由$\lambda(t) > 0$知, $m(t) = \int_0^t \lambda(s)ds > 0$且单调增加,所以$m^{-1}(t)$存在且单调增加。为证明定理,只需证明${N^*(t), t \ge 0}$满足3.1节中的条件(1)' ~ (4)',其中(1)'、(2)'不难由$N(t)$的相应性质继承得到。下面证明它满足(3)'、(4)'.

### Poisson过程与非齐次Poisson过程之间转换关系
记$v(t) = m^{-1}(t)$, 则
$$
N^*(t) = N(m^{-1}(t)) = N(v(t)).
$$
设$v = m^{-1}(t), v+h' = m^{-1}(t+h)$, 则由
$$
\begin{aligned}
h &= m(v+h')-m(v) \\
&= \int_v^{v+h'} \lambda(s)ds \\
&= \lambda(v)h' + o(h')
\end{aligned}
$$

### Poisson过程与非齐次Poisson过程之间转换关系
得
$$
\begin{aligned}
\lim_{h \to 0^+} \frac{P(N^*(t+h)-N^*(t)=1)}{h} &= \lim_{h' \to 0^+} \frac{P(N(v+h')-N(v)=1)}{\lambda(v)h' + o(h')} \\
&= \lim_{h' \to 0^+} \frac{\lambda(v)h' + o(h')}{\lambda(v)h' + o(h')} = 1,
\end{aligned}
$$
即
$$
P(N^*(t+h) - N^*(t) = 1) = h+o(h).
$$
同理可得
$$
P(N^*(t+h) - N^*(t) \ge 2) = o(h).
$$
所以${N^*(t), t \ge 0}$是参数为1的Poisson过程。

### 非齐次Poisson过程到Poisson过程的转换图
[Image: A graph showing a transformation of the time axis. The top x-axis is labeled 'v', the bottom x-axis is labeled 't'. A curve representing the function t=m(v) is shown. A segment [v, v+h'] on the v-axis maps to a segment [t, t+h] on the t-axis.]
图3.2 非齐次 Poisson 过程到 Poisson 过程的时间变量的转换

### 非齐次Poisson过程到Poisson过程的转换
问题1: 试直接基于Poisson过程的第一个等价定义进行论证。
问题2: 如何从强度函数为$\lambda(t)$的非齐次Poisson过程转换为强度为$\lambda$的齐次Poisson过程?

### 例3.7
**例**
设某设备的使用期限为10年,在前5年内它平均2.5需要维修一次,后5年平均2年需维修一次。试求它在使用期内只维修过一次的概率。
**解**：用非齐次Poisson过程考虑,强度函数
$$
\lambda(t) = \begin{cases} \frac{2.5}{5} & 0 \le t \le 5, \\ \frac{1}{2} & 5 < t \le 10, \end{cases}
$$
$$
m(10) = \int_0^{10} \lambda(t)dt = \int_0^5 \frac{1}{2.5} dt + \int_5^{10} \frac{1}{2} dt = 4.5.
$$
因此
$$
P(N(10)-N(0)=1) = e^{-4.5} \frac{(4.5)^1}{1!} = \frac{9}{2} e^{-\frac{9}{2}}.
$$
$\square$

### 复合Poisson过程
**定义**
称随机过程${X(t), t \ge 0}$为复合Poisson过程,如果对于$t \ge 0, x(t)$可以表示为
$$
X(t) = \sum_{i=1}^{N(t)} Y_i,
$$
其中${N(t), t \ge 0}$是一个Poisson过程, $Y_i, i = 1,2,\dots$是一族独立同分布的随机变量,并且与${N(t), t \ge 0}$也是独立的。
**注**：复合Poisson过程不一定是计数过程,但是当$Y_i = c, i = 1,2,\dots,c$为常数时,可化为Poisson过程。

### 例3.8
**例**
保险公司接到的索赔次数服从一个Poisson过程${N(t)}$,每次要求赔付的金额$Y_i$都相互独立,且有同分布$F$, 每次的索赔数额与它发生的时刻无关,则$[0,t]$时间区间内保险公司需要赔付的总金额${X(t)}$就是一个复合Poisson过程, 其中
$$
X(t) = \sum_{i=1}^{N(t)} Y_i.
$$

### 定理3.6
**定理**
设${X(t) = \sum_{i=1}^{N(t)} Y_i, t \ge 0}$是一复合Poisson过程, Poisson过程${N(t), t \ge 0}$的强度为$\lambda$, 则
(1) $X(t)$有独立增量;
(2) 若$E(Y_i^2) < +\infty$,则
$$
E[X(t)] = \lambda t \cdot EY_1, \quad var[X(t)] = \lambda t E(Y_1^2).
$$
证明:(1)令$0 \le t_0 < t_1 < t_2 < \dots < t_n$,则
$$
X(t_k) - X(t_{k-1}) = \sum_{i=N(t_{k-1})+1}^{N(t_k)} Y_i, \quad k=1,2,\dots,n,
$$
由Poisson过程的独立增量性及各$Y_i, i = 1,2,\dots,n$之间的独立性不难得出$X(t)$的独立增量性。

### 定理3.6
(2) 利用矩母函数方法,首先有
$$
\begin{aligned}
\phi_t(u) &= E(e^{uX_t}) \\
&= \sum_{n=0}^\infty E[e^{uX_t}|N(t)=n] P(N(t)=n) \\
&= \sum_{n=0}^\infty E[e^{u(Y_1+\dots+Y_n)}|N(t)=n] e^{-\lambda t} \frac{(\lambda t)^n}{n!} \\
&= \sum_{n=0}^\infty E[e^{u(Y_1+\dots+Y_n)}] e^{-\lambda t} \frac{(\lambda t)^n}{n!} \\
&= \sum_{n=0}^\infty E(e^{uY_1}) \cdots E(e^{uY_n}) e^{-\lambda t} \frac{(\lambda t)^n}{n!} \\
&= \sum_{n=0}^\infty [E(e^{uY_1})]^n e^{-\lambda t} \frac{(\lambda t)^n}{n!} \\
&= e^{\lambda t[E(e^{uY_1})-1]}.
\end{aligned}
$$
对上式求导得
$$
E[X(t)]=\lambda t EY_1 \text{ 及 } var(X(t))=\lambda t E(Y_1^2).
$$

### 复合Poisson过程
问题1: 试利用EV-VE公式计算复合Poisson过程的方差函数。
问题2: 复合Poisson过程是平稳独立增量过程吗?

### 例3.10
**例**
在保险中的索赔模型中,设保险公司接到的索赔要求是强度为每个月两次的Poisson过程。每次赔付服从均值为10000元的正态分布,则一年中保险公司平均的赔付额是多少。
**解** 由定理3.6易得
$$
E[X(12)] = 2 \cdot 12 \cdot 10000 = 240000(\text{元})
$$

### 条件Poisson过程
**定义**
设随机变量$\Lambda > 0$,在$\Lambda = \lambda$的条件下,计数过程${N(t),t \ge 0}$是参数为$\lambda$的Poisson过程。则称${N(t),t \ge 0}$为条件Poisson过程。
**注**：设$\Lambda$的分布是$G$,那么随机选择一个个体在长度为$t$的时间区间内发生$n$次事件的概率为
$$
P(N(t+s)-N(s)=n) = \int_0^\infty e^{-\lambda t} \frac{(\lambda t)^n}{n!} dG(\lambda).
$$
这是全概率公式。

### 定理3.7
**定理**
设${N(t),t \ge 0}$是条件Poisson过程,且$E(\Lambda^2) < \infty$, 则
(1) $EN(t) = t E\Lambda$;
(2) $var[N(t)] = t^2 var(\Lambda) + t E\Lambda$.
证明:
* $EN(t) = E[E(N(t)|\Lambda)] = E(t\Lambda) = tE\Lambda$
* $var(N(t)) = E[N^2(t)] - [EN(t)]^2$
$= E[E(N^2(t)|\Lambda)] - (tE\Lambda)^2$
$= E[(\lambda t)^2 + \lambda t] - t^2(E\Lambda)^2$
$= t^2 var(\Lambda) + t E\Lambda$.

### 例3.11
**例**
设意外事故的发生频率受某种未知因素影响有两种可能$\lambda_1, \lambda_2$,且$P(\Lambda = \lambda_1) = p, P(\Lambda = \lambda_2) = 1-p=q, 0 < p < 1$为已知,已知到时刻$t$已发生了$n$次事故。求下一次事故在$t+s$之前不会到来的概率。另外,这个发生频率为$\lambda_1$的概率是多少?

### 例3.11
**解** 事实上,我们不难计算
$$
\begin{aligned}
& P((t,t+s)\text{内无事故}|N(t)=n) \\
&= \frac{\sum_{i=1}^2 P(\Lambda=\lambda_i)P(N(t)=n,N(t+s)-N(t)=0|\Lambda=\lambda_i)}{\sum_{i=1}^2 P(\Lambda=\lambda_i)P(N(t)=n|\Lambda=\lambda_i)} \\
&= \frac{p(\lambda_1 t)^n e^{-\lambda_1(s+t)} + (1-p)(\lambda_2 t)^n e^{-\lambda_2(s+t)}}{p(\lambda_1 t)^n e^{-\lambda_1 t} + (1-p)(\lambda_2 t)^n e^{-\lambda_2 t}} \\
&= \frac{p\lambda_1^n e^{-\lambda_1(s+t)} + q\lambda_2^n e^{-\lambda_2(s+t)}}{p\lambda_1^n e^{-\lambda_1 t} + q\lambda_2^n e^{-\lambda_2 t}}
\end{aligned}
$$
以及
$$
P(\Lambda=\lambda_1|N(t)=n) = \frac{p e^{-\lambda_1 t} (\lambda_1 t)^n}{p e^{-\lambda_1 t}(\lambda_1 t)^n + (1-p)e^{-\lambda_2 t}(\lambda_2 t)^n}.
$$