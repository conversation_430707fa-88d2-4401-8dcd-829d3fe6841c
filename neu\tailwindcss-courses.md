- 课程
  - Tailwind Tutorial: Learn Tailwind CSS in this interactive course](https://scrimba.com/learn-tailwind-css-c010) 浏览器里直接写代码、即时预览，还有一步步的互动挑战与项目练习；不需要本地安装，非常适合零基础快速入门。
  - [Styling with utility classes - Core concepts - Tailwind CSS](https://tailwindcss.com/docs/styling-with-utility-classes) 官方教程起点
  - [Tailwind CSS v4 Full Course 2025 | Master Tailwind in One Hour](https://www.youtube.com/watch?v=6biMWgD6_JY)
    - 实用类（utility classes）的基础开始，逐步进阶到构建响应式用户界面
    - 没有高级 CSS 知识也能轻松跟上
    - Tailwind Play（一个在线代码实验平台）展示代码更改与视觉效果的即时同步，例如调整类名以实时查看样式变化。课程包括一个健身项目的实战构建、暗模式实现、响应式设计（使用媒体查询）以及部署技巧
    - 基于v4版本
  - "Tailwind CSS: From Zero to Production" on YouTube - Tailwind CSS](https://tailwindcss.com/blog/tailwindcss-from-zero-to-production) 官方出品的视频系列，讲解清晰，配套每节课源码；虽然录制于 v2，但核心理念与工作流到今天仍通用，搭配 Tailwind Play 做练习体验最佳。
- 工具
  - [Tailwind Play](https://play.tailwindcss.com/)
  - [Tailwind Battle](https://www.tailwindbattle.com/daily) 每天一个布局复制任务，使用Tailwind重建目标设计并评分。免费、交互式，适合练习响应式和组件构建。
  - [Tailwind CSS Playground](https://playcode.io/tailwind) 另一款即写即看编辑器，开箱即用、有实时预览和文件结构，适合做小练习或保存 Demo。
  - [Tailwind Trainer - A game for practicing Tailwind utility classes | Codepip](https://codepip.com/games/tailwind-trainer/) 一个免费的beta版小游戏，通过识别和应用实用类来解锁关卡，覆盖颜色、间距、Flexbox等主题。非常有趣且交互式，但假设你有基本CSS知识。