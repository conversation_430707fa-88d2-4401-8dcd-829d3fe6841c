/*
 * 'Shell commands' plugin for Obsidian.
 * Copyright (C) 2021 - 2024 <PERSON><PERSON><PERSON>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, version 3.0 of the License.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 * Contact the author (<PERSON><PERSON><PERSON>): https://github.com/Taitava/
 */

/*
 * COMMON
 */

.SC-hide {
    display: none;
}

.SC-scrollable {
    overflow-y: auto;
}

.SC-no-margin {
    margin: 0;
}

.SC-no-top-border {
    border-top: none;
}

.SC-wrappable {
    white-space: normal;
}

.SC-small-font {
    font-size: 80%;
}

/**
 * Used for indenting setting elements when they have relations to each other.
 */
.SC-indent {
    margin-left: 20px;
}

.SC-text-right {
    text-align: right;
}

/*
 * SETTING GROUPS
 * Related setting fields combined together.
 */

div.SC-setting-group div.setting-item:not(div.setting-item-heading+div.setting-item) {
    /* Remove top border from all settings in the group, except settings that come AFTER a heading setting, as it looks
     * good when there is a border line below a heading setting.
     */
    border-top: none;
}

div.SC-setting-group div.setting-item {
    padding: 5px; /* Shrink the padding. */
}

div.SC-setting-group div.setting-item:not(.setting-item-heading) div.setting-item-info, /* :not() = Don't shrink headings, they usually don't have large control elements. Heading texts themselves can be long, so don't limit them to 50% width. */
div.SC-setting-group div.setting-item:not(.setting-item-heading) div.setting-item-control {
    width: 50%; /* Make sure description does not take over 50% of the space. */
}

div.SC-setting-group div.setting-item.SC-full-description div.setting-item-info {
    width: 100%; /* Give description 100% width. Built-in variable list in settings uses this. */
}
div.SC-setting-group div.setting-item.SC-full-description div.setting-item-control {
    width: 0; /* Make room for description. */
}

div.SC-setting-group div.setting-item.SC-wide-description div.setting-item-info {
    width: max-content; /* Grow description as wide as possible, but leave some space for setting control elements. */
}
div.SC-setting-group div.setting-item.SC-wide-description div.setting-item-control {
    width: min-content; /* Make room for description. */
}

div.SC-setting-group div.setting-item input[type=text],
div.SC-setting-group div.setting-item input[type=password],
div.SC-setting-group div.setting-item input[type=search],
div.SC-setting-group div.setting-item textarea,
div.SC-setting-group div.setting-item:not(.setting-item-heading) select {
    width: 100%; /* Enlarge fields. */
    max-width: 100%; /* Remove a maximum width limitation, at least <select> elements had one. */
}

div.SC-setting-group div.setting-item textarea {
    resize: vertical; /* Allow resizing multiline text fields vertically. Don't allow horizontal resizing, because the width is already maxed out (to 50%), and horizontal resizing would just make the field accidentally narrower. */
}

div.SC-setting-group div.setting-item.SC-no-description div.setting-item-info { /* .SC-no-description is used by the 'alias' setting field. */
    width: 0;
    margin: 0;
}

div.SC-setting-group div.setting-item.SC-no-description div.setting-item-control { /* .SC-no-description is used by the 'alias' setting field. */
    width: 100%;
}


/*
 * SHELL COMMAND SETTING CONTAINERS
 */

div.SC-name-setting,
div.SC-shell-command-setting,
div.SC-preview-setting {
    padding: 0;
}

div.SC-name-setting {
    padding-top: 18px;
}

div.SC-shell-command-setting {
    border-top: none;
    padding: 5px 0; /* Padding top & bottom 5px, sides 0. */
}

div.SC-preview-setting {
    border-top: none;
    padding-bottom: 18px;
}

div.SC-shell-command-setting div.setting-item-info {
    /* Make the left cell (=div.setting-item-info) to have no space at all: */
    flex: none;
    flex-grow: unset;
    margin-right: 0;
}


/*
 * SHELL COMMAND SETTING FIELDS
 */

div.SC-shell-command-setting textarea {
    width: 100%;
}

textarea.SC-multiline {
    padding: 2px 5px; /* Shrink the padding all around the textarea. */
    resize: vertical;
}

div.SC-hotkey-info {
    white-space: nowrap;
}

div.SC-hotkey-info svg {
    vertical-align: middle; /* Not middle but close enough. */
}

small.SC-preview-shell-name {
    display: inline-block;
    margin-top: 7px;
    border-top: 1px solid var(--text-faint);
    font-weight: bold;
}

/*
 * SHELL COMMAND ICONS (not button icons, just informational icons)
 */

span.SC-main-icon-container {
    margin-left: 10px;
}


/*
 * CUSTOM VARIABLE SETTINGS
 */

div.setting-item.SC-custom-variable-name-setting div.setting-item-control::before {
    content: "{{_"; /* Wrap custom variable name field in {{_ }} */
}

div.setting-item.SC-custom-variable-name-setting div.setting-item-control::after {
    content: "}}"; /* Wrap custom variable name field in {{_ }} */
}

/*
 * CUSTOM VARIABLE VIEW
 */

li.SC-custom-variable-view-list-item {
    margin-bottom: 1em; /* Add space between variables in list. */
}


/*
 * CUSTOM SHELL SETTINGS
 */
div.SC-path-translator-setting div.setting-item-control::before {
    content: "function (absolutePath) {";
    white-space: nowrap;
}

div.SC-path-translator-setting div.setting-item-control::after {
    content: "}";
}


/*
 * MODALS (generic rules to all of the plugin's modals)
 */

div.SC-modal {
    width: max-content; /* Widen the width as much as is reasonable. Obsidian 0.16.0 made modals narrower, this aims to revert it. */
}


/*
 * PROMPT MODALS
 */

p.SC-prompt-dry-run-notice {
    font-style: italic;
    font-weight: bold;
}


/*
 * Tab elements
 * This CSS is copied 2021-10-21 from https://www.w3schools.com/howto/howto_js_tabs.asp
 * Modifications:
 *  - Renamed classes
 *  - Added tab icons.
 *  - Changed colors.
 *  - Changed/removed borders.
 *  - Removed button transition.
 *  - Changed button border-radiuses
 *  - Added margin-right rule to .SC-tab-header-button .
 */

/* Style the tab */
.SC-tab-header {
    border-bottom: 2px solid var(--background-modifier-border);
}

/* Style the buttons that are used to open the tab content */
button.SC-tab-header-button {
    background-color: unset;
    border: none;
    box-shadow: none; /* Remove a "border" that came via Obsidian 0.16.0. */
    outline: none;
    cursor: pointer;
    padding: 14px 16px;
    margin-right: 6px; /* Reduced margin. Obsidian's default margin-right for button is 12px (0 for other margins). */
    border-radius: 10px 10px 0 0; /* 0 0 = No border-radius at bottom */
}

/* Create an active/current tablink class */
button.SC-tab-header-button.SC-tab-active,
button.SC-tab-header-button:hover {
    background-color: var(--background-modifier-border);
}

.SC-tab-header-button svg {
    vertical-align: middle; /* Not middle but close enough. */
}

/* Style the tab content */
.SC-tab-content {
    display: none;
    padding: 6px 12px;
}

.SC-tab-content.SC-tab-active {
    display: block;
}


/*
 * AUTOCOMPLETE
 */

.SC-autocomplete {
    background-color: var(--background-primary-alt);
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    color: var(--text-normal);
    z-index: 100; /* Otherwise the element will be behind everything = invisible. */
    width: auto !important; /* Otherwise the element will be as wide as the text input field. */
}

.SC-autocomplete > div {
    padding: 0 5px 2px 10px;
}

.SC-autocomplete > div > span.SC-autocomplete-value {
    font-weight: bold;
}

.SC-autocomplete > div > span.SC-autocomplete-help-text {
    font-size: 90%;
    font-style: italic;
}

.SC-autocomplete > div.selected,
.SC-autocomplete > div:hover:not(.group) {
    cursor: pointer;
    background-color: var(--text-selection);
    color: var(--text-normal);
}

.SC-autocomplete > div.group {
    background-color: var(--background-modifier-border);
    padding-left: 5px;
    font-size: 75%;
}

a.SC-autocomplete-link-icon {
    margin-left: 3px;
}

a.SC-autocomplete-link-icon svg {
    vertical-align: middle; /* Not middle but close enough. */
}

/*
 * OUTPUT CHANNEL: NOTIFICATION/ERROR BALLOON.
 */
code.SC-output-channel-notification-monospace {
    font-family: var(--font-monospace), monospace;
}

/*
 * OUTPUT CHANNEL: ASK AFTER EXECUTION (OutputModal TypeScript class)
 */

div.SC-output-channel-modal-textarea-container textarea {
    width: 100%;
    min-width: 100%;
    min-height: 15em;
    resize: both;
}

div.SC-output-channel-modal-redirection-buttons-container div.setting-item-control {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px;
}

div.SC-output-channel-modal-redirection-buttons-container div.setting-item-control button {
    margin: 0;
    flex-grow: 1;
}

/*
 * 'REQUEST TO TERMINATE THE PROCESS' ICON BUTTON
 */
a.SC-icon-terminate-process { /* Generic for all terminator icons. */
    color: inherit; /* Don't use accent color. */
}

a.SC-icon-terminate-process svg {
    height: 1em;
    vertical-align: middle; /* Not middle but close enough. */
}

.notice a.SC-icon-terminate-process { /* For terminator icons in Notice balloons. */
    margin-left: 5px; /* Add space between the icon and notification text. */
    float: right; /* Position the icon to the (top) right corner. */
}

div.SC-modal-output a.SC-icon-terminate-process { /* For terminator icons in OutputChannel_Modal. */
    margin-right: 5px; /* Add space between the icon and "Executing..." text.*/
}