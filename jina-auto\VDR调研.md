## FinChat

- 没有使用 Visual Embedding 模型，因此不能用于 VDR pipeline 的参考框架；
- 但使用了基本的 图/表 分离 pipline，值得学习其如何耦合文档处理过程中的异步逻辑，并实现稳定的 pipeline   
- 结构
  - PDF文档输入
  - 页面扫描
  - **第一层：物理检测**
    - PyMuPDF图像扫描
    - 获取页面图像列表
    - 提取图像元数据
    - 图像质量检测
  - **第二层：质量过滤**
    - 尺寸检测：min(width,height) > 50px
    - 文件大小检测：size > 1024 bytes
    - 相对密度检测：data_density > threshold
  - **第三层：智能分类检测**
    - Table Transformer模型
    - 表格检测与置信度评分
    - 置信度 >= 0.88?
      - 是 → 标记为表格
      - 否 → 标记为普通图像
  - **第四层：数据标记**
    - has_table: True
    - has_image: True
    - 存储到CSV结构
  - **第五层：视觉模型触发**
    - 数据流处理
    - 检查标记
      - has_image=True → 调用图像描述模型 → Qwen2-VL图像分析
      - has_table=True → 调用表格解析模型 → Qwen2-VL表格转文本


## 更多用于 VDR 任务的嵌入模型

- ViDoRe 与 MTEB 的关系：考题（4 套视觉检索题）v.s. 考试系统（把 ViDoRe 装进自己的题库，再加 500 多道别的题）
  - **ViDoRe v1/v2 = 数据集+专题榜单**
  - **MTEB = 通用评测框架/总榜单**：ViDoRe 把自己的四个文档检索子任务“嫁接”进了 MTEB，所以你在终端里跑
     `mteb run -b "ViDoRe(v2)" …` 时，本质上还是在测 ViDoRe，只是使用 MTEB 提供的统一下载、评测与结果格式。
     ViDoRe 官方仓库已标明 **“⚠️ Deprecation → 请改用 MTEB”** 
- MRL 支持：俄罗斯套娃学习
- 其它模型：CLaMR: Contextualized Late-Interaction for Multimodal Content Retrieval
  - backbone: 主要 Qwen/Qwen2.5-VL-7B-Instruct，拓展 Qwen/Qwen2.5-Omni-3B
  - 任务：视频模态支持
  - 特性：添加了自定义投影层；使用late-interaction机制（类 ColBERT/ColPali）；支持多模态融合（统一的视觉语言骨干网络处理多种模态）
- 其它模型：Video-BERT

  - 向量结构：
  - backbone:
- **模型**：vdr-2b-multi-v1 (llamaindex)
  - 向量结构：dense **i.e.** single-vector representations
  - backbone: MrLight/dse-qwen2-2b-mrl-v1 / qwen2-vl-2b / qwen2.5 llm
  - 示例：https://qdrant.tech/documentation/multimodal-search
- **模型**：[vidore/colqwen2.5-v0.2](https://huggingface.co/vidore/colqwen2.5-v0.2)
  - DEMO：[ColPali - a Hugging Face Space by manu](https://huggingface.co/spaces/manu/ColPali-demo)
  
  - 向量结构：ColBERT-style multi-vector representations
    - 对应地，在检索时通过late interaction进行相似度计算
- **模型**：[lightonai/MonoQwen2-VL-v0.1](https://huggingface.co/lightonai/MonoQwen2-VL-v0.1)

  - multimodal reranker finetuned with LoRA from [Qwen2-VL-2B](https://huggingface.co/Qwen/Qwen2-VL-2B-Instruct)
  - rel ColQwen2: 2阶段文档检索流水线，检索器-重排序器
- **模型**：[nvidia/llama-nemoretriever-colembed-3b-v1 · Hugging Face](https://huggingface.co/nvidia/llama-nemoretriever-colembed-3b-v1)

  - 向量结构：ColBERT-style multi-vector numerical representations
  - 在Output部分说明输出参数为`[batchsize x seq length x embedding_dim]`，这表明：
    - 每个输入会产生seq_length个向量
    - 而不是单个固定维度的dense向量
  - backbone: SigLIP-vision-encoder, Llama-3.2-3B
  - paper: [[2507.05513\] Llama Nemoretriever Colembed: Top-Performing Text-Image Retrieval Model](https://arxiv.org/abs/2507.05513)
  - specs：3b基于miracle bench的中文性能0.4355远低于英文0.7363，尽管被训练为一个多语言嵌入模型
- 模型：llama-3.2-nemoretriever-1b-vlm-embed-v1 (nvidia)
  - 相同 vdr-2b 的性能（==更小尺寸？==），支持文本/图像输入
  - 向量结构：单向量表示
  - backbone：Eagle 2, SigLIP2-So400m-patch16-512 (vision encoder), Llama-3.2-1B
  - 少量指标及代码用例：[Best-in-Class Multimodal RAG: How the Llama 3.2 NeMo Retriever Embedding Model Boosts Pipeline Accuracy | NVIDIA Technical Blog](https://developer.nvidia.com/blog/best-in-class-multimodal-rag-how-the-llama-3-2-nemo-retriever-embedding-model-boosts-pipeline-accuracy/)
  - benchmark (sota) blog 页面: [ViDoRe Benchmark V2: Raising the Bar for Visual Retrieval](https://huggingface.co/blog/manu/vidore-v2)
  - 依赖问题：flash attention 2 `flash-attn`
    - [Build flash-attn takes a lot of time · Issue #1038 · Dao-AILab/flash-attention](https://github.com/Dao-AILab/flash-attention/issues/1038)
    - [[WIP\] First draft for softcapping. by Narsil · Pull Request #1025 · Dao-AILab/flash-attention](https://github.com/Dao-AILab/flash-attention/pull/1025)
- 模型：jina-embedding-v4

  - DEMO：[Jina Search Foundation API](https://jina.ai/api-dashboard/m0-image-rerank), [Colab 实现](https://colab.research.google.com/drive/1fb8jGCDPf-MXUnyXt-DNoe8_hmBDpDrl#scrollTo=M54aS0TvApyi)
  - LoRA 组件
    - **非对称检索**：将 v3 中独立的查询和段落适配器整合为一。
    - **对称相似度**：延续了 v3 的文本匹配功能，专门处理语义文本相似度（STS）任务。
    - **代码检索**：借鉴 v2-code 的成功经验，弥补了 v3 在代码支持上的空白。
  - 与jina-reranker-m0的关系：[社区供稿 | Jina Embeddings V4: 为搜索而生，多模态多语言向量模型](https://mp.weixin.qq.com/s/wy-RevzaOR9rnekw0qBHiA)
    - embedding-v4: Qwen2.5-VL-3B-Instruct
    - reranker-m0: Qwen2-VL-2B-Instruct

  - 关注 community：
    - [Can we embed multiple images and text into a single embedding?](https://huggingface.co/jinaai/jina-embeddings-v4/discussions/38) 基于模型接口探索同时嵌入文档文本+图像提升鲁棒性？——*the model is only trained to encode single images and pure text into one embedding representation*——需进一步阅读论文来理解这里提到的训练机制
    - [How to fine tune this model?](https://huggingface.co/jinaai/jina-embeddings-v4/discussions/47) 未来几周发布ft tutorial (7.2)
- 模型2：VLM2Vec 系列 (tiger-lab)

  - [[2410.05160\] VLM2Vec: Training Vision-Language Models for Massive Multimodal Embedding Tasks](https://arxiv.org/abs/2410.05160)
  - benchmark: MMEB 广泛多模态检索，包含子任务Visdoc-Overall
  - backbone: Phi-3.5-V(lora/ft-60%) | qwen2VL-2/7b-66% | LLAVA
  - 向量结构：dense单向量 “by taking the last layer vector representation of the last token”
- 模型2：doubao-embedding-vision **i.e.** [Seed1.6-Embedding](https://seed1-6-embedding.github.io/)

  - benchmark: MMEB-VisDoc 73.44%
  - 支持 **视频模态** 检索：即将上线 [火山方舟管理控制台](https://console.volcengine.com/ark/region:ark+cn-beijing/experience/embedding?type=VideoEmbedding)
  - 测试：需要自行调用api嵌入并调用api存储至数据库并创建索引
- 模型2：[Alibaba-NLP/gme-Qwen2-VL-7B-Instruct · Hugging Face](https://huggingface.co/Alibaba-NLP/gme-Qwen2-VL-7B-Instruct)

  - gme 系列(General Multimodal Embedding)
  - 向量结构：dense单向量
  - **特性：**似乎支持 *fuse-modal embedding*




## VDR benchmarks

- mteb (sub: vdr) 早期vdr模型 or 强调通用能力的模型使用该榜单附带 vdr score

- jina-vdr 基于vidore-v1进一步增加任务丰富性

- vidore-v1/v2

- [MIRACL-VISION: A Large, multilingual, visual document retrieval benchmark](https://arxiv.org/abs/2505.11651)

  - 评价数据：关注维基百科页面截图

  - 特性：多语种、多模态下对比（基于MIRACL文本嵌入bench拓展而来）

  - eg. colembed

    | 模型版本 | 中文得分 | 总体平均分 |
    |---------|---------|-----------|
    | colembed-1b | 0.3869 | 0.5414 |
    | colembed-3b | 0.4355 | 0.5841 |

    

- 个人对比，基于colembed hf页面中的3图片、3text query匹配数据：对角线为实际匹配对象（小样本参考有限，无法覆盖边缘场景）

  ```
  # jina-embedding-v4
  # 11G vRAM
  tensor([[12.4328,  4.9845,  8.2872],
          [ 6.8097, 13.8597,  9.0601],
          [ 6.8614,  7.7098,  9.2222]])
  
  # 5G vRAM
  # colembed-1b
  tensor([[13.9879, 11.4159, 12.1137],
          [11.4137, 14.6339, 12.0341],
          [ 9.9002,  9.8804, 11.3303]], device='cuda:0')
  
  # 9G vRAM
  # colembed-3b
  tensor([[13.4660, 10.1190, 11.3106],
          [11.0491, 14.4790, 11.2651],
          [ 9.2999,  9.0384, 10.9142]], device='cuda:0')
          
  # 9G vRAM
  # colqwen2.5-v0.2 (with flash-attn==latest)
  tensor([[19.7500,  9.9375, 13.2500],
          [ 9.1250, 24.3750, 13.2500],
          [12.0625, 12.6875, 17.6250]])
  
  ```

## PDF 测试

使用操作系统77-83page，覆盖cpu调度逻辑。

- pdf 到 page-wise png 转换 

```sh
pdftoppm -png *77-83.pdf 26王道操作系统

counter=77
for file in 26王道操作系统-*.png; do
    mv "$file" "26王道操作系统-$(printf %02d $counter).png"
    ((counter++))
done
```

- 进一步上传到图床的 urls，具有原始分辨率（转换关系未知，通过下载原图链接获得）
  - query: CPU第一级调度和第二级调度的关系图示
  - [jina-api-dashboard](https://jina.ai/api-dashboard/m0-image-rerank)

```
https://i.postimg.cc/J1n6gDgL/26-77.png
https://i.postimg.cc/Gc5713ws/26-78.png
https://i.postimg.cc/vM6jbnm8/26-79.png
https://i.postimg.cc/Lmm0t03h/26-80.png
https://i.postimg.cc/HmNKqzdP/26-81.png
https://i.postimg.cc/VfJhSrBR/26-82.png
https://i.postimg.cc/fwTpNgV1/26-83.png
```

![image-20250705021023231](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250705021023231.png)

![image-20250705021043532](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250705021043532.png)



## 关于 ColBERT 的介绍（key: late interaction）

- 概念定义

  - “用于视觉文档检索的嵌入向量是单向量还是多向量的？”——多向量（VDR 任务下存在少数单向量 dense 模型；相对于 late interaction）
  - eg. ColPali
    - 将文档页面图像分割成32×32网格，产生1024个图像块 patches
    - 每个图像块被投影为 128 维嵌入
    - 使用ColBERT风格的晚期交互机制(late interaction)进行相似度计算(fine-grained交互)
  - 相似度计算数学：对于查询中的每个词元 token，搜索具有最相似表示的文档补丁 patch（获得 MaxSim），然后将查询所有 token 的最相似 patch 分数相加，获得最终的查询-文档分数

- youtube 视频 7min [Text vs Vision: How Late Interaction Models Are Changing AI Search (ColBERT vs ColPali)](https://www.youtube.com/watch?v=3SsTY_3Ur18)


k

## Frameworks

- [Time to Speak Some Dialects, Qwen-TTS! | Qwen](https://qwenlm.github.io/blog/qwen-tts/)
- jina-embeddings-v4-vllm-retrieval：文本 → Token化 → 模型处理 → 每个token的向量 → 池化 → 最终嵌入

  ```py
  # 1. 模型初始化
  model = LLM(
      model="jinaai/jina-embeddings-v4-vllm-retrieval",
      task="embed",  # 指定任务为嵌入生成，而不是文本生成
      override_pooler_config=PoolerConfig(pooling_type="ALL", normalize=False),  # 使用所有token，而不是只用最后一个token；暂时不归一化，稍后手动处理
      dtype="float16",
  )
  
  
  # 2. 创建输入提示
  # 文本查询
  query_prompt = TextPrompt(prompt=f"Query: {query}")
  
  # 文本段落  
  passage_prompt = TextPrompt(prompt=f"Passage: {passage}")
  
  # 图像输入 图像处理更复杂，需要特殊的视觉token
  image_prompt = TextPrompt(
      prompt="<|im_start|>user\n<|vision_start|><|image_pad|><|vision_end|>Describe the image.<|im_end|>\n",
      multi_modal_data={"image": image},
  )
  
  
  # 3. 特殊token的含义
  # <|image_pad|>：图像占位符，实际图像数据会插入这里
  VISION_START_TOKEN_ID, VISION_END_TOKEN_ID = 151652, 151653
  
  
  # 4. 核心处理逻辑：模型输出的内部结构
  # 输出：prompt_token_ids - 输入被转换成的数字序列；outputs.data - 每个token位置的向量表示（通常是2048维）
  outputs = model.encode(prompts)
  
  
  # 5. 智能嵌入提取
  # 文本输入：所有token都有用，包括"Query:"、"Passage:"等前缀
  # 图像输入：只有视觉token包含图像信息，其他文本token是模板语言
  def get_embeddings(outputs):
      embeddings = []
      for output in outputs:
          if VISION_START_TOKEN_ID in output.prompt_token_ids:
              # 处理包含图像的输入
              img_start_pos = torch.where(...)  # 找到视觉token的开始位置
              img_end_pos = torch.where(...)    # 找到视觉token的结束位置
              # 只提取视觉相关的token嵌入
              embeddings_tensor = output.outputs.data[img_start_pos:img_end_pos+1]
          else:
              # 处理纯文本输入 - 使用所有token
              embeddings_tensor = output.outputs.data.detach().clone()
  
  
  # 6. 池化和归一化
  # 平均池化：把多个token向量合并成一个
  pooled_output = (
      embeddings_tensor.sum(dim=0, dtype=torch.float32) / embeddings_tensor.shape[0]
  )
  
  # 归一化：让向量长度为1，便于计算相似度
  embeddings.append(torch.nn.functional.normalize(pooled_output, dim=-1))
  ```

  
