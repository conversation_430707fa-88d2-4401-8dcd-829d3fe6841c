## 1. Introduction to Model Context Protocol

### 1.3 Architectural Components



### Communication Flow 通信流程

> In the next section, we’ll dive deeper into the communication protocol that enables these components with practical examples.
> 在下一节中，我们将深入探讨使这些组件得以工作的通信协议，并提供实际示例。

1. **User Interaction**: user 与 Host Application 进行交互，表达意图或 query。
2. **Host Processing**: Host 处理用户的输入，可能会使用 LLM 来理解请求并确定可能需要哪些外部能力。
3. **Client Connection**: Host 指示其 Client 组件连接到相应的服务器。
4. **Capability Discovery**: Client queries the Server，以发现其提供的 capabilities（Tools, Resources, Prompts）。
5. **Capability Invocation**: 根据用户需求或 LLM 的判断，Host 指示 Client invoke specific capabilities from the server。
6. **Server Execution**: Server 执行请求的功能并将结果返回给 Client。
7. **Result Integration**:  Client 将这些结果传回 Host，Host 将其整合到 LLM 的上下文中或直接呈现给用户。


该架构的一个关键优势是其模块化。单个 Host 可以通过不同 Clients 同时连接到多个 Servers。新的服务器可以添加到生态系统中，而无需对现有主机进行更改。不同服务器之间的功能可以轻松组合。



### 1.5 The Communication Protocol

*protocol structure and transport mechanisms used in MCP.*



#### JSON-RPC: The Foundation

在核心上，MCP 使用 JSON-RPC 2.0 作为客户端和服务器之间所有通信的消息格式。JSON-RPC 是一种轻量级的远程过程调用协议，使用 JSON 进行编码，这使得它：可读性强且易于调试、语言无关，支持在任何编程环境中实现……

The protocol defines three types of messages:

![message types](https://huggingface.co/datasets/mcp-course/images/resolve/main/unit1/5.png)

##### 1. Requests

Client to Server to initiate an operation. A message includes:

- A unique identifier (`id`)
- The **method name** to invoke (e.g., `tools/call`)
- Parameters for the method (if any)

```
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "weather",
    "arguments": {
      "location": "San Francisco"
    }
  }
}
```

##### 2. Responses

Server to Client in **reply to a Request**. A message includes:

- The same `id` as the corresponding Request
- Either a `result` (for success) or an `error` (for failure)

```
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "temperature": 62,
    "conditions": "Partly cloudy"
  }
}
```

```
{
  "jsonrpc": "2.0",
  "id": 1,
  "error": {
    "code": -32602,
    "message": "Invalid location parameter"
  }
}
```

##### 3. Notifications

One-way messages that don’t require a response. **Server to Client** to provide updates or notifications about events.

```
{
  "jsonrpc": "2.0",
  "method": "progress",
  "params": {
    "message": "Processing data...",
    "percent": 50
  }
}
```

#### Transport Mechanisms 传输机制

JSON-RPC defines the **message format**, but MCP also specifies how these messages are transported between Clients and Servers.

Two primary **transport mechanisms** are supported:

##### stdio

The stdio transport is used for local communication, where the Client and Server run on the **same machine**:
The Host application launches the **Server as a subprocess**, and communicates with it by **writing to its standard input** (stdin) and **reading from its standard output** (stdout).

**Use cases:** local tools like file system access or running local scripts.

**Advantages:** simple, no network configuration required, and securely sandboxed by the operating system.

##### HTTP + SSE / Streamable HTTP

HTTP+SSE 传输用于远程通信，其中 Client 和 Server 可能位于不同的机器上：
通信通过 HTTP 进行，服务器使用 SSE 通过持久连接 persistent connection 向 Client 推送更新。

**Use cases:** connecting to remote APIs, cloud services, or shared resources.

**Advantages:** works across networks, enables integration with web services, and is compatible with serverless environments.

##### Streamable HTTP

*Recent updates (refined):* more flexibility by allowing servers to **dynamically upgrade to SSE for streaming** when needed, while maintaining compatibility with serverless environments.



#### MCP 交互声明周期（完整）

Between Clients and Servers

- Initialization
  - **Client connects** to the Server, exchange ***protocol versions and capabilities***, Server responds with its supported *protocol version* and *capabilities*.
  - The **Client confirms** the initialization is complete via a ***notification message***.
- Discovery
  - The **Client requests** information about available capabilities and the **Server responds** with a list of **available tools**.
  - This process could be repeated for each tool, resource, or prompt type.
- Execution
  - The **Client invokes** capabilities based on the Host’s needs.
- Termination
  - The connection is gracefully closed when no longer needed, and the **Server acknowledges** the shutdown request.
  - The **Client sends** the final exit message to complete the termination.



#### Protocol Evolution

MCP 协议被设计为可扩展和适应性强的。**The initialization phase** 包括版本协商，允许协议演进时保持向后兼容。此外，**capability discovery** 使客户端能够适应每个服务器提供的特定功能，使得在同一生态系统中可以混合使用基本和高级服务器。



### 1.6 Understanding MCP Capabilities



