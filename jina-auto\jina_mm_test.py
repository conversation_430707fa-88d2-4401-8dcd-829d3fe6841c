# file: process_pdf.py
#%%
import asyncio
import base64
import httpx
import fitz  # PyMuPDF
import os
import uuid
import hashlib
from io import BytesIO
from PIL import Image
from typing import List, Dict, Any

# --- 用户配置 ---
# 重要：请将 'your_jina_api_key_here' 替换为您的真实 Jina API 密钥
# 或者设置名为 JINA_API_KEY 的环境变量
JINA_API_KEY = os.getenv("JINA_API_KEY", "jina_fb37b8f75d47454bb4eaac0668fd206417QJlBJx1vkZdH54Z3mYK7_mnKEE")
JINA_EMBEDDINGS_V4_URL = "https://api.jina.ai/v1/embeddings"
# EMBEDDING_IMAGE_DPI = 100  # layra 项目中用于 PDF 转图像的 DPI
# --------------------

# requirements.txt
# PyMuPDF==1.24.9
# httpx==0.27.0
# Pillow>=10.0.0  # 用于读取图像DPI

#%%
"""
复现自 backend/app/rag/convert_file.py 中的 pdf_to_image_and_embed 核心逻辑。
1. 将 PDF 转换为图像列表。
2. 调用 get_jina_embeddings 获取嵌入。
3. 构建最终用于存储的数据结构。
"""
pdf_path = r"C:\Users\<USER>\class6-1\jina-auto\26王道计算机组成原理_244,250,266.pdf"

knowledge_base_id = "kb-example"
source_filename = os.path.basename(pdf_path)

print(f"正在处理 PDF: {source_filename}")

# 1. PDF 逐页转为图像（PNG 格式）
doc = fitz.open(pdf_path)
image_bytes_list: List[bytes] = []
page_uuids: List[str] = []

for page_num, page in enumerate(doc):
    pix = page.get_pixmap(annots=False)
    img_bytes = pix.tobytes()
    image_bytes_list.append(img_bytes)
    
    # 生成基于页面图像哈希值的UUID
    page_uuid = str(uuid.uuid5(uuid.NAMESPACE_OID, hashlib.sha256(pix.samples).hexdigest()))
    page_uuids.append(page_uuid)
    
    with Image.open(BytesIO(img_bytes)) as img:
        width, height = img.size
        dpi = img.info.get('dpi')
        print(f"  - 第 {page_num + 1} 页的图像DPI: {dpi}")
        print(f"  - 第 {page_num + 1} 页: 分辨率 {width}x{height}")
        print(f"  - 第 {page_num + 1} 页的UUID: {page_uuid}")

doc.close()
print(f"PDF 已成功转换为 {len(image_bytes_list)} 张图片。")

# 将原始 bytes 转换为 BytesIO 对象列表以供发送
image_io_list = [BytesIO(img) for img in image_bytes_list]


#%%
# 2. 调用 Jina API 获取所有图像的嵌入
"""
复现自 backend/app/rag/get_embedding.py 中的 _get_jina_embeddings 函数。
将图像数据列表发送到 Jina API 并获取嵌入向量。
"""
headers = {"Content-Type": "application/json", "Authorization": f"Bearer {JINA_API_KEY}"}

# 将所有图像进行 base64 编码
encoded_images = []
for img_io in image_io_list:
    img_io.seek(0)
    base64_image = base64.b64encode(img_io.read()).decode("utf-8")
    encoded_images.append({"image": base64_image})

# 构建与 layra 源码一致的请求体
payload = {
    "model": "jina-embeddings-v4",
    "task": "retrieval.passage",  # 图片嵌入使用 retrieval.passage
    "return_multivector": True,  # 明确请求多向量
    "input": encoded_images
}

print(f"向 Jina API 发送 {len(encoded_images)} 张图片的嵌入请求...")

#%%
with httpx.Client() as client:
    response = client.post(
        JINA_EMBEDDINGS_V4_URL,
        headers=headers,
        json=payload,
        timeout=300.0,
    )
    response.raise_for_status()
    result = response.json()

    # 此处处理逻辑与 layra 源码完全一致：
    # 尽管请求了多向量，但程序只从响应的 "embeddings" 字段提取，
    # 期待其为一个单一向量（List[float]）。
    embeddings = []
    for item in result.get("data", []):
        embeddings.append(item["embeddings"])
    
    print(f"成功从 Jina 获取 {len(embeddings)} 个嵌入向量。")
    print(f"Token 使用量: {result.get('usage', {}).get('total_tokens', 'N/A')}")



#%%
if len(embeddings) != len(image_bytes_list):
    raise RuntimeError("获取到的嵌入向量数量与 PDF 页面数不匹配。")

# 3. 构建用于存储的最终数据结构
final_data = []
for i, embedding in enumerate(embeddings):
    chunk_data = {
        "document_id": page_uuids[i],  # 使用基于页面图像哈希值的UUID
        "knowledge_base_id": knowledge_base_id,
        "chunk_id": page_uuids[i],  # 使用相同的UUID作为chunk_id
        "page_content": image_bytes_list[i],  # 存储原始的图像二进制数据
        "metadata": {
            "source_filename": source_filename,
            "page_number": i + 1,
        },
        "embedding": embedding,
    }
    final_data.append(chunk_data)

print("已成功构建用于存储的数据结构。")

print("\n--- 处理完成 ---")
print(f"总共生成了 {len(final_data)} 个数据块 (chunks)。")

# 打印第一个数据块的结构以供查验
if final_data:
    first_chunk = final_data[0]  # 修正索引从0开始
    print("\n第一个数据块示例:")
    print(f"  - document_id: {first_chunk['document_id']}")
    print(f"  - chunk_id: {first_chunk['chunk_id']}")
    print(f"  - metadata: {first_chunk['metadata']}")
    print(f"  - page_content (bytes length): {len(first_chunk['page_content'])}")
    print(f"  - embedding (vector dimension): {len(first_chunk['embedding'])}")
# %%

