## 第六章 鞅（上）

作者 郭旭  
统计学院  
北京师范大学

# 第6章鞅

本章将介绍另一类特殊的随机过程——鞅，它们不仅在随机过程及其他数学分支中占据了重要的地位，而且在实际问题诸如金融、保险和医学上也得到了广泛的应用。在此我们将阐述鞅的一些基本理论，并以介绍离散时间的鞅为主。

### 什么是鞅

鞅论在18世纪开始流行，最初是由法国数学家Paul Pierre Levy引入了"鞅"的名称。鞅最初是一种双信下注的赌博方式，其正将"鞅"发展为数学理论的是美国数学家Joseph Leo Doob。

还有就法是这个名字来源于法国的一个小镇Martique。这个小镇的居民非常小气，他们下周美花的钱，最有可能是本周的消费。

中文的"鞅"则指马的套党，是公平博弈的数学模型。

### Doob

Joseph Leo Doob (February 27, 1910–June 7, 2004)，美国数学家，及鞅论的理论，博士生导师是Harold Hotelling让Doob和他一起工作。这使得Doob开始从事概率论的研究。Doob martingale、<PERSON><PERSON>'s martingale convergence theorems、<PERSON><PERSON>'s martingale inequality、Doob-Meyer decomposition theorem、Doob's Optional stopping theorem都可以他的名字。（请自查百科）

## 6.1 基本概念

每个赌博者自然都对从得他在一系列赌博后获得期望收益最大的策略感兴趣。然而在数学上可以证明，在"公平"的博弈中，是没有这样的赌博策略的。

考虑一个赌博者正在进行一系列赌博，每次赌博输赢的概率都是1，令$\{Y_n\}, n \geq 1$，是一列独立同分布随机变量，表示赌博结果（其中

$$P(Y_n = 1) = \frac{1}{2} = P(Y_n = -1). \tag{1}$$

这里$\{Y_n = 1\}$ ($\{Y_n = -1\}$) 表示赌博者在第$n$次赌博时的赢（输）。如果赌博者采用的赌博策略（即所下赌注）依赖于前面

的赌博结果，那么，他的赌博可以用下述的随机变量序列

$$b_n = b_n(Y_1, \cdots, Y_{n-1}), n \geq 2 \tag{2}$$

来描述。假设$b_n < \infty$是第$n$次赌局所投赌注，若赢则赢得$b_n$，若输则输掉$b_n$。

设$X_0$是赌博者初始财富，则

$$X_n = X_0 + \sum_{i=1}^n b_i Y_i \tag{3}$$

是他在第$n$次赌博后的财富。可以断言

$$E(X_{n+1}|Y_1, \cdots, Y_n) = X_n. \tag{4}$$

事实上，由式(3)我们有

$$X_{n+1} = X_n + b_{n+1}Y_{n+1},$$

因此

$$\begin{align}
E(X_{n+1}|Y_1, \cdots, Y_n) &= E(X_n|Y_1, \cdots, Y_n) + E(b_{n+1}Y_{n+1}|Y_1, \cdots, Y_n)\\
&= X_n + b_{n+1}E(Y_{n+1}|Y_1, \cdots, Y_n)\\
&\quad\text{（因为}X_n\text{与}b_{n+1}\text{对}Y_1, \cdots, Y_n\text{可测）},\\
&= X_n + b_{n+1}E(Y_{n+1}),\\
&\quad\text{（因为}\{Y_n\}\text{是独立同分布序列）},\\
&= X_n \quad\text{（因为}E(Y_{n+1}) = 0, \forall n \geq 0\text{）}.
\end{align}$$

这样证明了，如果每次赌博输赢的概率是相等的，那么赌博者在"公平"博弈中，其财富是"平均"保持不变的。这一概念正是马尔可夫财富与马尔可夫策略的概念。因此"鞅"就是对应于"公平"的赌博。

**定义6.1**

随机过程$\{X_n, n \geq 0\}$称为关于$\{Y_n, n \geq 0\}$的下鞅，如果对$n \geq 0, X_n$是$(Y_0, \cdots, Y_n)$可测，$E(X_n^+) < \infty$，且有

$$E(X_{n+1}|Y_0, \cdots, Y_n) \geq X_n, \tag{5}$$

这里$X_n^+ = \max\{0, X_n\}$。

我们称$\{X_n, n \geq 0\}$是关于$\{Y_n, n \geq 0\}$的上鞅，如果对$n \geq 0, X_n$是$(Y_0, \cdots, Y_n)$可测，$E(X_n^-) < \infty$，且有

$$E(X_{n+1}|Y_0, \cdots, Y_n) \leq X_n, \tag{6}$$

这里$X_n^- = \max\{0, -X_n\}$。

如果$\{X_n, n \geq 0\}$既是关于$\{Y_n, n \geq 0\}$的下鞅又是关于$\{Y_n, n \geq 0\}$的上鞅，则称为鞅。即对

$$E(X_{n+1}|Y_0, \cdots, Y_n) = X_n. \tag{7}$$

来说是"平均"的鞅，如果这样公司来说"因子"鞅与"无利"鞅。

下面我们将定义关于代数的鞅，为此，需要在某个概率空间$(Ω, \mathcal{F}, P)$上定义。我们在引理3这样概率空间上的$\{F_n, n \geq 0\}$是$\mathcal{F}$的一个$\sigma$子代数序列且有$F_n \subset F_{n+1}, n \geq 0$（即满足条件的$\sigma$子代数增列）。

随机序列$\{X_n, n \geq 0\}$称为$\{F_n\}$适应，如果对每个$n \geq 0, X_n$是$F_n$可测，即对任何$x \in \mathbb{R}, \{X_n \leq x\} \in F_n$。即所称$\{X_n, F_n, n \geq 0\}$是适应的。在定义6.1中的定义就：$X_n$是$(Y_0, \cdots, Y_n)$可测即$F_n = \sigma\{Y_0, \cdots, Y_n\}, n \geq 0$。而$\{F_n, n \geq 0\}$就是$\sigma$-代数增列。$X_n$是$Y_0, \cdots, Y_n$可测的条件等价于$\{X_n\}$是$\{F_n\}$适应。

**定义6.2**

$\{F_n, n \geq 0\}$是$\mathcal{F}$的一个$\sigma$子代数增列。随机序列$\{X_n, n \geq 0\}$称为关于$\{F_n, n \geq 0\}$的鞅，如果$\{X_n\}$是$\{F_n\}$适应，$E(|X_n|) < \infty$，且有对任何$n \geq 0$，有

$$E(X_{n+1}|F_n) = X_n. \tag{8}$$

我们称$\{X_n, F_n, n \geq 0\}$为下鞅，如果对任何$n \geq 0$，

$$EX_n^+ < \infty \quad \text{且} \quad E(X_{n+1}|F_n) \geq X_n. \tag{9}$$

上鞅可以类似定义。

在未来问题的情况下，常作引理与性质的一般情况。

**性质6.1**

(1) 我们称$\{X_n, F_n, n \geq 0\}$是上鞅当且仅当$\{-X_n, F_n, n \geq 0\}$是下鞅。

(2) 如果$\{X_n, F_n\}, \{Y_n, F_n\}$是两个上鞅，$a, b$是两个正常数，则$\{aX_n + bY_n, F_n\}$是上鞅。

(3) 如果$\{X_n, F_n\}, \{Y_n, F_n\}$是两个上鞅（下鞅），则$\{\max(X_n, Y_n), F_n\}$ ($\{\min(X_n, Y_n), F_n\}$) 是上鞅（下鞅）。

证明是简单的，在参考文献中。在性质中可能用到的$\sigma$子代数$\{F_n\}$可以由$\{Y_k, k = 1, 2, \cdots, n\}$生成，即$\{X_n\}$是关于$\{Y_n\}$上鞅。

下面我们定义关于代数的鞅，为此，首先介绍有关概念。设$(Ω, \mathcal{F}, P)$是完备的概率空间，我们所讨论的随机变量都定义在这个概率空间上的。$\{F_n, n \geq 0\}$是$\mathcal{F}$上的一列$\sigma$子代数且有$F_n \subset F_{n+1}, n \geq 0$（此时称它为$\sigma$子代数增列）。

随机过程$\{X_n, n \geq 0\}$称为$\{F_n\}$是适应的，如果对任意的$n \geq 0, X_n$是$F_n$可测的，即对任意$x \in \mathbb{R}, \{X_n \leq x\} \in F_n$。此时称$\{X_n, F_n, n \geq 0\}$为适应序列。在定义6.1中，定义就是$X_n$是$(Y_0, \cdots, Y_n)$可测是$F_n = \sigma\{Y_0, \cdots, Y_n\}, n \geq 0$。则$\{F_n, n \geq 0\}$一个$\sigma$代数增列。$X_n$是$Y_0, \cdots, Y_n$可测的充分必要条件是$\{X_n\}$是$\{F_n\}$适应的。

**定义6.2**

设$\{F_n, n \geq 0\}$是$\mathcal{F}$上的一个$\sigma$子代数增列。随机过程$\{X_n, n \geq 0\}$称为关于$\{F_n, n \geq 0\}$的鞅，如果$\{X_n\}$是$\{F_n\}$适应的，$E(|X_n|) < \infty$，且有对任何$n \geq 0$，有

$$E(X_{n+1}|F_n) = X_n. \tag{8}$$

适应列$\{X_n, F_n, n \geq 0\}$称为下鞅，如果对任何$n \geq 0$，

$$EX_n^+ < \infty \quad \text{且} \quad E(X_{n+1}|F_n) \geq X_n. \tag{9}$$

上鞅可以类似定义。
无论是哪种内容都与直观地联系到命题的给出。

**命题6.1**

(1) 适应列$\{X_n, F_n, n \geq 0\}$是下鞅当且仅当$\{-X_n, F_n, n \geq 0\}$是上鞅。

(2) 如果$\{X_n, F_n\}, \{Y_n, F_n\}$是两个下鞅，$a, b$是两个正常数，则$\{aX_n + bY_n, F_n\}$是下鞅。

(3) 如果$\{X_n, F_n\}, \{Y_n, F_n\}$是两个下鞅（或上鞅），则$\{\max(X_n, Y_n), F_n\}$ ($\{\min(X_n, Y_n), F_n\}$) 是下鞅（上鞅）。

证明是简单的，会作习题。在性质中可能用到的$\sigma$子代数$\{F_n\}$可以由$\{Y_k, k = 1, 2, \cdots, n\}$生成，即$\{X_n\}$是关于$\{Y_n\}$下鞅。

**例6.1** 

$X_1, X_2, \cdots$是一族零均值独立随机变量序列，且$E(|X_i|) < \infty$，令$S_0 = 0, S_n = \sum_{k=1}^n X_k$，则$\{S_n\}$是关于$F_n = \sigma(X_1, X_2, \cdots, X_n)$的鞅（关于$X_i,(i = 1, 2, \cdots)$都是独立且均值为0，则$\{M_n = S_n - n\mu\}$是关于$\{F_n\}$的鞅）。

**证明**

当$EX_i = 0$时，显然$S_n$是$F_n$可测，而且$E(|S_n|) \leq \sum_{i=1}^n E(|X_i|) < \infty$，于是

$$\begin{align}
E(S_{n+1}|F_n) &= E(X_1 + X_2 + \cdots + X_{n+1}|F_n)\\
&= E(X_1 + \cdots + X_n|F_n) + E(X_{n+1}|F_n)\\
&= S_n,
\end{align}$$

从而$\{S_n\}$满足定义6.2的 (1) 和 (2)，因此是一个关于$\{F_n\}$的鞅。同理可以证明当$EX_i = \mu \neq 0$时，$\{M_n\}$也是关于$\{F_n\}$的鞅。 $\square$

**例6.2**

考虑一个公平投掷的问题。设$X_1, X_2, \cdots$独立同分布，分布函数为

$$P(X_i = 1) = P(X_i = -1) = \frac{1}{2}$$

于是，可以将$X_i(i = 1, 2 \cdots)$看做一个投硬币的游戏的结果：如果出现正面就意义1元，出现反面则输掉1元。假设我们按以下规则来进行赌博：每次投掷硬币之前的赌注都比上一次翻一倍，直到赢了赌博即停。令$W_n$表示第$n$次赌博后所输（或赢）的总数。$W_0 = 0$，无论何时，只要赢了就停止赌博，从而$W_n$从某个起超过不再变化，于是有$P(W_{n+1} = 1|W_n = 1) = 1$。

若之$X_n$表示一个赌博者在第$n$次赌博后所有的赌资，由式(8) 表示，平均而言，他在一次赌博结束后的赌资与他开始时的赌资相等。这样说起了他开始的赌资，与他这次赌博的输赢无关。这也是正确性，向同时这体现出正确性的公平。

**例6.1** 设$X_1, X_2, \cdots$是一族零均值独立随机序列，且$E(|X_i|) < \infty$，令$S_0 = 0, S_n = \sum_{k=1}^n X_k$，则$\{S_n\}$是（关于$F_n = \sigma(X_1, X_2, \cdots, X_n)$的）鞅。另外，若$X_i, (i = 1, 2, \cdots)$均值为$\mu \neq 0$，则$\{M_n = S_n - n\mu\}$是关于$\{F_n\}$的鞅。

**证明**

当$EX_i = 0$时，显然$S_n$是$F_n$可测，而且$E(|S_n|) \leq \sum_{i=1}^n E(|X_i|) < \infty$，于是

$$\begin{align}
E(S_{n+1}|F_n) &= E(X_1 + X_2 + \cdots + X_{n+1}|F_n)\\
&= E(X_1 + \cdots + X_n|F_n) + E(X_{n+1}|F_n)\\
&= S_n,
\end{align}$$

从而$\{S_n\}$满足定义6.2的 (1) 和 (2)，因此是一个关于$\{F_n\}$的鞅。同理可以证明当$EX_i = \mu \neq 0$时，$\{M_n\}$也是关于$\{F_n\}$的鞅。

**例6.2**

考虑一个公平赌博的问题。设$X_1, X_2, \cdots$独立同分布，分布函数为

$$P(X_i = 1) = P(X_i = -1) = \frac{1}{2}$$

于是，可以将$X_i(i = 1, 2 \cdots)$看做一个投硬币游戏的结果：如果出现正面就赢意1元，出现反面则输掉1元。假设我们按以下规则来进行赌博：每次投掷硬币之前的赌注都比上一次翻一倍，直到赢了赌博即停。令$W_n$表示第$n$次赌博后所输（或赢）的总数。$W_0 = 0$，无论何时，只要赢了就停止赌博，从而$W_n$从某个起超过不再变化，于是有$P(W_{n+1} = 1|W_n = 1) = 1$。

假设前$n$次投了均为硬币出现反面，按照规则，我们已经输掉了$1+2+4+\cdots+2^{n-1} = 2^n-1$，即$W_n = -(2^n-1)$，但如果第$n+1$次硬币出现正面，则$W_{n+1} = 2^n - (2^n - 1) = 1$显示之恰好

$$P(W_{n+1} = 1|W_n = -(2^n - 1)) = \frac{1}{2}$$

$$P(W_{n+1} = -2^n - 2^n + 1|W_n = -(2^n - 1)) = \frac{1}{2}$$

易证$E(W_{n+1}|F_n) = W_n$，这里$F_n = \sigma(X_1, \cdots, X_n)$，从而$\{W_n\}$是关于$\{F_n\}$的鞅。

**例6.3**

我们可以把例6.2再一般化，设$X_1, X_2, \cdots$如例6.2中假设，赌博的下注策略与前面硬币的投掷结果，以$B_n$记第$n$次的赌注，则$B_n$是$X_1, \cdots, X_{n-1}$可测，并且$E(|B_n|) < \infty$（这样确保了每次的赌注都有一定节制），现在回到例6.2中定义，$W_0 = 0$，而有

$$W_n = \sum_{j=1}^n B_j X_j ,$$

假设现在$E(|W_n|) < \infty$（这样用对没确保了每次赌注的总数有一定节制），现在我们的事

事实上，注意到$E(|W_n|) < \infty$（这里用对$E(B_n) < \infty$），而且知$W_n$是$F_n$可测，且有

$$\begin{align}
E(W_{n+1}|F_n) &= E\left(\sum_{j=1}^{n+1} B_j X_j |F_n\right)\\
&= E\left(\sum_{j=1}^n B_j X_j |F_n\right) + E(B_{n+1}X_{n+1}|F_n)\\
&= \sum_{j=1}^n B_j X_j + B_{n+1}E(X_{n+1}|F_n)\\
&= W_n + B_{n+1}EX_{n+1}\\
&= W_n.
\end{align}$$

**例6.4**

（Polya罐子抽球模型） 考虑一个装有红、黄两色球的坛子。假设最初坛子中装有红、黄两色球各一个，每次都按如下规则有放回地抽取：如果拿出的是红色的球，则放回的同时再加入一个同色的球，如果拿出的是黄色的球也同样作法。以$X_n$表示第$n$次抽取后的红球个数。令$X_0 = 1$，$\{X_n\}$是一个具时间的Markov链，转移概率为

$$P(X_{n+1} = k + 1|X_n = k) = \frac{k}{n + 2},$$

$$P(X_{n+1} = k|X_n = k) = \frac{n + 2 - k}{n + 2}.$$

令$M_n$表示第$n$次抽取后红球所占的比例，则$M_n = \frac{X_n}{n+2}$，且有$\{M_n\}$是一个鞅。这是因为

$$E(X_{n+1}|X_n) = X_n + \frac{X_n}{n + 2}.$$

由于$\{X_n\}$是一个Markov链，则$F_n = \sigma(X_1, \cdots, X_n)$对于$X_{n+1}$的
条件分布传递&$E$方程只有当3$X_n$中，所以

$$\begin{align}
E(M_{n+1}|F_n) &= E(M_{n+1}|X_n)\\
&= E\left[\frac{X_{n+1}}{n + 1 + 2}|X_n\right]\\
&= \frac{1}{n + 3}E(X_{n+1}|X_n)\\
&= \frac{1}{n + 3}E\left(X_n + \frac{X_n}{n + 2}\right)\\
&= \frac{X_n}{n + 2} = M_n.
\end{align}$$

这个例子知的模型是Polya首次引入的，它上述有关遗传学和保险精算等方面的应用。

**例6.5**

在例6.1中设$EX_i = \mu \neq 0, E(|X_i|) < \infty$。则有$E(|S_n|) < \infty$，

$$E(S_{n+1}|F_n) = E\left(\sum_{i=1}^n X_i + X_{n+1}|F_n\right) = S_n + \mu.$$

因此，如果$\mu > 0$，$\{S_n\}$是一关于$\{F_n\}$上鞅，反之为下鞅。

### 更多例子

**似然比** 令$Y_n = \prod_{i=1}^n g(X_i)/f(X_i), g(x), f(x)$是两个密度函数。如果$X \sim f(x)$，则$Y_n$关于$\{X_1, \cdots, X_n\}$是一个鞅。

**二项树模型** 设$S_n$表示$n$时刻的股票价格，且

$$S_{n+1} = \begin{cases}
uS_n; & p = (1 - d)/(u - d)\\
dS_n; & q = 1 - p.
\end{cases}$$

**条件期望** 令$S_n = E(Y |X_1, \cdots, X_n)$，则$S_n$是一个鞅序列。

### 更多例子

**U统计量** 令$\{X_i, e_i\}_{i=1}^n$是一队i.i.d.随机向量，具有$p + 1$维特征向量，且$X_i$和$e_i$相互独立。考虑U统计量

$$T_n = \frac{1}{n(n - 1)} \sum_{j \neq i} e_i e_j X_i^T X_j .$$

定义$\eta_{in} = \sqrt{\frac{2}{n(n-1)}} \sum_{j=1}^{i-1} e_i e_j X_i^T X_j$，令$S_{kn} = \sum_{i=2}^k \eta_{in}, F_i = \sigma\{(X_j, e_j), j = 1, \cdots, i\}$。因此有$E(\eta_{in}|F_{i-1}) = 0$，进而$S_{kn}$是鞅序列。

下面可以利用这样的性理或半Jensen不等式的直接应用来说明更多的例子。考虑定义在开区间$I$上的函数$\phi(x)$，称为，如果对任意的$x, y \in I, 0 < \alpha < 1$，有

$$\alpha\phi(x) + (1 - \alpha)\phi(y) \geq \phi(\alpha x + (1 - \alpha)y) \tag{10}$$

成立。

**引理6.1**

（条件Jensen不等式） 设$\phi(x)$是实直线上的凸函数，随机变量$M$满足

(1) $E(|M|) < \infty$;

(2) $E[|\phi(M)|] < \infty$.

则有

$$E[\phi(M)|F_n] \geq \phi[E(M|F_n)], \tag{11}$$

其中$F_n$是任意上的$\sigma$代数列。

证明见[35]。式(11)是条件Jensen不等式，由它可以得到此引理。

**定理6.1**

设$\{M_n, n \geq 0\}$是关于$\{F_n, n \geq 0\}$ 的下鞅（上鞅），$\phi(x)$是$\mathbb{R}$上的凸函数，且满足$E(\phi(M_n)^+) < \infty, \forall n \geq 0$，则$\{\phi(M_n), n \geq 0\}$是关于$\{F_n, n \geq 0\}$上鞅。特别地，若$\{|M_n|, n \geq 0\}$是上鞅，$E(M_n^2) < \infty, \forall n \geq 0$时，$\{M_n^2, n \geq 0\}$也是上鞅。

证明 根据定义很易证，我们留给读者利用引理6.1进行。

### KL散度的非负性

$$\begin{align}
D_{KL}(P\|Q) &=: \int p(x) \log \frac{p(x)}{q(x)} dx = -\int p(x) \log \frac{q(x)}{p(x)} dx\\
&= E_P \left(- \log \frac{q(X)}{p(X)}\right) \geq - \log E_P \left(\frac{q(X)}{p(X)}\right) = 0.
\end{align}$$

这里$- \log(x)$是凸函数。

### 高阶矩推低阶矩

考虑函数$f(x) = x^{s/r}, s > r$，则易知$f(x)$是凸的。由Jensen不等式，可得：

$$E[f(\tilde{X})] = E[|X^r|^{s/r}] \geq f(E[\tilde{X}]) = (E|X^r|)^{s/r} .$$

在上式中，$\tilde{X} = |X|^r$。由此得到

$$(E|X^s|)^{1/s} \geq (E|X^r|)^{1/r} .$$

上式中的不等式也被称为Liapunov不等式。

### Rao-Blackwell定理

设$T_0(X)$是参数$\theta$的一个无偏估计量，$T(X)$是参数$\theta$的充分统计量。由于$T(X)$是充分统计量，因此$T_1 = E[T_0|T]$仍然能够充分估计参数$\theta$，它是一个更好的估计量。根据（条件）Jensen不等式，对于任何凸损失函数$L(\cdot)$，有

$$E[L(T_0)|T] \geq L[E(T_0|T)] = L(T_1).$$

两边求期望

$$E[L(T_0)] \geq E[L(T_1)]$$

从而得到了Rao-Blackwell定理。

### 均值和中位数的距离

设$\mu$和$m$分别是$X$的数学期望和中位数，则有

$$|\mu-m| = |E(X-m)| \leq E[|X-m|] \leq E|X-\mu| \leq \sqrt{E[(X - \mu)^2]} = \sigma$$

第一个不等式和第三个不等式基于Jensen不等式或者由概率不等式得到。一个应用是概率论中，Jensen不等式和其他概率不等式都是凸函数理想在概率论的直接应用。

## 6.2 鞅的停时定理

### 6.2.1 鞅的停时定理

本节中我们所讨论的鞅，都是指关于某个随机变量序列的鞅。所得到的结论对关于$\sigma$-代数列的鞅也是成立的，为了便于理解和应用，我们没有设求这样的一般性，对于一个关于$\{X_n\}$的鞅$\{M_n, n \geq 0\}$，我们很容易知道对$\forall n \geq 0$

$$EM_n = EM_0. \tag{12}$$

我们想知道，如果把这里的确定的由固定的时间换作一个随机变量$T$，是否仍有

$$EM_T = EM_0. \tag{13}$$

$$EM_T = EM_0. \tag{14}$$

一般地，此结论并不总是成立。但在一定的条件下可以保证成立，鞅的停时定理给出了这个结论。鞅的停时定理具有深刻的意义。在平均意义下，鞅论可以看作"公平"博弈，$M_n$表示某玩家第$n$次博弈后的财富。式 (12) 说明他在一次博弈结束后的财富与他开始时的财富相等。这也是正确的公平。

但是，这样的"赌博"是否还是"公平"的呢？停时定理将回答这一个问题的。

式（14）说明他停止时的财富和他开始时的财富相同，任何在有限时间的停止下，他可以保证他按一致到停止博弈，这一时刻是随机的。样，对于这样的结果，所以他停止定理说明了停止时的财富与他开始时的财富相同，这就是不正确的。这也如例6.2中赌博者采用的策略，不能使其最终确实得到"有利"的结果，这可以由下面的定理（14）成立。这说明他停止时的财富与他开始时的财富相同，任何在有限时间的概率内，这是不正确的条件下，这就是不正确的方法，他可以确保（14）成立。

**定理6.3**

（停时定理） $\{X_n, n \geq 0\}$是一个随机变量序列，$\sigma$子代数列$T$是关于$\{X_n, n \geq 0\}$ 的停时，如果$T$在$\{0, 1, 2, \cdots, \infty\}$上取值，且有对每个$n \geq 0$，$\{T = n\} \in \sigma(X_0, X_1, \cdots, X_n)$。

由定义我们知道$\{T = n\}$或$\{T \neq n\}$一致$A$对于$n$时刻的情况可以作判断，这意味着停时具有"不能查看未来"的特性。例如，在平均策略下投注，赌博者决定何时停止博弈只能根据当前已知道的情况，而不能基于未来还将发生的事情。停时，这是任何一个有意义的停时的基本要求。

就本停时来说有。

**例6.6**

确定时刻$T = n$是一个停时，即在赌博开始就已经确定了第$n$个之后一定结束，很显然这是一个停时。

**例6.7** （首达时） $\{X_n, n \geq 0\}$是一个随机变量序列，$A$是一个Borel集，$T(A) = \inf\{n, X_n \in A\}$，约定 $T(\emptyset) = \inf\{n, X_n \in \emptyset\} = \infty$，称$T(A)$是$\{X_n, n \geq 0\}$首次击中（或首次进入）$A$ 的时刻，称为首达时，可以证明$T(A)$是关于$\{X_n, n \geq 0\}$ 的停时，因为

$$\{T(A) = n\} = \{X_0 \notin A, X_1 \notin A, \cdots, X_{n-1} \notin A, X_n \in A\},$$

因此$\{T(A) = n\}$由$X_0, X_1, \cdots, X_n$决定，从而$T(A)$是关于$\{X_n, n \geq 0\}$ 的停时。

**例6.8** 如果$T$和$S$是两个停时，则$T+S, \min(T, S)$和$\max(T, S)$也是停时。这可以由定义直接证明出来。

**性质6.2**

$T$是任何取值于$\{0, 1, 2, \cdots, \infty\}$随机变量，则下面各条等价：

(1) $\{T = n\} \in \sigma(X_0, X_1, \cdots, X_n)$;

(2) $\{T \leq n\} \in \sigma(X_0, X_1, \cdots, X_n)$;

(3) $\{T > n\} \in \sigma(X_0, X_1, \cdots, X_n)$.

**定义6.3**

（停时） 设$\{X_n, n \geq 0\}$是一随机变量序列，称随机函数$T$是关于$\{X_n, n \geq 0\}$ 的停时，如果$T$在$\{0, 1, 2, \cdots, \infty\}$中取值，而且对每个$n \geq 0, \{T = n\} \in \sigma(X_0, X_1, \cdots, X_n)$。

由定义我们知道事件$\{T = n\}$或$\{T \neq n\}$都可以由$n$时刻及以前的信息完全确定，所以不能查看未来的信息。例如，停时博弈就说明了停时只能依靠当时已经观察到的的信息，而不能依赖于，如果我下一次要输我就现在就停止赌博，这是违反停时具一的基本要求：它预先是一个停时。

看几个停时的例子。

**例6.6**

确定时刻$T = n$是一个停时，即在赌博开始就已经确定了为$n$之后，从而停时过程是一个停时。

**例6.7** （首达时） $\{X_n, n \geq 0\}$是一个随机变量序列，$A$是一个平分集，令

$$T(A) = \inf\{n, X_n \in A\}, \quad \text{并约定} \quad T(\emptyset) = \inf\{n, X_n \in \emptyset\} = \infty,$$

可见$T(A)$是$\{X_n, n \geq 0\}$首次进入$A$ （即发生了$A$中所含的事件） 的时刻，称$T(A)$是$\{X_n, n \geq 0\}$到集合$A$的首达时，可以证明$T(A)$是关于$\{X_n, n \geq 0\}$的停时，事实上

$$\{T(A) = n\} = \{X_0 \notin A, X_1 \notin A, \cdots, X_{n-1} \notin A, X_n \in A\},$$

显然$\{T(A) = n\}$完全由$X_0, X_1, \cdots, X_n$决定，从而$T(A)$是关于$\{X_n, n \geq 0\}$的停时。

**例6.8** 如果$T$和$S$是两个停时，则$T+S, \min(T, S)$和$\max(T, S)$也是停时。这可由下面简单的命题来证明。

**命题6.2**

设$T$为任意取值于$\{0, 1, 2, \cdots, \infty\}$的随机变量，则下述三个等价

(1) $\{T = n\} \in \sigma(X_0, X_1, \cdots, X_n)$;

(2) $\{T \leq n\} \in \sigma(X_0, X_1, \cdots, X_n)$;

(3) $\{T > n\} \in \sigma(X_0, X_1, \cdots, X_n)$.

只要注意到下面等式，即可证明(1),(2),(3)的等价性：

$$\{T \leq n\} = \bigcup_{k=0}^n\{T = n\},$$

$$\{T > n\} = \Omega - \{T \leq n\},$$

$$\{T = n\} = \{T \leq n\} - \{T \leq n - 1\}.$$

这就可以证明$T + S, \max(T, S)$和$\min(T, S)$是停时。特别地，由例6.6可知$T \wedge n$是停时。$T$是停时，令$T_n = \min\{T, n\}$，则有其$T_n$都是停时，且有$T_0 \leq T_1 \leq T_2 \leq \cdots \leq T_n \leq n, \forall n$。

### 更新过程中的停时

如果在$t$之后的第一次更新即$N(t) + 1$个更新时刻，则$N(t) + 1$是一个停时。

只有$N(t) + 1 = n$当且仅当$N(t) = n-1$当且仅当$X_1 +\cdots+ X_{n-1} \leq t, X_1 +\cdots+ X_n > t$。因此事件$\{N(t)+ 1 = n\}$仅仅依靠$X_1, \cdots, X_n$。

### Wald等式

如果$X_1, \cdots,$独立同分布且$E(X) < \infty$，$T$是$\{X_1, \cdots, \}$ 的停时，且$E(T) < \infty$，则

$$E\left(\sum_{n=1}^T X_i\right) = E(X)E(T).$$

设$X_1, \cdots$ 独立同分布且$P(X_i = a) = 1/2, a = 0, 1$，令$T = \min\{n : X_1 + \cdots + X_n = 10\}$，求$E(T)$。这是个简单随机游走，需要获得—赢10次正面的期望次数。

只要注意到下面等式，即可证明(1),(2),(3)的等价性：

$$\{T \leq n\} = \bigcup_{k=0}^n\{T = n\},$$

$$\{T > n\} = \Omega - \{T \leq n\},$$

$$\{T = n\} = \{T \leq n\} - \{T \leq n - 1\}.$$

这就可以证明$T + S, \max(T, S)$和$\min(T, S)$是停时。特别地，由例6.6可知，常数$n$是停时。设$T$是停时，令$T_n = \min\{T, n\}$，则每个$T_n$都是停时，并且有$T_0 \leq T_1 \leq T_2 \leq \cdots \leq T_n \leq n, \forall n$。

### 更新过程中的停时

• 若在$t$之后的第一次更新即$N(t) + 1$次更新时刻，则$N(t) + 1$是一个停时。

• 注意到$N(t) + 1 = n$等价于$N(t) = n - 1$等价于$X_1 + \cdots + X_{n-1} \leq t, X_1 + \cdots + X_n > t$。因而事件$\{N(t) + 1 = n\}$只依赖于$X_1, \cdots, X_n$。

### Wald等式

• 若$X_1, \cdots,$独立同分布且$E(X) < \infty$，$T$是$\{X_1, \cdots, \}$ 的停时，且$E(T) < \infty$，则

$$E\left(\sum_{n=1}^T X_i\right) = E(X)E(T).$$

• 设$X_1, \cdots$ 独立同分布且$P(X_i = a) = 1/2, a = 0, 1$，令$T = \min\{n : X_1 + \cdots + X_n = 10\}$，求$E(T)$。即求连续投掷一枚硬币，首次出现10次正面所需的平均次数。

**命题6.3**

设$M_0, M_1, \cdots$是一个关于$X_0, X_1, \cdots$的鞅，$T$是关于$X_0, X_1, \cdots$的停时且有界，$T \leq K$，$F_n = \sigma(X_0, X_1, \cdots, X_n)$，则

$$E(M_T|F_0) = M_0,$$

特别地

$$E(M_T) = E(M_0).$$

**证明** 由于$T \leq K$，即$T$有界有限，且当$T = j$时$M_T = M_j$，我们可以表示$M_T$为

$$M_T = \sum_{j=0}^K M_j I_{\{T=j\}}. \tag{15}$$

对式(15)关于$F_{K-1}$取条件期望，有

$$\begin{align}
E(M_T|F_{K-1}) &= E\left(\sum_{j=0}^K M_j I_{\{T=j\}}|F_{K-1}\right)\\
&= E\left(\sum_{j=0}^{K-1} M_j I_{\{T=j\}}|F_{K-1}\right) + E(M_K I_{\{T=K\}}|F_{K-1})
\end{align}$$

当$j \leq K - 1$时$M_j$和$I_{\{T=j\}}$都是$F_{K-1}$可测，从而

$$E\left(\sum_{j=0}^{K-1} M_j I_{\{T=j\}}|F_{K-1}\right) = \sum_{j=0}^{K-1} M_j I_{\{T=j\}}.$$

另外$\{T = K\}$与$\{T > K - 1\}$是相同，由例6.8中（或命题6.2）$\{T > K - 1\} \in \sigma(X_0, \cdots, X_{K-1})$，因此

$$E(M_K I_{\{T=K\}}|F_{K-1}) = I_{\{T>K-1\}} \cdot E(M_K|F_{K-1}) = I_{\{T>K-1\}} \cdot M_{K-1},$$

从而

$$\begin{align}
E(M_T|F_{K-1}) &= I_{\{T>K-1\}}M_{K-1} + \sum_{j=0}^{K-1} M_j I_{\{T=j\}}\\
&= I_{\{T>K-2\}} \cdot M_{K-1} + \sum_{j=0}^{K-2} M_j I_{\{T=j\}}.
\end{align}$$

重复以上运算，关于$F_{K-2}$取条件期望，有

$$E(M_T|F_{K-2}) = E[E(M_T|F_{K-1})|F_{K-2}] = I_{\{T>K-3\}} \cdot M_{K-2} + \sum_{j=0}^{K-3} M_j I_{\{T=j\}}.$$

继续这样过程，最终有

$$E(M_T|F_0) = I_{\{T \geq 0\}} \cdot M_0 = M_0.$$

这样命题是停时定理的一种特殊情况，可以看出它保证了条件下，可以得到我们所希望的结论。作为推论，特别当$P(T < \infty) = 1$（即T有限）且$P(T \leq K) = 1, \forall K$（一些条件下）。

但是这样的限制是很苛刻的。但在一定的条件下它仍可以保证结论成立。

考虑停时$T_n = \min\{T, n\}$，注意到

$$M_T = M_{T_n} + M_T I_{\{T>n\}} - M_n I_{\{T>n\}},$$

从而

$$EM_T = EM_{T_n} + E(M_T I_{\{T>n\}}) - E(M_n I_{\{T>n\}}).$$

可以看出，$T_n$是一个有界停时（$T_n \leq n$），由上面命题可知$E(M_{T_n}) = E(M_0)$。我们希望当$n \to \infty$时，右上面两项能抵消。容易看出，如果$M_T$有界或者$T$有限时，总存在整数$\{T>n\}$因为$\{M_n\}$的Doob分解，若$\lim_{n\to\infty}E(|M_n|I_{\{T>n\}}) = 0$，则我们可以得到希望有$E(|M_T|) < \infty$成立。这样的条件下而利用Fatou引理或者Lebesgue控制收敛定理，可以证明$E(M_T I_{\{T>n\}}) \to 0$。

**定理6.2**

（鞅的停时定理） 设$M_0, M_1, M_2, \cdots$是一个关于$\{F_n = \sigma(X_0, X_1, \cdots, X_n)\}$的鞅，$T$是停时且满足

(1) 
$$P(T < \infty) = 1; \tag{16}$$

(2) 
$$E(|M_T|) < \infty \tag{17}$$

(3) 
$$\lim_{n\to\infty} E(|M_n|I_{\{T>n\}}) = 0. \tag{18}$$

则有

$$EM_T = EM_0.$$

### 停止过程$M_{T_n}$

• 注意到$T_n = \min(T, n)$，且

$$M_{T_n} = M_{T_{n-1}} + I(T > n - 1)(M_n - M_{n-1}).$$

• 当$T > n - 1$时，$T_n = n, T_{n-1} = n - 1$，则有

$$M_n = M_{n-1} + 1 \cdot (M_n - M_{n-1}).$$

• 当$T \leq n - 1$时，$T_n = T_{n-1} = T$，则有

$$M_T = M_T + 0 \cdot (M_n - M_{n-1}).$$

$$E(M_{T_n}|F_{n-1}) = M_{T_{n-1}}+I(T > n-1)E(M_n-M_{n-1}|F_{n-1}) = M_{T_{n-1}}$$

第三项要求涉及一些，考虑前面的例6.2，在这个例子中，事件$\{T > n\}$相当于下面事件：前$n$次投掷硬币，均出现反面。这个概率是$(\frac{1}{2})^n$，如果这个事件发生了，则至少输掉$T 2^n - 1$元，即$M_n = 1 - 2^n$，从而

$$E(M_n I_{\{T>n\}}) = 2^{-n}(1 - 2^n).$$

当$n \to \infty$时，上式并不趋于0，这也是为什么停时定理的结论在此处不成立的原因。然而如果$M_n$和$T$满足

$$\lim_{n\to\infty}E(|M_n|I_{\{T>n\}}) = 0,$$

我们就可以得到结论$EM_T = EM_0$。我们把这个过程写成下面的停时定理。

**定理6.2**

（鞅停时定理） 设$M_0, M_1, M_2, \cdots$是一个关于$\{F_n = \sigma(X_0, X_1, \cdots, X_n)\}$的鞅，$T$是停时且满足

(1) 
$$P(T < \infty) = 1; \tag{16}$$

(2) 
$$E(|M_T|) < \infty \tag{17}$$

(3) 
$$\lim_{n\to\infty} E(|M_n|I_{\{T>n\}}) = 0. \tag{18}$$

则有

$$EM_T = EM_0.$$

**例6.9**

设$X_n$是在$\{0, 1, \cdots, N\}$上的简单随机游走（$p = \frac{1}{2}$），并且0和$N$为两个吸收态。设$X_0 = a$，则$X_n$是一个鞅（（关于其轨道而言）。令$T = \min\{j : X_j = 0$或$N\}$，则$T$是一个停时，由于$X_n$取状态空间，条件(17)(18)满足（只有$\phi(x)$是有界的，而第二个不等式则由于中位数是使得$E|X-a|$最小的值。

通过从上面的推导方法，无论是对鞅定理的停时定理的意义

$$E(X_T) = E(X_0) = a.$$

由于此时$X_T$仅取两个值$N, 0$，

$$E(X_T) = N \cdot P(X_T = N) + 0 \cdot P(X_T = 0),$$

从而

$$P(X_T = N) = \frac{E(X_T)}{N} = \frac{a}{N}.$$

这就是吸收概率的经典结果。

**例6.10** 令$X_n$和$T$如例6.9中假设，但定义$M_n = X_n^2-n$，则$\{M_n\}$是关于$\{X_n\}$的鞅。这是因为

$$E(M_{n+1}|F_n) = E[X_{n+1}^2 - (n + 1)|F_n] = X_n^2 + 1 - (n + 1) = X_n^2 - n = M_n,$$

易见，此时$M_n$不是一个有界鞅，不能直接得出式 (17), (18)成立，但是可以证明，存在$C < \infty, \rho < 1$，使得

$$P(T > n) \leq C\rho^n. \tag{19}$$

因为$|M_n| \leq N^2 + n$，从而$E(|M_T|) < \infty$，且有

$$E(|M_n|I_{\{T>n\}}) \leq C\rho^n(N^2 + n) \to 0,$$

从而停时定理的条件满足，我们有结论

$$EM_T = EM_0 = a^2$$

注意到

$$EM_T = E(X_T^2) - ET = N^2P(X_T = N) + 0 \cdot P(X_T = 0) - ET = aN - ET$$

从而

$$ET = aN - a^2 = a(N - a).$$

这是吸收过程中的平均首达时间。

**注6.1** 式 (19) 是Markov链中的一个问题，设$\{X_n\}$是一个不可约的Markov链，状态空间为$\{0, 1, \cdots, N\}$，但存在$C < \infty, \rho < 1$，使得

$$P(X_m \neq j, m = 0, 1, \cdots, n|X_0 = i) \leq C\rho^n.$$

**例6.11**

令$X_n$是一个在$\{\cdots, -1, 0, 1, \cdots\}$上的简单随机游走（$p = \frac{1}{2}$），我们已经知道，这样简单随机游走是常返的，从而$P(T < \infty) = 1$。令$T = \min\{j : X_j = 1\}$。我们已经知道，这样简单随机游走的期望首达时间是无穷，从而$P(T < \infty) = 1$，但是$E(T) = \infty$。然而$X_T = 1$从而$EX_T = 1 \neq 0 = EX_0$，停时定理不成立。 $\square$

这停时定理不适用，我们已看到这样的反例不能给一些条件。

**定理6.3**

设$\{M_n, n \geq 0\}$是关于$\{X_n, n \geq 0\}$ 的下鞅，$T$是关于$\{X_n, n \geq 0\}$ 的停时，$T_n = \min(T, n)$，在某个确有界随机变量$W$，满足$EW < \infty$，且有

$$M_{T_n} \geq -W, \forall n \geq 0.$$

则有

$$EM_0 \geq E(M_T I_{\{T<\infty\}}).$$

特别地，如果$P(T < \infty) = 1$，则有

$$EM_0 \geq EM_T .$$

证明略。

**推论6.1**

设$\{M_n, n \geq 0\}$是关于$\{X_n, n \geq 0\}$的上鞅，$T$是关于$\{X_n, n \geq 0\}$ 的停时，且$M_n \geq 0$，则有

$$EM_0 \geq E(M_T I_{\{T<\infty\}}).$$

我们已经知道对于上鞅，有$EM_n \leq EM_0, \forall n \geq 0$，此处上鞅停时定理说明当极限为有界停时$T$时，在附加某些条件的前提下，结论也成立。


# 鞅补充材料


## 一致可积性及其意义

本文对一致可积性(uniform integrability, UI)及其意义进行介绍。

### Part1 动机

首先讨论下为什么希望提出这样一个概念。这里讨论两个问题。

第一，很多时候我们关心：当随机变量序列 $X_n$ 依分布或者依概率收敛到 $X$ 时，是否也有 $E(X_n)$ 收敛到 $E(X)$？

根据Portmanteau引理，$X_n \stackrel{d}{\to} X$ 等价于对所有有界连续函数 $E(g(X_n)) \to E(g(X))$。但 $g(x)=x$ 连续却不有界，因而无法判定是否有 $E(X_n) \to E(X)$ 成立。实际上，令 $X_n$ 是 $[0,n]$ 上的均匀随机变量，$X=0$。可知 $X_n \stackrel{P}{\to} 0$，但 $E(X_n)=n/2 \to \infty$。这表明除了随机变量序列收敛以外还需要添加额外的条件才能保证对应的数学期望也收敛。那么条件会是什么呢？

第二，在鞅停时定理中，条件 $E(|X_T|) < \infty$ 很难验证，能否给出更加容易验证的条件呢？这里 $\{X_t\}$ 是一个鞅序列，而 $T$ 则是一个停时。这里的困难在于 $X_T$ 和 $T$ 都会随着 $\omega$ 的变动而变动。尽管当 $P(T < \infty)=1$ 也即 $T$ 有限时可以控制 $T$ 的概率，但 $X_T$ 的取值仍可能很大，从而使得该条件失效。

将发现所需的条件是一致可积性。下面给出一致可积性的定义。

### Part2 定义

如果随机变量序列 $\{X_n\}$ 满足

$$\lim_{a \to \infty} \sup_n E(|X_n|I(|X_n|>a)) = 0$$

则称随机变量序列 $\{X_n\}$ 是一致可积的。

为什么这么定义呢？实际上，根据单调收敛定理，可得对任意期望存在的随机变量 $X$，有

$$\lim_{a \to \infty} E(|X|I(|X|>a)) = 0$$

也就是远尾对期望的贡献非常小。因而一致可积性概念是上述结果推广到随机变量序列时的一致性版本。

### Part3 充要条件

下面给出一致可积性的充要条件。

**定理1：** 随机变量序列 $\{X_n\}$ 是一致可积的等价于
(1). $\sup_n E(|X_n|) < \infty$ 和
(2). 对任意 $\varepsilon > 0$，存在 $\delta > 0$，对任意事件 $A$，当 $P(A) < \delta$ 时，有 $\sup_n E(|X_n|I_A) < \varepsilon$。

证明： 由于随机变量序列 $\{X_n\}$ 是一致可积的，因而对任意 $\varepsilon > 0$，存在 $M > 0$，当 $a > M$ 时，$\sup_n E(|X_n|I(|X_n|>a)) < \varepsilon$。于是有

$$\sup_n E(|X_n|) = \sup_n [E(|X_n|I(|X_n| \leq M)) + E(|X_n|I(|X_n| > M))] \leq M + \varepsilon < \infty$$

从而(1)成立。而对于(2)，注意到：

$$E(|X_n|I_A) = E(|X_n|I_A I(|X_n| \leq a)) + E(|X_n|I_A I(|X_n| > a)) \leq a P(A) + E(|X_n|I(|X_n| > a))$$

因为 $\{X_n\}$ 一致可积，故可选择 $a$ 使得 $\sup_n E(|X_n|I(|X_n| > a)) < \varepsilon/2$。现令 $\delta = \varepsilon/(2a)$，则 $P(A) < \delta$ 意味着 $a P(A) < \varepsilon/2$，从而得到

$$\sup_n E(|X_n|I_A) \leq \varepsilon/2 + \varepsilon/2 = \varepsilon$$

令 $A = \{|X_n| > a\}$，则

$$E(|X_n|I(|X_n| > a)) = E(|X_n|I_A)$$

则由(2)可知 $\lim_{a \to \infty} \sup_n E(|X_n|I(|X_n| > a)) = 0$。从而可得一致可积性。

上述结果表明一致可积性要强于期望一致有限。仅有期望一致有限是得不出一致可积的。对于 $X_n = n \cdot I(U \leq 1/n)$，其中 $U \sim U[0,1]$，其期望都是1，但该序列不是一致可积的。

### Part4 充分条件

上述充要条件仍然不易验证。下面给出一致可积性的一个充分条件。

**定理2：** 如果存在一个常数 $p > 1$，使得 $\sup_n E(|X_n|^p) < \infty$，则随机变量序列 $\{X_n\}$ 是一致可积的。

证明： 注意到：

$$E(|X_n|I(|X_n| > a)) = E(|X_n| \cdot I(|X_n| > a)) \leq E(|X_n| \cdot |X_n|^{p-1}/a^{p-1}) = E(|X_n|^p)/a^{p-1}$$

因而

$$\sup_n E(|X_n|I(|X_n| > a)) \leq \sup_n E(|X_n|^p)/a^{p-1} \to 0 \quad (a \to \infty)$$

从而得证。

上述结果表明，尽管期望一致有限无法推出一致可积性，但一致可积性实际上也没有强太多。

下述结果表明如果随机变量序列 $\{X_n\}$ 被一个期望存在的随机变量控制住，则 $\{X_n\}$ 是一致可积的。请学生对此予以论证。

**定理3：** 如果存在一个随机变量 $Y$，使得 $|X_n| \leq Y$ 且 $E(Y) < \infty$，则随机变量序列 $\{X_n\}$ 是一致可积的。

### Part5 一致可积的意义

现在基于一致可积性回答开始的问题。

**定理4：** 若 $E|X_n| < \infty$，$X_n \stackrel{P}{\to} X$，则以下论述是等价的
(1). $E(|X_n|) \to E(|X|)$;
(2). $E(|X_n - X|) \to 0$;
(3). 随机变量序列 $\{X_n\}$ 是一致可积的。

证明见Durrett，Probability: Theory and Examples，Theorem 4.6.3.

由Markov不等式可知，$L_1$ 收敛（2）可推出依概率收敛。上述结果则给出了逆命题。在序列一致可积条件下，依概率收敛也可以推出 $L_1$ 收敛，同时也有期望收敛。实际上，根据Jensen不等式：

$$|E(X_n - X)| \leq E|X_n - X|$$

因而 $L_1$ 收敛可得期望收敛。

另外由定理3可知，控制收敛定理是定理4的一个特例。

对于鞅停时定理，则有下述结果。

**定理5：** 若鞅序列 $\{M_n\}$ 是一致可积的，$T$ 是停时，满足 $P(T < \infty) = 1$，且 $E(|M_T|) < \infty$，则停时定理成立，即此时有 $E(M_T) = E(M_0)$。

当 $T$ 有限且 $\{M_n\}$ 一致可积时，根据定理1可知 $E(M_n I(T > n)) \to 0$ 成立，因而停时定理成立。

## Wald等式、停时和鞅

本文对Wald等式进行简介。Wald等式讨论随机个随机变量和的计算问题。这里的Wald就是提出了Wald test的匈牙利数学家和统计学家Abraham Wald。他在二战中也揭示了"幸存者偏差"现象。

我们以一个有趣的问题开始。求以下代码返回值sum的期望值。

```
sum=0
while(sum<=1){
  sum=sum+runif(1,0,1)
}
sum
```

上述问题来自知乎上的一个讨论。这个问题涉及Wald等式，停时和鞅等概念。这个问题比较有趣因而很适合作为授课的例子。注意到上述程序的停止条件为sum>1。基于此，首先给出一些记号。

记 $X_i \stackrel{\text{iid}}{\sim}U[0,1]$，定义停止时刻 $T = \min\{n: \sum_{i=1}^n X_i > 1\}$，所要求的即为 $E(S_T)$，其中 $S_n = \sum_{i=1}^n X_i$。由于 $S_T$ 和 $T$ 都是随机变量且 $S_T$ 依赖于 $T$，因而无法直接使用双期望公式来求解 $E(S_T)$ 的期望。

### 一、停时

对此，首先给出停时的概念。

停时: 设 $\{X_n, n \geq 1\}$ 是一随机变量序列，称随机函数 $T$ 是关于 $\{X_n\}$ 的停时，如果 $T$ 在 $\{1,2,\ldots\} \cup \{\infty\}$ 中取值，而且对每个 $n \geq 1$，$\{T=n\} \in \mathcal{F}_n$。这里 $\mathcal{F}_n$ 表示 $\sigma(X_1, \ldots, X_n)$ 代数。

也就是说若 $T$ 是停时，则 $\{T=n\}$ 这一事件完全由 $X_1, \ldots, X_n$ 确定。停时的一个典型例子是首达时。具体地，

首达时: $\{X_n, n \geq 1\}$ 是一个随机变量序列，$A$ 是一个事件集，令

$$T_A = \inf\{n \geq 1: X_n \in A\}$$

可见 $T_A$ 是首次进入（即发生了 $A$ 中所含的事件）的时刻，称 $T_A$ 是 $\{X_n\}$ 到集合 $A$ 的首达时，可以证明 $T_A$ 是关于 $\{X_n\}$ 的停时，事实上

$$\{T_A = n\} = \{X_1 \notin A, \ldots, X_{n-1} \notin A, X_n \in A\}$$

显然 $\{T_A = n\}$ 完全由 $X_1, \ldots, X_n$ 决定，从而 $T_A$ 是关于 $\{X_n\}$ 的停时。

回到我们所讨论的问题，容易看出停止时刻 $T = \min\{n: \sum_{i=1}^n X_i > 1\}$ 就是一个首达时，因而也是一个停时。

### 二、Wald等式

在更新过程的学习中，我们有：$E(S_{N(t)}) = E(N(t)) \cdot E(X)$，这里 $N(t)$ 是 $[0,t]$ 内更新发生的次数，而 $X_i$ 是更新的时间间隔。这一结果被称为Wald等式。可以论证 $N(t)$ 是一个停时，实际上注意到 $\{N(t) = k\}$ 等价于 $\{S_k \leq t < S_{k+1}\}$ 等价于 $\{S_k \leq t, X_{k+1} > t - S_k\}$。因而事件 $\{N(t) = k\}$ 只依赖于 $X_1, \ldots, X_k, X_{k+1}$。从而更新过程中的Wald等式是下述更一般的Wald等式的特例。

Wald等式：若 $X_i$ 独立同分布且 $E(X_i) = \mu$，$T$ 是 $\{X_i\}$ 的停时，且 $E(T) < \infty$，则

$$E\left(\sum_{i=1}^T X_i\right) = E(T) \cdot E(X_1) = E(T) \cdot \mu$$

证明1： 令 $Y_i = X_i I(T \geq i)$，则

$$\sum_{i=1}^T X_i = \sum_{i=1}^{\infty} X_i I(T \geq i) = \sum_{i=1}^{\infty} Y_i$$

令 $Z_n = \sum_{i=1}^n Y_i$，$Z = \sum_{i=1}^{\infty} Y_i$，则由单调收敛定理可知 $E(Z) = \lim_{n \to \infty} E(Z_n)$。也就是

$$E\left(\sum_{i=1}^T X_i\right) = E\left(\sum_{i=1}^{\infty} Y_i\right) = \sum_{i=1}^{\infty} E(Y_i)$$

即级数求和与期望可交换顺序。

另一方面注意到 $Y_i = X_i I(T \geq i)$。而由于 $T$ 是停时，因而事件 $\{T \geq i\}$ 完全由 $X_1, \ldots, X_{i-1}$ 确定。进一步可知 $I(T \geq i)$ 和 $X_i$ 独立。从而有

$$E(Y_i) = E(X_i I(T \geq i)) = E(X_i) \cdot E(I(T \geq i)) = \mu \cdot P(T \geq i)$$

若定义 $P(T \geq i) = E(I(T \geq i))$，易知 $\sum_{i=1}^{\infty} P(T \geq i) = E(T)$，从而由控制收敛定理可知

$$E\left(\sum_{i=1}^T X_i\right) = \sum_{i=1}^{\infty} E(Y_i) = \sum_{i=1}^{\infty} \mu \cdot P(T \geq i) = \mu \cdot E(T)$$

对于Wald等式，也可以基于鞅的停时定理加以论证。在文稿一致可积性及其意义中，我们介绍了一致可积性的概念，它的充要条件，充分条件以及它的作用。其中如果所考虑的随机变量序列被一个可积的随机变量控制住，则该随机变量序列是一致可积的。

我们有以下结论：

一致可积鞅的停时定理： 若鞅序列 $\{M_n\}$ 是一致可积的，$T$ 是停时，满足 $P(T < \infty) = 1$，则停时定理成立，即此时有 $E(M_T) = E(M_0)$。

现在基于上述停时定理来论证Wald等式。

证明2： 令 $M_n = S_n - n\mu$，这里 $S_n = \sum_{i=1}^n X_i$。易知 $\{M_n\}$ 序列是一个鞅。令 $M_n(\omega) = M_{n \wedge T}(\omega)$，和

$$S_n^T = \sum_{i=1}^{n \wedge T} X_i$$

这表明 $M_n^T$ 是 $M_n$ 所对应的停止过程，因而也是一个鞅过程。

注意到证明1中的 $Y$ 满足 $|Y_i| \leq |X_i|$。

另外

$$|M_n^T| \leq \sum_{i=1}^T (|X_i| + |\mu|) \leq \sum_{i=1}^{\infty} (|X_i| + |\mu|) \cdot I(T \geq i)$$

而后者 $\sum_{i=1}^{\infty} (|X_i| + |\mu|) \cdot I(T \geq i)$ 的期望等于 $(E|X_1| + |\mu|) \cdot E(T)$。因而 $\{M_n^T\}$ 是一致可积的。

根据停时定理，$E(M_T) = E(M_0) = 0$，从而得到Wald等式。

### 三、程序返回值期望的求解

回到我们最初的问题。根据Wald等式可知 $E(S_T) = E(T) \cdot E(X) = E(T)/2$。

下求 $E(T)$。我们有

$$E(T) = \sum_{n=0}^{\infty} P(T > n) = \sum_{n=0}^{\infty} P(X_1 + \cdots + X_n \leq 1) = \sum_{n=0}^{\infty} \frac{1}{n!} = e$$

倒数第二个等号可由数学归纳法得到。

从而得出 $E(S_T) = e/2$。

## 鞅停时定理在BH方法中的作用

在之前的文章中，我们对BH方法进行了简介，并论证了该方法在p值相互独立时可以控制错误发现率(False Discovery Rate, FDR)。这里给出另一种基于鞅停时定理的论证方式。本文主要参考Emmanuel Candes教授课程讲义的第八章。这一证明思路最初由Storey et al. (2004, JRSSB)给出。

### 1. BH方法

对所考虑的 $m$ 个假设 $H_i$'s，可得到相应的 $p$ 值 $p_i$'s。BH方法操作如下：
1. 将 $p$ 值进行排序，得到：$p_{(1)} \leq p_{(2)} \leq \cdots \leq p_{(m)}$。
2. 定义 $\hat{k} = \max\{k: p_{(k)} \leq \frac{\alpha k}{m}\}$.
3. 若 $\hat{k}$ 存在，拒绝 $H_{(1)}, \ldots, H_{(\hat{k})}$。

下面从过程的角度理解上面的操作方法。定义如下"经验"分布函数

$$\hat{F}(t) = \frac{1}{m} \sum_{i=1}^m I(p_i \leq t)$$

在BH方法中的阈值为 $\hat{t} = \frac{\alpha \hat{k}}{m}$。注意到：

$$\hat{k} = \max\{k: p_{(k)} \leq \frac{\alpha k}{m}\} = \max\{k: \hat{F}(p_{(k)}) \geq \frac{k}{m} = \hat{F}(p_{(k)}) \cdot \frac{\alpha}{\hat{F}(p_{(k)})} \cdot \frac{k}{m}\}$$

从而BH方法等价于拒绝所有 $p_i \leq \hat{t}$ 的假设 $H_i$，这里

$$\hat{t} = \max\{t: \hat{F}(t) \geq \frac{t}{\alpha}\}$$

考虑某个给定的阈值 $t$，当 $p_i \leq t$ 时拒绝原假设，因而 $R(t) = \sum_{i=1}^m I(p_i \leq t)$ 是所有拒绝的个数，而错误拒绝的个数 $V(t) = \sum_{i \in \mathcal{H}_0} I(p_i \leq t)$。这里 $\mathcal{H}_0$ 是原假设为真的假设的集合，同时记 $m_0 = |\mathcal{H}_0|$。由于 $\mathcal{H}_0$ 是未知的，因而 $V(t)$ 也是未知的需要进行估计。注意到当原假设为真时，p值服从均匀分布，因而 $E(V(t)) = m_0 t$。若进一步假定信号是稀疏的，则可用总的假设个数 $m$ 近似原假设为真的假设个数 $m_0$。

上述讨论表明在 $\hat{t} = \max\{t: \hat{F}(t) \geq \frac{t}{\alpha}\}$ 的定义中，$\frac{V(t)}{R(t)} \approx \frac{m_0 t}{m \hat{F}(t)} \approx \frac{t}{α \hat{F}(t)}$ 是错误发现率 $\frac{V(t)}{R(t)}$ 的合理估计。FDR被定义为FDP的期望。

### 2. 基于鞅停时定理论证FDR控制

**定理:** 若 $p_i$ 相互独立，则BH方法能够控制FDR使得：

$$\text{FDR}(\hat{t}) = E\left(\frac{V(\hat{t})}{R(\hat{t})} \vee 1\right) \leq \frac{m_0}{m} \alpha \leq \alpha$$

证明: 定义

$$\mathcal{F}_t = \sigma(V(s), R(s): t \leq s \leq 1)$$

下面将说明 $\{V(t)/t, 0 \leq t \leq 1\}$ 是一个反向鞅。

首先注意到，s<=t时

$$E(I(p_i \leq s)|\mathcal{F}_t) = E(I(p_i \leq s)|I(p_i \leq t)) = \frac{s}{t}I(p_i \leq t)$$

从而可得

$$E\left(\frac{V(s)}{s}|\mathcal{F}_t\right) = \frac{1}{s}E\left(\sum_{i \in \mathcal{H}_0} I(p_i \leq s)|\mathcal{F}_t\right) = \frac{1}{s} \sum_{i \in \mathcal{H}_0} E(I(p_i \leq s)|\mathcal{F}_t) = \frac{1}{s} \sum_{i \in \mathcal{H}_0} \frac{s}{t}I(p_i \leq t) = \frac{V(t)}{t}$$

从而说明 $\{V(t)/t, 0 \leq t \leq 1\}$ 是一个反向鞅。

同时注意到 $\hat{t}$ 是一个停时。根据 $\hat{t}$ 的定义，$R(\hat{t}) \vee 1 = \hat{t} m / \alpha$。由于停时有界，从而根据停时定理可得

$$\text{FDR}(\hat{t}) = E\left(\frac{V(\hat{t})}{R(\hat{t}) \vee 1}\right) = E\left(\frac{V(\hat{t})}{\hat{t} m / \alpha}\right) = \frac{\alpha}{m}E\left(\frac{V(\hat{t})}{\hat{t}}\right) = \frac{\alpha}{m}E\left(\frac{V(1)}{1}\right) = \frac{\alpha m_0}{m} \leq \alpha$$

这一例子再次论证鞅是一个非常强大的数学工具，能够帮助解决很多重要的统计问题。

## 鞅中心极限定理及其应用简介

注：本文是蔡乐衡同学对鞅中心极限定理的整理总结

### 1 鞅的中心极限定理简介

对于一列独立的随机变量序列 $\{X_i\}$，记

$$S_n = \sum_{i=1}^n X_i$$

著名的中心极限定理导出了 $S_n$ 的渐近正态性。

但当随机变量序列 $\{X_i\}$ 并不独立，而是存在着某种相依结构时，如何保证 $S_n$ 的渐近正态性呢？

考虑 $\{D_i\}$ 是一列鞅差，本文所介绍的鞅的中心极限定理为导出 $S_n$ 的极限分布提供了强大的理论工具。

首先回忆鞅差的定义。称 $\{D_i\}$ 是相对于 $\sigma$ 代数流 $\{\mathcal{F}_i\}$ 的鞅差：若 $D_i$ 是 $\mathcal{F}_i$ 适应的（对于任意 $i$，$D_i$ 是 $\mathcal{F}_i$ 可测的），且 $E(D_i|\mathcal{F}_{i-1}) = 0$。

先给出实随机变量版本的鞅的中心极限定理。

**定理1:** 设 $\{D_i\}$ 是相对于 $\sigma$ 代数流 $\{\mathcal{F}_i\}$ 取值于 $\mathbb{R}$ 的鞅差列，满足 $E(D_i^2) < \infty$。设以下两个条件成立：

(1)存在某个 $\sigma^2 > 0$，使得

$$\frac{1}{n}\sum_{i=1}^n E(D_i^2|\mathcal{F}_{i-1}) \stackrel{P}{\to} \sigma^2$$

(2)对于任意 $\varepsilon > 0$，

$$\frac{1}{n}\sum_{i=1}^n E(D_i^2 I(|D_i| > \varepsilon \sqrt{n})|\mathcal{F}_{i-1}) \stackrel{P}{\to} 0$$

则

$$\frac{1}{\sqrt{n}}\sum_{i=1}^n D_i \stackrel{d}{\to} N(0, \sigma^2)$$

其中条件(2)是条件期望版本的Lindeberg条件。

下面给出随机向量版本的鞅的中心极限定理。

**定理2:** 设 $\{D_i\}$ 是相对于 $\sigma$ 代数流 $\{\mathcal{F}_i\}$ 取值于 $\mathbb{R}^p$ 的鞅差列，满足 $E(||D_i||^2) < \infty$。令 $\{e_1, e_2, \ldots, e_p\}$ 为 $\mathbb{R}^p$ 的任意一组基。设以下两个条件成立：

(1)对于任意 $1 \leq j, k \leq p$，存在某个 $\Sigma_{jk}$，使得

$$\frac{1}{n}\sum_{i=1}^n E((D_i^T e_j)(D_i^T e_k)|\mathcal{F}_{i-1}) \stackrel{P}{\to} \Sigma_{jk}$$

(2)对于任意 $\varepsilon > 0$ 以及任意 $1 \leq j \leq p$，

$$\frac{1}{n}\sum_{i=1}^n E((D_i^T e_j)^2 I(||D_i|| > \varepsilon \sqrt{n})|\mathcal{F}_{i-1}) \stackrel{P}{\to} 0$$

则

$$\frac{1}{\sqrt{n}}\sum_{i=1}^n D_i \stackrel{d}{\to} N(0, \Sigma)$$

其中 $\Sigma = (\Sigma_{jk})_{p \times p}$。

最后给出更加一般的希尔伯特空间中随机元版本的鞅的中心极限定理。

**定理3:** 设 $\{D_i\}$ 是相对于 $\sigma$ 代数流 $\{\mathcal{F}_i\}$ 取值于希尔伯特空间 $\mathbb{H}$ 的鞅差列，满足 $E(||D_i||^2) < \infty$。令 $\{e_j\}_{j=1}^{\infty}$ 为 $\mathbb{H}$ 的一组规范正交基。设以下三个条件成立：

(1)对于任意 $j, k \geq 1$，存在某个 $\Sigma_{jk}$，使得

$$\frac{1}{n}\sum_{i=1}^n E(⟨D_i, e_j⟩⟨D_i, e_k⟩|\mathcal{F}_{i-1}) \stackrel{P}{\to} \Sigma_{jk}$$

(2)

$$\sum_{j=1}^{\infty} \Sigma_{jj} < \infty$$

(3)对于任意 $\varepsilon > 0$ 以及任意 $j \geq 1$，

$$\frac{1}{n}\sum_{i=1}^n E(⟨D_i, e_j⟩^2 I(||D_i|| > \varepsilon \sqrt{n})|\mathcal{F}_{i-1}) \stackrel{P}{\to} 0$$

则

$$\frac{1}{\sqrt{n}}\sum_{i=1}^n D_i \stackrel{d}{\to} N(0, \Sigma)$$

其中协方差算子 $\Sigma$ 满足 $⟨\Sigma e_j, e_k⟩ = \Sigma_{jk}$。

### 2 鞅的中心极限定理的应用

下面将利用定理1来介绍一个简单的例子。

在高维统计推断理论研究中，常常使用U统计量作为检验统计量。而鞅的中心极限定理往往可以用于导出U统计量的极限分布。

考虑以下U统计量：

$$S_{nn} = \frac{1}{n(n-1)}\sum_{i \neq j} X_i^T X_j e_i e_j$$

其中 $\{X_i\}$ 是一列i.i.d的零均值p维随机向量（这里维数p可以是发散的），$\{e_i\}$ 是一列i.i.d的零均值随机变量，且与 $\{X_i\}$ 相互独立。

事实上，该U统计量可视为一个零均值鞅。定义 $\mathcal{F}_1 = \sigma(X_1, e_1)$，令 $\mathcal{F}_i = \sigma(X_1, e_1, \ldots, X_i, e_i)$，$i \geq 2$。显然有 $E(X_i^T X_j e_i e_j) = 0$ 成立，进而可得

$$S_{nn} = \frac{1}{n(n-1)}\sum_{i=2}^n \sum_{j=1}^{i-1} (X_i^T X_j e_i e_j + X_j^T X_i e_j e_i)$$

是零均值的鞅列。再令 $D_i = \frac{1}{n-1}\sum_{j=1}^{i-1} (X_i^T X_j e_i e_j + X_j^T X_i e_j e_i)$，$i \geq 2$。基于鞅的中心极限定理，我们只需要验证以下两个条件成立：

$$\frac{1}{n}\sum_{i=2}^n E(D_i^2|\mathcal{F}_{i-1}) \stackrel{P}{\to} \sigma^2$$

以及

$$\frac{1}{n}\sum_{i=2}^n E(D_i^2 I(|D_i| > \varepsilon\sqrt{n})|\mathcal{F}_{i-1}) \stackrel{P}{\to} 0$$

即可导出 $S_{nn}$ 的渐近正态性。

经过简单的计算，不难证明：

$$\frac{1}{n-1}\sum_{i=2}^n X_i^T X_j e_i e_j \stackrel{d}{\to} N\left(0, \frac{2tr(\Sigma^2)\sigma^4}{n^2}\right)$$

其中 $\sigma^2 := E(e_i^2)$。

### 3 参考文献

[1] Kundu, S., Majumdar, S., & Mukherjee, K. (2000). Central limit theorems revisited. Statistics & Probability Letters, 47(3), 265-275.

[2] Hall, P. and Heyde, C. C. (2014), Martingale limit theory and its application, Academic press.

## 基于鞅分解方法的集中不等式

在往常的学习中，我们一般只从独立同分布随机变量和的角度去考虑问题。但是在许多问题中，我们通常要面对形式更复杂的随机变量函数。在这种情形下，函数可能并不满足独立同分布和的形式了。对于这类问题，一种经典的方法是鞅分解。因此，这篇笔记介绍一个基于鞅分解的集中不等式。

### 背景介绍

令 $X = (X_1, \ldots, X_n)$ 为独立同分布的随机变量序列，考虑如下的随机变量 $f(X)$，其中 $f: \mathcal{X}^n \to \mathbb{R}$ 为任意一个可测函数。我们想要控制 $P(|f(X) - E(f(X))| \geq t)$，即对于任意 $t > 0$，对 $P(|f(X) - E(f(X))| \geq t)$ 进行放缩。但 $f(X)$ 的形式可能很复杂，因此无法直接利用基于独立同分布和的结果。我们考虑如下的处理方法：假设所有的条件期望都是存在的，我们令 $V_0 = E(f(X))$，并且

$$V_i = E(f(X)|X_1, \ldots, X_i)$$

其中 $i = 1, 2, \ldots, n$。对于任意的 $i$ 来说，

$$E(V_i|X_1, \ldots, X_{i-1}) = E(E(f(X)|X_1, \ldots, X_i)|X_1, \ldots, X_{i-1}) = E(f(X)|X_1, \ldots, X_{i-1}) = V_{i-1}$$

也就是说，在给定前 $i-1$ 时刻信息的时候，$V_i$ 的条件期望就等于 $V_{i-1}$。如果 $f(X)$ 是绝对可积的，即 $E|f(X)| < \infty$ 的话，那么 $\{V_i\}_{i=0}^n$ 就是一个鞅序列。对应地，令

$$D_i = V_i - V_{i-1}$$

那么 $\{D_i\}_{i=1}^n$ 是一个鞅差序列。因此我们可以进行如下的分解：

$$f(X) - E(f(X)) = V_n - V_0 = \sum_{i=1}^n D_i$$

可以看到，前面相对复杂的 $f(X) - E(f(X))$，被转化为了 $\sum_{i=1}^n D_i$ 的求和。形式上"好看"了很多。

虽然 $D_1, \ldots, D_n$ 之间并不是独立同分布的，但他们满足正交的关系，即对于任意的 $i \neq j$，

$$E(D_i D_j) = 0$$

这就是鞅的分解。相比于 $f(X) - E(f(X))$ 来说，$\sum_{i=1}^n D_i$ 就相对好处理了很多，下面的集中不等式，也是基于 $\sum_{i=1}^n D_i$ 进行推导的。

### Bounded differences inequality

我们首先对 $f(X)$ 做一些假设。给定向量 $x = (x_1, \ldots, x_n)$，对于 $i = 1, \ldots, n$，我们定义一个新的向量 $x^{(i)}$，其中 $x^{(i)}$ 的第 $j$ 个元素 $x^{(i)}_j$ 满足

$$x^{(i)}_j = \begin{cases} x_j, & j \neq i \\ x'_i, & j = i \end{cases}$$

$x_j$ 代表向量 $x$ 的第 $j$ 个元素，$x'_j$ 代表向量 $x'$ 的第 $j$ 个元素。也就是说，$x^{(i)}$ 中只有第 $i$ 个分量从 $x'$ 中取值的，其他分量都是从 $x$ 中取值的。我们对 $f(X)$ 如下定义：

$$|f(x) - f(x^{(i)})| \leq c_i$$

对于函数 $f(X)$，我们称 $f(X)$ 满足参数为 $(c_1, \ldots, c_n)$ 的bounded difference条件，如果对于任意的 $i \in \{1, \ldots, n\}$ 以及任意 $x, x' \in \mathcal{X}^n$，

$$|f(x) - f(x^{(i)})| \leq c_i$$

这个定义表明，在任何一个维度上，$f(X)$ 取值的变化范围始终是有限的。接下来我们基于bounded difference的假设，计算 $P(|f(X) - E(f(X))| \geq t)$ 的尾概率。根据鞅分解的方法，前面所讲的尾概率可以写成

$$P\left(\left|\sum_{i=1}^n D_i\right| \geq t\right)$$

的形式。

第一步，我们证明 $D_i$ 的取值范围在一个长度至多为 $c_i$ 的区间内。考虑如下的随机变量

$$D_i^+ = E(f(X)|X_1, \ldots, X_i) - E(f(X)|X_1, \ldots, X_{i-1}, X'_i)$$

以及

$$D_i^- = E(f(X)|X_1, \ldots, X_{i-1}, X'_i) - E(f(X)|X_1, \ldots, X_{i-1})$$

根据上确界以及下确界的定义，我们可以得到 $D_i^- \leq D_i \leq D_i^+$。也就是说 $D_i$ 在区间 $[D_i^-, D_i^+]$ 内活动。如果我们能证明这个区间长度比 $c_i$ 小，即 $D_i^+ - D_i^- \leq c_i$，就能够证出 $D_i$ 的取值范围在一个长度至多为 $c_i$ 的区间内。

因为 $X_i$ 之间是独立的，我们可得对于任意向量 $x$，

$$E(f(X)|X_1 = x_1, \ldots, X_{i-1} = x_{i-1}, X_i = x_i) - E(f(X)|X_1 = x_1, \ldots, X_{i-1} = x_{i-1}, X_i = x'_i) = E(f(x) - f(x^{(i)}))$$

其中 $E_X$ 代表对 $X$ 求期望。因此我们可得

$$D_i^+ - D_i^- \leq \sup_{x, x' \in \mathcal{X}^n} |f(x) - f(x^{(i)})| \leq c_i$$

其中第二个不等号用到了bounded difference条件。因此 $D_i$ 的取值范围在一个长度至多为 $c_i$ 的区间内。

根据往常的证法，我们接下来再对 $D_i$ 的矩母函数做出限制，并利用Chernoff方法得到集中不等式。

第二步，计算 $D_i$ 的矩母函数。因为 $D_i$ 在长度为 $c_i$ 的区间内取值，所以 $D_i$ 服从参数为 $c_i$ 的次高斯分布。因此 $D_i$ 的条件矩母函数满足

$$E(e^{\lambda D_i}|X_1, \ldots, X_{i-1}) \leq e^{\lambda^2 c_i^2/8}$$

其中 $\lambda \in \mathbb{R}$ 以及 $i = 1, \ldots, n$。

第三步，利用Chernoff方法构造概率不等式。因为

$$P\left(\sum_{i=1}^n D_i \geq t\right) = P\left(e^{\lambda \sum_{i=1}^n D_i} \geq e^{\lambda t}\right) \leq \frac{E\left(e^{\lambda \sum_{i=1}^n D_i}\right)}{e^{\lambda t}}$$

接下来只需要控制 $E\left(e^{\lambda \sum_{i=1}^n D_i}\right)$。但因为 $D_1, \ldots, D_n$ 之间并不是独立同分布的，所以这里不能像独立同分布和那样处理。我们利用迭代的技巧进行放缩。因为

$$E\left(e^{\lambda \sum_{i=1}^n D_i}\right) = E\left(e^{\lambda \sum_{i=1}^{n-1} D_i} \cdot E\left(e^{\lambda D_n}|X_1, \ldots, X_{n-1}\right)\right) \leq E\left(e^{\lambda \sum_{i=1}^{n-1} D_i}\right) \cdot e^{\lambda^2 c_n^2/8}$$

其中不等号利用了第二步的结果。类似地一步步迭代下去，我们可以得到

$$E\left(e^{\lambda \sum_{i=1}^n D_i}\right) \leq \prod_{i=1}^n e^{\lambda^2 c_i^2/8} = e^{\lambda^2 \sum_{i=1}^n c_i^2/8}$$

因此可得

$$P\left(\sum_{i=1}^n D_i \geq t\right) \leq \frac{e^{\lambda^2 \sum_{i=1}^n c_i^2/8}}{e^{\lambda t}}$$

令 $\lambda = \frac{4t}{\sum_{i=1}^n c_i^2}$，可得

$$P\left(\sum_{i=1}^n D_i \geq t\right) \leq e^{-2t^2/\sum_{i=1}^n c_i^2}$$

同理可得

$$P\left(\sum_{i=1}^n D_i \leq -t\right) \leq e^{-2t^2/\sum_{i=1}^n c_i^2}$$

因此我们可以得到如下的不等式：

$$P\left(\left|\sum_{i=1}^n D_i\right| \geq t\right) \leq 2e^{-2t^2/\sum_{i=1}^n c_i^2}$$

又因为 $\sum_{i=1}^n D_i = f(X) - E(f(X))$，对于任意的 $t > 0$，可得

$$P(|f(X) - E(f(X))| \geq t) \leq 2e^{-2t^2/\sum_{i=1}^n c_i^2}$$

这就是Bounded differences inequality。

### 一些应用：U统计量的集中不等式

令 $h(x, y)$ 为一个对称的二元函数，$X_1, \ldots, X_n$ 为独立同分布的随机变量序列。考虑如下的 $U$ 统计量：

$$U_n = \frac{1}{n(n-1)} \sum_{i \neq j} h(X_i, X_j)$$

因为 $h(X_i, X_j)$ 之间存在相关性，所以 $U$ 统计量并不是独立同分布和的形式。接下来我们利用Bounded difference inequality计算U统计量的集中不等式。

我们假设 $h$ 是有界函数($|h| \leq B$)。将 $U_n$ 看作函数 $f(X_1, \ldots, X_n)$，对于任意的 $i \in \{1, \ldots, n\}$，可得

$$|f(x) - f(x^{(i)})| = \left|\frac{1}{n(n-1)}\sum_{j \neq k} h(x_j, x_k) - \frac{1}{n(n-1)}\sum_{j \neq k} h(x^{(i)}_j, x^{(i)}_k)\right| \leq \frac{1}{n(n-1)}\sum_{j \neq i} (|h(x_j, x_i)| + |h(x_j, x'_i)|) \leq \frac{2(n-1)B}{n(n-1)} = \frac{2B}{n}$$

其中第一个不等号用到了绝对值的三角不等式，第二个不等号用到了函数 $h$ 有界的性质。因此函数 $f(X_1, \ldots, X_n)$ 满足参数 $c_1 = \ldots = c_n = \frac{2B}{n}$ 的bounded difference性质。将 $c_i = \frac{2B}{n}$ 带入到bounded difference inequality中，可得

$$P(|U_n - E(U_n)| \geq t) \leq 2e^{-\frac{n^2t^2}{2 \cdot n \cdot (2B/n)^2}} = 2e^{-\frac{n^2t^2}{8B^2}}$$

这样就得到了一个 $U$ 统计量的集中不等式。