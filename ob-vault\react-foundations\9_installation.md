Title: React Foundations: Installing Next.js | Next.js

URL Source: https://nextjs.org/learn/react-foundations/installation

When you use Next.js in your project, you do not need to load the `react` and `react-dom` scripts from [unpkg.com](http://unpkg.com/) anymore. Instead, you can install these packages locally using `npm` or your preferred package manager.

> **Note**: To use Next.js, you will need to have Node.js version **18.17.0** or above installed on your machine ([see minimum version requirement](https://nextjs.org/docs/getting-started/installation)), you can [download it here](https://nodejs.org/en/).

To do so, create a new file in the same directory as your `index.html` file, called `package.json` with an empty object `{}`.

package.json

`{}`

In your [terminal](https://code.visualstudio.com/docs/terminal/basics), run the following command in the root of your project:

Terminal

`npm install react@latest react-dom@latest next@latest`

Once the installation is complete, you should be able to see your project dependencies listed inside your `package.json` file:

package.json

```
{
  "dependencies": {
    "next": "^14.0.3",
    "react": "^18.3.1",
    "react-dom": "^18.3.1"
  }
}
```

Don't worry if you're on later versions than the ones shown above, as long as you have the `next`, `react`, and `react-dom` packages installed, you're good to go.

You will also notice a new file called `package-lock.json` file that contains detailed information about the exact versions of each package.

Jumping back to the `index.html` file, you can delete the following code:

1.   The `<html>` and `<body>` tags.
2.   The `<div>` element with the `id` of `app`.
3.   The `react` and `react-dom` scripts since you've installed them with NPM.
4.   The `Babel` script because Next.js has a compiler that transforms JSX into valid JavaScript browsers can understand.
5.   The `<script type="text/jsx">` tag.
6.   The `document.getElementById()` and `ReactDom.createRoot()` methods.
7.   The `React.` part of the `React.useState(0)` function

After deleting the lines above, add the following import to the top of your file:

index.html

`import { useState } from 'react';`

Your code should look like this:

index.html

```
import { useState } from 'react';
 
function Header({ title }) {
  return <h1>{title ? title : 'Default title'}</h1>;
}
 
function HomePage() {
  const names = ['Ada Lovelace', 'Grace Hopper', 'Margaret Hamilton'];
 
  const [likes, setLikes] = useState(0);
 
  function handleClick() {
    setLikes(likes + 1);
  }
 
  return (
    <div>
      <Header title="Develop. Preview. Ship." />
      <ul>
        {names.map((name) => (
          <li key={name}>{name}</li>
        ))}
      </ul>
 
      <button onClick={handleClick}>Like ({likes})</button>
    </div>
  );
}
```

The only code left in the HTML file is JSX, so you can change the file type from `.html` to `.js` or `.jsx`.

[Creating your first page](https://nextjs.org/learn/react-foundations/installation#creating-your-first-page)
------------------------------------------------------------------------------------------------------------

Next.js uses file-system routing. This means that instead of using code to define the routes of your application, you can use folders and files.

Here's how you can create your first page in Next.js:

1.   Create a new folder called [app](https://nextjs.org/docs/app/building-your-application/routing#the-app-router) and move the `index.js` file inside it.
2.   Rename your `index.js` file to `page.js`. This will be the main page of your application.
3.   Add `export default` to your `<HomePage>` component to help Next.js distinguish which component to render as the main component of the page.

app/page.js

```
import { useState } from 'react';
 
function Header({ title }) {
  return <h1>{title ? title : 'Default title'}</h1>;
}
 
export default function HomePage() {
  // ...
}
```

[Running the development server](https://nextjs.org/learn/react-foundations/installation#running-the-development-server)
------------------------------------------------------------------------------------------------------------------------

Next, let's run your development server so you can see the changes in your new page while developing. Add a `"next dev"` script to your `package.json` file:

package.json

```
{
  "scripts": {
    "dev": "next dev"
  },
  "dependencies": {
    "next": "^14.0.3",
    "react": "^18.3.1",
    "react-dom": "^18.3.1"
  }
}
```

Check what happens by running `npm run dev` in your terminal. You'll notice two things:

1.   When you navigate to [localhost:3000](http://localhost:3000/), you should see the following error:

![Image 3: Next.js Error Message: You're importing a component that needs useState. It only works in a Client component...](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Flearn-usestate-rsc-error.png&w=3840&q=75)![Image 4: Next.js Error Message: You're importing a component that needs useState. It only works in a Client component...](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Flearn-usestate-rsc-error.png&w=3840&q=75)
This is because Next.js uses React Server Components, a new feature that allows React to render on the server. Server Components don't support `useState`, so you'll need to use a Client Component instead.

In the next chapter, we'll discuss the main differences between Server and Client Components and fix this error.

1.   A new file called `layout.js` was automatically created inside the `app` folder. This is the main layout of your application. You can use it to add UI elements that are shared across all pages (e.g. navigation, footer, etc).

/app/layout.js

```
export const metadata = {
  title: 'Next.js',
  description: 'Generated by Next.js',
};
 
export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  );
}
```

[Summary](https://nextjs.org/learn/react-foundations/installation#summary)
--------------------------------------------------------------------------

Looking at the migration so far, you may already be getting a sense of the benefits of using Next.js:

*   You removed the React and Babel scripts; a taste of the complex tooling and configuration you no longer have to think about.
*   You created your first page.

> **Additional Reading:**
> 
> 
> *   [Next.js Routing Fundamentals](https://nextjs.org/docs/app/building-your-application/routing)
> *   [Defining Routes](https://nextjs.org/docs/app/building-your-application/routing/defining-routes)
> *   [Pages and Layouts](https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts)