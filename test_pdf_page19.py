#!/usr/bin/env python3
"""
删除GoodNotes PDF批注并生成清理后的页面图片
专门处理26王道数据结构.pdf的第19页
"""

import fitz  # PyMuPDF
import os
from pathlib import Path


def remove_annotations_and_save_image():
    """删除第19页的所有批注并保存为图片"""

    # PDF文件路径
    pdf_path = "goodnotes/GoodNotes/考研材料/26王道数据结构.pdf"

    # 检查文件是否存在
    if not os.path.exists(pdf_path):
        print(f"❌ 文件不存在: {pdf_path}")
        # 尝试列出可能的路径
        print("🔍 尝试查找文件...")
        if os.path.exists("goodnotes"):
            print("✅ goodnotes 目录存在")
            if os.path.exists("goodnotes/GoodNotes"):
                print("✅ GoodNotes 目录存在")
                if os.path.exists("goodnotes/GoodNotes/考研材料"):
                    print("✅ 考研材料 目录存在")
                    files = os.listdir("goodnotes/GoodNotes/考研材料")
                    print(f"📁 目录内容: {files}")
        return False
    
    try:
        # 打开PDF文档
        doc = fitz.open(pdf_path)
        print(f"✅ 成功打开PDF文件: {pdf_path}")
        print(f"📄 总页数: {len(doc)}")

        # 检查第19页是否存在（页面索引从0开始，所以第19页是索引18）
        page_index = 18  # 第19页
        if page_index >= len(doc):
            print(f"❌ 第19页不存在，文档只有 {len(doc)} 页")
            doc.close()
            return False

        # 获取第19页
        page = doc[page_index]
        print(f"\n🔍 处理第19页...")

        # 1. 先保存原始页面图片（带批注）
        print("📸 保存原始页面图片（带批注）...")
        mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放，提高图片质量
        pix_original = page.get_pixmap(matrix=mat)
        pix_original.save("page19_with_annotations.png")
        print("✅ 原始页面已保存为: page19_with_annotations.png")

        # 2. 分析页面中的批注
        annotations = list(page.annots())
        print(f"📝 发现 {len(annotations)} 个批注")

        if len(annotations) > 0:
            # 显示批注类型统计
            annotation_types = {}
            for annot in annotations:
                annot_type = annot.type[1]
                annotation_types[annot_type] = annotation_types.get(annot_type, 0) + 1

            print("📊 批注类型统计:")
            for annot_type, count in annotation_types.items():
                print(f"  {annot_type}: {count} 个")

            # 3. 删除所有批注
            print("\n🗑️  删除所有批注...")
            removed_count = 0

            # 使用安全的方式删除批注
            annot = page.first_annot
            while annot:
                annot_type_name = annot.type[1]
                print(f"  删除批注: {annot_type_name}")
                annot = page.delete_annot(annot)
                removed_count += 1

            print(f"✅ 成功删除 {removed_count} 个批注")
        else:
            print("ℹ️  第19页没有批注")

        # 4. 保存清理后的页面图片（无批注）
        print("\n📸 保存清理后的页面图片（无批注）...")
        pix_clean = page.get_pixmap(matrix=mat)
        pix_clean.save("page19_no_annotations.png")
        print("✅ 清理后页面已保存为: page19_no_annotations.png")

        # 5. 保存清理后的PDF文件
        input_file = Path(pdf_path)
        output_pdf_path = input_file.parent / f"{input_file.stem}_page19_cleaned{input_file.suffix}"
        doc.save(str(output_pdf_path))
        print(f"💾 清理后的PDF已保存为: {output_pdf_path}")

        # 6. 验证批注是否已被完全移除
        remaining_annotations = list(page.annots())
        print(f"\n� 验证结果: 剩余批注数量 = {len(remaining_annotations)}")

        if len(remaining_annotations) == 0:
            print("🎉 所有批注已成功删除！")
        else:
            print(f"⚠️  仍有 {len(remaining_annotations)} 个批注未删除")

        doc.close()
        return True

    except Exception as e:
        print(f"❌ 处理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False



def main():
    """主函数"""
    print("🔧 GoodNotes PDF批注删除工具")
    print("📖 目标文件: 26王道数据结构.pdf")
    print("📄 目标页面: 第19页")
    print("🎯 功能: 删除批注并生成清理后的页面图片")
    print("=" * 50)

    print("\n🚀 开始处理...")
    success = remove_annotations_and_save_image()

    if success:
        print("\n✅ 处理完成！")
        print("📁 生成的文件:")
        print("  - page19_with_annotations.png (原始页面，带批注)")
        print("  - page19_no_annotations.png (清理后页面，无批注)")
        print("  - 26王道数据结构_page19_cleaned.pdf (清理后的PDF)")
    else:
        print("\n❌ 处理失败！")

    print("\n🎉 程序结束!")


if __name__ == "__main__":
    main()
