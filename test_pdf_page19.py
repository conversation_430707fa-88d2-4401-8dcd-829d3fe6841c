#!/usr/bin/env python3
"""
测试脚本：处理26王道数据结构.pdf的第19页
去除GoodNotes批注，获得原始内容
"""

import fitz  # PyMuPDF
import os
from pathlib import Path


def test_page19():
    """测试处理第19页的批注"""

    # PDF文件路径 - 使用正斜杠，Python会自动处理
    pdf_path = "goodnotes/GoodNotes/考研材料/26王道数据结构.pdf"

    # 检查文件是否存在
    if not os.path.exists(pdf_path):
        print(f"❌ 文件不存在: {pdf_path}")
        # 尝试列出可能的路径
        print("🔍 尝试查找文件...")
        if os.path.exists("goodnotes"):
            print("✅ goodnotes 目录存在")
            if os.path.exists("goodnotes/GoodNotes"):
                print("✅ GoodNotes 目录存在")
                if os.path.exists("goodnotes/GoodNotes/考研材料"):
                    print("✅ 考研材料 目录存在")
                    files = os.listdir("goodnotes/GoodNotes/考研材料")
                    print(f"📁 目录内容: {files}")
        return False
    
    try:
        # 打开PDF文档
        doc = fitz.open(pdf_path)
        print(f"✅ 成功打开PDF文件: {pdf_path}")
        print(f"📄 总页数: {len(doc)}")
        
        # 检查第19页是否存在（页面索引从0开始，所以第19页是索引18）
        page_index = 18  # 第19页
        if page_index >= len(doc):
            print(f"❌ 第19页不存在，文档只有 {len(doc)} 页")
            doc.close()
            return False
        
        # 获取第19页
        page = doc[page_index]
        print(f"\n🔍 分析第19页...")
        
        # 分析页面中的批注
        annotations = list(page.annots())
        print(f"📝 发现 {len(annotations)} 个批注")
        
        if len(annotations) == 0:
            print("ℹ️  第19页没有批注，可能是原始PDF或批注已被移除")
            doc.close()
            return True
        
        # 详细分析每个批注
        print("\n📋 批注详情:")
        annotation_types = {}
        
        for i, annot in enumerate(annotations, 1):
            annot_type = annot.type[1]  # 批注类型名称
            annot_content = annot.content  # 批注内容
            annot_rect = annot.rect  # 批注位置
            
            print(f"  {i}. 类型: {annot_type}")
            if annot_content:
                content_preview = annot_content[:100] + "..." if len(annot_content) > 100 else annot_content
                print(f"     内容: {content_preview}")
            print(f"     位置: ({annot_rect.x0:.1f}, {annot_rect.y0:.1f}) - ({annot_rect.x1:.1f}, {annot_rect.y1:.1f})")
            
            # 统计批注类型
            annotation_types[annot_type] = annotation_types.get(annot_type, 0) + 1
        
        print(f"\n📊 批注类型统计:")
        for annot_type, count in annotation_types.items():
            print(f"  {annot_type}: {count} 个")
        
        # 询问是否要移除批注
        print(f"\n❓ 是否要移除第19页的所有批注？(y/n): ", end="")
        response = input().lower().strip()
        
        if response in ['y', 'yes', '是', 'Y']:
            # 移除批注
            print("\n🗑️  开始移除批注...")
            removed_count = 0
            
            # 使用安全的方式删除批注
            annot = page.first_annot
            while annot:
                annot_type_name = annot.type[1]
                print(f"  移除批注: {annot_type_name}")
                annot = page.delete_annot(annot)
                removed_count += 1
            
            print(f"✅ 成功移除 {removed_count} 个批注")
            
            # 生成输出文件名
            input_file = Path(pdf_path)
            output_path = input_file.parent / f"{input_file.stem}_page19_no_annotations{input_file.suffix}"
            
            # 保存处理后的PDF
            doc.save(str(output_path))
            print(f"💾 已保存到: {output_path}")
            
            # 验证批注是否已被移除
            print("\n🔍 验证批注移除结果...")
            page_after = doc[page_index]
            remaining_annotations = list(page_after.annots())
            print(f"📝 剩余批注数量: {len(remaining_annotations)}")
            
            if len(remaining_annotations) == 0:
                print("✅ 所有批注已成功移除！")
            else:
                print(f"⚠️  仍有 {len(remaining_annotations)} 个批注未移除")
        
        else:
            print("ℹ️  取消移除批注操作")
        
        doc.close()
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {str(e)}")
        return False


def extract_page19_text():
    """提取第19页的文本内容（用于对比）"""

    pdf_path = "goodnotes/GoodNotes/考研材料/26王道数据结构.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"❌ 文件不存在: {pdf_path}")
        return
    
    try:
        doc = fitz.open(pdf_path)
        page_index = 18  # 第19页
        
        if page_index >= len(doc):
            print(f"❌ 第19页不存在")
            doc.close()
            return
        
        page = doc[page_index]
        
        # 提取文本
        text = page.get_text()
        print(f"📄 第19页文本内容:")
        print("=" * 50)
        print(text)
        print("=" * 50)
        
        # 保存文本到文件
        text_file = "page19_text.txt"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(f"26王道数据结构.pdf - 第19页文本内容\n")
            f.write("=" * 50 + "\n")
            f.write(text)
        
        print(f"💾 文本已保存到: {text_file}")
        
        doc.close()
        
    except Exception as e:
        print(f"❌ 提取文本时发生错误: {str(e)}")


def main():
    """主函数"""
    print("🔧 PDF批注移除测试工具")
    print("📖 目标文件: 26王道数据结构.pdf")
    print("📄 目标页面: 第19页")
    print("=" * 50)
    
    print("\n1️⃣  测试第19页批注处理")
    success = test_page19()
    
    if success:
        print("\n2️⃣  提取第19页文本内容")
        extract_page19_text()
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    main()
