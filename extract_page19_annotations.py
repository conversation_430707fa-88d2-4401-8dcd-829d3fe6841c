import fitz  # PyMuPDF
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from PIL import Image
import io

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def extract_page19_annotations():
    """
    从26王道数据结构.pdf中提取第19页的注释图像
    类似于 fitz.open(pdf_path)[page_num].get_pixmap(annots=True/False) 的实现
    """
    # PDF文件路径
    pdf_path = r"goodnotes\GoodNotes\考研材料\26王道数据结构.pdf"
    page_num = 18  # 第19页（索引从0开始）
    
    # 打开PDF文件
    doc = fitz.open(pdf_path)
    
    print(f"PDF文件: {pdf_path}")
    print(f"总页数: {len(doc)}")
    print(f"提取页面: 第{page_num + 1}页")
    
    # 获取第19页
    page = doc[page_num]
    
    # 提取带注释的图像 (annots=True)
    pixmap_with_annots = page.get_pixmap(annots=True)
    img_data_with = pixmap_with_annots.tobytes("ppm")
    img_with_annots = Image.open(io.BytesIO(img_data_with))
    
    # 提取不带注释的图像 (annots=False)
    pixmap_without_annots = page.get_pixmap(annots=False)
    img_data_without = pixmap_without_annots.tobytes("ppm")
    img_without_annots = Image.open(io.BytesIO(img_data_without))
    
    # 关闭文档
    doc.close()
    
    # 使用matplotlib显示图像
    fig, axes = plt.subplots(1, 2, figsize=(20, 12))
    
    # 显示带注释的图像
    axes[0].imshow(img_with_annots)
    axes[0].set_title('Page 19 - With Annotations (annots=True)', fontsize=16, fontweight='bold')
    axes[0].axis('off')
    
    # 显示不带注释的图像
    axes[1].imshow(img_without_annots)
    axes[1].set_title('Page 19 - Without Annotations (annots=False)', fontsize=16, fontweight='bold')
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.suptitle('26王道数据结构.pdf - 第19页注释对比', fontsize=18, y=0.98)
    plt.show()
    
    print(f"图像尺寸: {img_with_annots.size}")
    print("图像已成功显示！")
    
    return img_with_annots, img_without_annots

def save_images_if_needed(img_with_annots, img_without_annots):
    """可选：保存图像到文件"""
    save = input("是否保存图像到文件？(y/n): ").lower().strip()
    if save == 'y':
        img_with_annots.save("page19_with_annotations.png", dpi=(300, 300))
        img_without_annots.save("page19_without_annotations.png", dpi=(300, 300))
        print("图像已保存为高分辨率PNG文件")

if __name__ == "__main__":
    try:
        # 提取并显示第19页注释图像
        img_with, img_without = extract_page19_annotations()
        
        # 可选保存
        save_images_if_needed(img_with, img_without)
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
