import asyncio
import json
import websockets
import requests

async def get_share_session_cookie():
    # 1. 获取调试目标
    targets = requests.get("http://localhost:9229/json/list").json()
    ws_url = targets[0]["webSocketDebuggerUrl"]
    
    # 2. 连接并执行
    async with websockets.connect(ws_url) as ws:
        command = {
            "id": 1,
            "method": "Runtime.evaluate",
            "params": {
                "expression": """
                (async () => {
                    const { session } = process.mainModule.require('electron');
                    const cookies = await session.defaultSession.cookies.get({ name: 'share-session' });
                    return cookies[0];
                })()
                """,
                "awaitPromise": True,
                "returnByValue": True
            }
        }
        
        await ws.send(json.dumps(command))
        result = json.loads(await ws.recv())
        
        # 3. 提取cookie值
        cookie = result["result"]["result"]["value"]
        return cookie["value"]

# 使用
print(asyncio.run(get_share_session_cookie()))
