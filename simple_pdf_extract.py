import fitz  # PyMuPDF
import matplotlib.pyplot as plt
from PIL import Image
import io

# PDF文件路径和页面设置
pdf_path = r"goodnotes\GoodNotes\考研材料\26王道数据结构.pdf"
page_num = 18  # 第19页（索引从0开始）

# 打开PDF并提取页面
doc = fitz.open(pdf_path)
page = doc[page_num]

# 方法1: 带注释的图像 (类似 fitz.open(pdf)[page].get_pixmap(annots=True))
pixmap_with_annots = page.get_pixmap(annots=True)
img_data_with = pixmap_with_annots.tobytes("ppm")
img_with_annots = Image.open(io.BytesIO(img_data_with))

# 方法2: 不带注释的图像 (类似 fitz.open(pdf)[page].get_pixmap(annots=False))
pixmap_without_annots = page.get_pixmap(annots=False)
img_data_without = pixmap_without_annots.tobytes("ppm")
img_without_annots = Image.open(io.BytesIO(img_data_without))

doc.close()

# 使用matplotlib显示
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 10))

ax1.imshow(img_with_annots)
ax1.set_title('With Annotations (annots=True)')
ax1.axis('off')

ax2.imshow(img_without_annots)
ax2.set_title('Without Annotations (annots=False)')
ax2.axis('off')

plt.tight_layout()
plt.show()

print(f"Successfully extracted page {page_num + 1}")
print(f"Image size: {img_with_annots.size}")
