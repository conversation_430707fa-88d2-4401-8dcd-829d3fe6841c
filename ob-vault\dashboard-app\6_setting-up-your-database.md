App Router: Setting Up Your Database | Next.js

===============
[Skip to content](https://nextjs.org/learn/dashboard-app/setting-up-your-database#geist-skip-nav)

[](https://vercel.com/home?utm_source=next-site&utm_medium=banner&utm_campaign=learn_dashboard-app_setting-up-your-database "Go to Vercel homepage")

[![Image 1: Next.js uwu logo by SAWARATSUKI](https://nextjs.org/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1714730590%2Ffront%2Fnextjs%2Fuwu%2Fnext-uwu-logo.png&w=128&q=75)](https://nextjs.org/?uwu=true "Go to the homepage")

[](https://nextjs.org/ "Go to the homepage")

Search documentation...CtrlK Search...⌘K

[](https://vercel.com/home?utm_source=next-site&utm_medium=banner&utm_campaign=learn_dashboard-app_setting-up-your-database "Go to Vercel homepage")

[![Image 2: Next.js uwu logo by SAWARATSUKI](https://nextjs.org/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1714730590%2Ffront%2Fnextjs%2Fuwu%2Fnext-uwu-logo.png&w=128&q=75)](https://nextjs.org/?uwu=true "Go to the homepage")

[](https://nextjs.org/ "Go to the homepage")

[Showcase](https://nextjs.org/showcase)[Docs](https://nextjs.org/docs "Documentation")[Blog](https://nextjs.org/blog)[Templates](https://vercel.com/templates/next.js?utm_source=next-site&utm_medium=navbar&utm_campaign=next_site_nav_templates)[Enterprise](https://vercel.com/contact/sales/nextjs?utm_source=next-site&utm_medium=navbar&utm_campaign=next_site_nav_enterprise)

Search documentation...CtrlK Search...⌘K[Deploy](https://vercel.com/new/clone?utm_source=next-site&utm_medium=banner&b=main&s=https%3A%2F%2Fgithub.com%2Fvercel%2Fvercel%2Ftree%2Fmain%2Fexamples%2Fnextjs&showOptionalTeamCreation=false&template=nextjs&teamCreateStatus=hidden&utm_campaign=learn_dashboard-app_setting-up-your-database)[Learn](https://nextjs.org/learn)

Chapter 6

Setting Up Your Database

[Sign in](https://nextjs.org/api/auth/authorize?slug=dashboard-app/setting-up-your-database)

[Sign in to save progress](https://nextjs.org/api/auth/authorize?slug=dashboard-app/setting-up-your-database)

6

Chapter 6

Setting Up Your Database
========================

Before you can continue working on your dashboard, you'll need some data. In this chapter, you'll be setting up a PostgreSQL database from one of [Vercel's marketplace integrations](https://vercel.com/marketplace?category=databases). If you're already familiar with PostgreSQL and would prefer to use your own database provider, you can skip this chapter and set it up on your own. Otherwise, let's continue!

In this chapter...

Here are the topics we'll cover

![Image 3](https://nextjs.org/_next/static/media/logo-github-light.4061ea5b.svg)![Image 4](https://nextjs.org/_next/static/media/logo-github-dark.314c1a0e.svg)

Push your project to GitHub.

![Image 5](https://nextjs.org/_next/static/media/logo-vercel-light.844bc059.svg)![Image 6](https://nextjs.org/_next/static/media/logo-vercel-dark.844bc059.svg)

Set up a Vercel account and link your GitHub repo for instant previews and deployments.

![Image 7](https://nextjs.org/_next/static/media/postgres.531582c2.svg)

Create and link your project to a Postgres database.

![Image 8](https://nextjs.org/_next/static/media/database.4700ee8c.svg)

Seed the database with initial data.

[Create a GitHub repository](https://nextjs.org/learn/dashboard-app/setting-up-your-database#create-a-github-repository)
------------------------------------------------------------------------------------------------------------------------

To start, let's push your repository to GitHub if you haven't already. This will make it easier to set up your database and deploy.

If you need help setting up your repository, take a look at [this guide on GitHub](https://help.github.com/en/github/getting-started-with-github/create-a-repo).

> **Good to know:**
> 
> 
> *   You can also use other git providers like GitLab or Bitbucket.
> *   If you're new to GitHub, we recommend the [GitHub Desktop App](https://desktop.github.com/) for a simplified development workflow.

[Create a Vercel account](https://nextjs.org/learn/dashboard-app/setting-up-your-database#create-a-vercel-account)
------------------------------------------------------------------------------------------------------------------

Visit [vercel.com/signup](https://vercel.com/signup) to create an account. Choose the free "hobby" plan. Select **Continue with GitHub** to connect your GitHub and Vercel accounts.

[Connect and deploy your project](https://nextjs.org/learn/dashboard-app/setting-up-your-database#connect-and-deploy-your-project)
----------------------------------------------------------------------------------------------------------------------------------

Next, you'll be taken to this screen where you can select and **import** the GitHub repository you've just created:

![Image 9: Screenshot of Vercel Dashboard, showing the import project screen with a list of the user's GitHub Repositories](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Fimport-git-repo.png&w=1920&q=75)![Image 10: Screenshot of Vercel Dashboard, showing the import project screen with a list of the user's GitHub Repositories](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Fimport-git-repo.png&w=1920&q=75)
Name your project and click **Deploy**.

![Image 11: Deployment screen showing the project name field and a deploy button](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Fconfigure-project.png&w=1920&q=75)![Image 12: Deployment screen showing the project name field and a deploy button](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Fconfigure-project.png&w=1920&q=75)
Hooray! 🎉 Your project is now deployed.

![Image 13: Project overview screen showing the project name, domain, and deployment status](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Fdeployed-project.png&w=1920&q=75)![Image 14: Project overview screen showing the project name, domain, and deployment status](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Fdeployed-project.png&w=1920&q=75)
By connecting your GitHub repository, whenever you push changes to your **main** branch, Vercel will automatically redeploy your application with no configuration needed. When opening pull requests, you'll also have [instant preview URLs](https://vercel.com/docs/deployments/environments#preview-environment-pre-production#preview-urls) which allow you to catch deployment errors early and share a preview of your project with team members for feedback.

[Create a Postgres database](https://nextjs.org/learn/dashboard-app/setting-up-your-database#create-a-postgres-database)
------------------------------------------------------------------------------------------------------------------------

Next, to set up a database, click **Continue to Dashboard** and select the **Storage** tab from your project dashboard. Select **Create Database**. Depending on when your Vercel account was created, you may see options like Neon or Supabase. Choose your preferred provider and click **Continue**.

![Image 15: Connect Store screen showing the Postgres option along with KV, Blob and Edge Config](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Fcreate-database.png&w=1920&q=75)![Image 16: Connect Store screen showing the Postgres option along with KV, Blob and Edge Config](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Fcreate-database.png&w=1920&q=75)
Choose your region and storage plan, if required. The [default region](https://vercel.com/docs/functions/configuring-functions/region) for all Vercel projects is **Washington D.C (iad1)**, and we recommend choosing this if available to reduce [latency](https://developer.mozilla.org/en-US/docs/Web/Performance/Understanding_latency) for data requests.

![Image 17: Database creation modal showing the database name and region](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Fdatabase-region.png&w=1920&q=75)![Image 18: Database creation modal showing the database name and region](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Fdatabase-region.png&w=1920&q=75)
Once connected, navigate to the `.env.local` tab, click **Show secret** and **Copy Snippet**. Make sure you reveal the secrets before copying them.

![Image 19: The .env.local tab showing the hidden database secrets](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Fdatabase-dashboard.png&w=1920&q=75)![Image 20: The .env.local tab showing the hidden database secrets](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Fdatabase-dashboard.png&w=1920&q=75)
Navigate to your code editor and rename the `.env.example` file to **`.env`**. Paste in the copied contents from Vercel.

> **Important:** Go to your `.gitignore` file and make sure `.env` is in the ignored files to prevent your database secrets from being exposed when you push to GitHub.

[Seed your database](https://nextjs.org/learn/dashboard-app/setting-up-your-database#seed-your-database)
--------------------------------------------------------------------------------------------------------

Now that your database has been created, let's seed it with some initial data.

We've included an API you can access in the browser, which will run a seed script to populate the database with an initial set of data.

The script uses **SQL** to create the tables, and the data from `placeholder-data.ts` file to populate them after they've been created.

Ensure your local development server is running with `pnpm run dev` and navigate to [`localhost:3000/seed`](http://localhost:3000/seed) in your browser. When finished, you will see a message "Database seeded successfully" in the browser. Once completed, you can delete this file.

### It's time to take a quiz!

Test your knowledge and see what you've just learned.

What is 'seeding' in the context of databases?

A

Deleting all data in the database

B

Importing the schema of a database

C

Populating the database with an initial set of data

D

Creating relationships between tables in a database

Check Answer

> **Troubleshooting**:
> 
> 
> *   Make sure to reveal your database secrets before copying it into your `.env` file.
> *   The script uses `bcrypt` to hash the user's password, if `bcrypt` isn't compatible with your environment, you can update the script to use [`bcryptjs`](https://www.npmjs.com/package/bcryptjs) instead.
> *   If you run into any issues while seeding your database and want to run the script again, you can drop any existing tables by running `DROP TABLE tablename` in your database query interface. See the [executing queries section](https://nextjs.org/learn/dashboard-app/setting-up-your-database#executing-queries) below for more details. But be careful, this command will delete the tables and all their data. It's ok to do this with your example app since you're working with placeholder data, but you shouldn't run this command in a production app.

[Executing queries](https://nextjs.org/learn/dashboard-app/setting-up-your-database#executing-queries)
------------------------------------------------------------------------------------------------------

Let's execute a query to make sure everything is working as expected. We'll use another Router Handler, `app/query/route.ts`, to query the database. Inside this file, you'll find a `listInvoices()` function that has the following SQL query.

```
SELECT invoices.amount, customers.name
FROM invoices
JOIN customers ON invoices.customer_id = customers.id
WHERE invoices.amount = 666;
```

Uncomment the file, remove the `Response.json() block`, and navigate to [`localhost:3000/query`](http://localhost:3000/query) in your browser. You should see that an invoice `amount` and `name` is returned.

### It's time to take a quiz!

Test your knowledge and see what you've just learned.

Which customer does this invoice belong to?

A

Lee Robinson

B

Evil Rabbit

C

Delba de Oliveira

D

Michael Novotny

Check Answer

6

You've Completed Chapter 6
--------------------------

With your database now set up and integrated, you can continue building your application.

Next Up

7: Fetching Data

Let's discuss the different ways you can fetch data from your database, including using APIs, SQL, and alternatives.

[Start Chapter 7](https://nextjs.org/learn/dashboard-app/fetching-data)

Was this helpful?

supported.

Send

[](https://vercel.com/home?utm_source=next-site&utm_medium=footer&utm_campaign=next-website "Go to Vercel website")

[](https://github.com/vercel/next.js)

* * *

[](https://x.com/nextjs)

* * *

[](https://bsky.app/profile/nextjs.org)

#### Resources

[Docs](https://nextjs.org/docs)[Support Policy](https://nextjs.org/support-policy)[Learn](https://nextjs.org/learn)[Showcase](https://nextjs.org/showcase)[Blog](https://nextjs.org/blog)[Team](https://nextjs.org/team)[Analytics](https://vercel.com/analytics?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_setting-up-your-database)[Next.js Conf](https://nextjs.org/conf)[Previews](https://vercel.com/products/previews?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_setting-up-your-database)

#### More

[Next.js Commerce](https://vercel.com/templates/next.js/nextjs-commerce?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_setting-up-your-database)[Contact Sales](https://vercel.com/contact/sales?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_setting-up-your-database)[Community](https://community.vercel.com/)[GitHub](https://github.com/vercel/next.js)[Releases](https://github.com/vercel/next.js/releases)[Telemetry](https://nextjs.org/telemetry)[Governance](https://nextjs.org/governance)

#### About Vercel

[Next.js + Vercel](https://vercel.com/solutions/nextjs?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_setting-up-your-database)[Open Source Software](https://vercel.com/oss?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_setting-up-your-database)[GitHub](https://github.com/vercel)[Bluesky](https://bsky.app/profile/vercel.com)[X](https://x.com/vercel)

#### Legal

[Privacy Policy](https://vercel.com/legal/privacy-policy)Cookie Preferences

#### Subscribe to our newsletter

Stay updated on new releases and features, guides, and case studies.

Subscribe

© 2025 Vercel, Inc.

[](https://github.com/vercel/next.js)

* * *

[](https://x.com/nextjs)

* * *

[](https://bsky.app/profile/nextjs.org)