/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
var __export = (target, all) => {
  __markAsModule(target);
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __reExport = (target, module2, desc) => {
  if (module2 && typeof module2 === "object" || typeof module2 === "function") {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && key !== "default")
        __defProp(target, key, { get: () => module2[key], enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable });
  }
  return target;
};
var __toModule = (module2) => {
  return __reExport(__markAsModule(__defProp(module2 != null ? __create(__getProtoOf(module2)) : {}, "default", module2 && module2.__esModule && "default" in module2 ? { get: () => module2.default, enumerable: true } : { value: module2, enumerable: true })), module2);
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// main.ts
__export(exports, {
  default: () => Linkify
});
var import_obsidian = __toModule(require("obsidian"));
var import_view = __toModule(require("@codemirror/view"));
var DEFAULT_SETTINGS = {
  rules: [
    {
      regexp: "g:([a-zA-Z0-9.-]*)",
      link: "http://google.com/search?q=$1",
      cssclass: ""
    },
    {
      regexp: "gh:([a-zA-Z0-9./-]*)",
      link: "http://github.com/$1",
      cssclass: ""
    },
    {
      regexp: "@([a-zA-Z0-9]*)",
      link: "http://twitter.com/$1",
      cssclass: ""
    }
  ]
};
var DEFAULT_NEW_RULE = {
  regexp: "g\\/([a-zA-Z.-]*)",
  link: "http://google.com/search?q=$1",
  cssclass: ""
};
function createViewPlugin(rule) {
  let decorator = new import_view.MatchDecorator({
    regexp: new RegExp(rule.regexp, "g"),
    decoration: import_view.Decoration.mark({
      class: `cm-link linkified ${rule.cssclass}`
    })
  });
  return import_view.ViewPlugin.define((view) => ({
    decorations: decorator.createDeco(view),
    update(u) {
      this.decorations = decorator.updateDeco(u, this.decorations);
    }
  }), {
    decorations: (v) => v.decorations
  });
}
var Linkify = class extends import_obsidian.Plugin {
  constructor() {
    super(...arguments);
    this.viewPlugins = [];
  }
  onload() {
    return __async(this, null, function* () {
      yield this.loadSettings();
      this.addSettingTab(new LinkifySettingTab(this.app, this));
      this.registerEditorExtension(this.viewPlugins);
      this.refreshExtensions();
      this.registerMarkdownPostProcessor(this.markdownPostProcessor.bind(this));
      this.registerDomEvent(document, "click", (evt) => {
        this.openLink(evt);
      });
    });
  }
  loadSettings() {
    return __async(this, null, function* () {
      this.settings = Object.assign({}, DEFAULT_SETTINGS, yield this.loadData());
    });
  }
  saveSettings() {
    return __async(this, null, function* () {
      yield this.saveData(this.settings);
    });
  }
  refreshExtensions() {
    this.viewPlugins.splice(0, this.viewPlugins.length, ...this.settings.rules.map(createViewPlugin));
    this.app.workspace.updateOptions();
  }
  openLink(evt) {
    if (evt.target instanceof HTMLSpanElement && evt.target.className.includes("cm-link linkified")) {
      let m = this.matchRule(evt.target.innerText);
      if (m != null) {
        const internalLinkMatch = m.link.match(/^\[\[([^|\]]*)(?:\|[^|\]]*)?\]\]$/);
        if (internalLinkMatch != null) {
          this.app.workspace.openLinkText(internalLinkMatch.at(1), "");
        } else {
          window.open(m.link);
        }
      }
    }
  }
  matchRule(text) {
    for (let rule of this.settings.rules) {
      let regexp = new RegExp(rule.regexp);
      let m = text.match(regexp);
      if (m != null) {
        return {
          match: m,
          link: m[0].replace(regexp, rule.link),
          cssclass: rule.cssclass
        };
      }
    }
    return null;
  }
  linkifyHtml(text) {
    let m = this.matchRule(text);
    if (m == null) {
      return null;
    }
    let index = m.match.index;
    let matchedText = m.match[0];
    let before = text.substring(0, index);
    let after = text.substring(index + matchedText.length);
    let anchor = document.createElement("a");
    anchor.textContent = matchedText;
    anchor.href = m.link;
    anchor.className = `linkified ${m.cssclass}`;
    let nodes = [];
    nodes.push(before);
    nodes.push(anchor);
    nodes.push(...this.linkifyHtml(after) || [after]);
    return nodes;
  }
  markdownPostProcessor(el) {
    if (el.firstChild instanceof Node) {
      let walker = document.createTreeWalker(el.firstChild, NodeFilter.SHOW_TEXT, null);
      let nodes = [];
      let node;
      while (node = walker.nextNode()) {
        nodes.push(node);
      }
      for (node of nodes) {
        let linkified = this.linkifyHtml(node.textContent);
        if (linkified) {
          node.replaceWith(...linkified);
        }
      }
    }
  }
};
var LinkifySettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("p", {
      text: "Text matching the following regular expressions will be converted into links."
    });
    for (let [index, rule] of this.plugin.settings.rules.entries()) {
      new import_obsidian.Setting(containerEl).setDesc("RegExp/Link").addText((text) => {
        text.setValue(rule.regexp);
        text.inputEl.onblur = () => __async(this, null, function* () {
          rule.regexp = text.getValue();
          yield this.plugin.saveSettings();
        });
      }).addText((text) => {
        text.setValue(rule.link);
        text.inputEl.onblur = () => __async(this, null, function* () {
          rule.link = text.getValue();
          yield this.plugin.saveSettings();
        });
      }).addText((text) => {
        text.setValue(rule.cssclass);
        text.setPlaceholder("CSS Class");
        text.inputEl.onblur = () => __async(this, null, function* () {
          rule.cssclass = text.getValue();
          yield this.plugin.saveSettings();
        });
      }).addButton((button) => {
        return button.setIcon("trash").onClick(() => __async(this, null, function* () {
          this.plugin.settings.rules.splice(index, 1);
          yield this.plugin.saveSettings();
          this.display();
        }));
      });
    }
    new import_obsidian.Setting(containerEl).addButton((button) => button.setButtonText("Add New Link").onClick(() => __async(this, null, function* () {
      this.plugin.settings.rules.push(__spreadValues({}, DEFAULT_NEW_RULE));
      yield this.plugin.saveSettings();
      this.display();
    })));
  }
  hide() {
    this.plugin.refreshExtensions();
  }
};

/* nosourcemap */