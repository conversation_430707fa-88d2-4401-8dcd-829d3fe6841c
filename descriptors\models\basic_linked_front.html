<div style="text-align: left;">
  <div>{{Front}}</div><br>
  <a id="note-link" href="">
    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right: 4px; opacity: 0.6;">
      <path d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    <span id="note-text">Note</span>
  </a>
  <button id="copy-btn" type="button">
    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right: 4px; opacity: 0.6;">
      <path d="M8 4v12a2 2 0 002 2h8a2 2 0 002-2V7.242a2 2 0 00-.602-1.43L16.83 2.83A2 2 0 0015.415 2H10a2 2 0 00-2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M16 18v2a2 2 0 01-2 2H6a2 2 0 01-2-2V9a2 2 0 012-2h2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    <span>复制ID</span>
  </button>
</div>

<style>
  blockquote {
    border-left: 4px solid #ccc;
    padding: 0.4em 0.5em;
    border-radius: 5px;
    color: #555;
    margin: 0.5em 0;
    font-style: italic;
    background: #f9f9f9;
  }
  
  #note-link, #copy-btn {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    background: transparent;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    color: #6b7280;
    text-decoration: none;
    font-size: 11px;
    font-weight: 400;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    transition: all 0.15s ease;
    margin-right: 4px;
    box-shadow: none;
    outline: none;
    cursor: pointer;
  }
  
  #note-link:hover, #copy-btn:hover {
    background: #f9fafb;
    color: #374151;
    border-color: #d1d5db;
  }
  
  #note-link:active, #copy-btn:active {
    background: #f3f4f6;
  }
  
  #note-link svg, #copy-btn svg {
    transition: opacity 0.15s ease;
  }
  
  #note-link:hover svg, #copy-btn:hover svg {
    opacity: 0.8;
  }
</style>

<script>
  // 使用立即执行函数避免变量污染
  (function() {
    // 添加时间戳确保代码执行
    const executionId = Date.now() + '_' + Math.random();
    
    // 直接处理deck名称并构建链接
    const deckName = '{{Deck}}'.replace(/::/g, '/');
    const noteId = '{{info-Nid:}}';
    
    // 转换时间戳为友好的时间显示
    function formatTimestamp(timestamp) {
      try {
        // 处理可能的字符串时间戳
        const ts = parseInt(timestamp);
        
        // 判断是秒时间戳还是毫秒时间戳
        const date = ts > 1000000000000 ? new Date(ts) : new Date(ts * 1000);
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return 'Note';
        }
        
        // 更简洁的时间格式：MM/DD HH:mm
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        return `${month}/${day} ${hours}:${minutes}`;
      } catch (e) {
        return 'Note';
      }
    }
    
    // 复制到剪贴板的函数 - 为Anki Desktop优化
    function copyToClipboard(text) {
      // 创建临时文本区域
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      
      try {
        // 选择文本
        textArea.focus();
        textArea.select();
        // 执行复制命令 - 在Anki Desktop中工作
        document.execCommand('copy');
      } catch (err) {
        console.log('复制失败:', err);
      } finally {
        // 清理
        document.body.removeChild(textArea);
      }
    }
    
    // 更新链接和文本的函数
    function updateNoteLink() {
      const noteLink = document.getElementById('note-link');
      const noteTextElement = document.getElementById('note-text');
      const copyBtn = document.getElementById('copy-btn');
      
      if (noteLink) {
        noteLink.href = `obsidian://open?vault=ob-notes&file=${deckName}.md%23%5E${noteId}`;
      }
      
      if (noteTextElement && noteId) {
        const formattedTime = formatTimestamp(noteId);
        noteTextElement.textContent = formattedTime;
      }
      
      // 添加复制按钮事件监听器
      if (copyBtn && noteId) {
        copyBtn.addEventListener('click', function() {
          copyToClipboard(noteId);
        });
      }
    }
    
    // 立即执行一次
    updateNoteLink();
    
    // 使用多种方式确保执行
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', updateNoteLink);
    } else {
      // DOM已加载，使用setTimeout确保渲染完成
      setTimeout(updateNoteLink, 0);
    }
    
    // 为了处理Anki的特殊情况，再次延迟执行
    setTimeout(updateNoteLink, 100);
  })();
</script>