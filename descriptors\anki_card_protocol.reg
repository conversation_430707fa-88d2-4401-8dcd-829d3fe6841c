Windows Registry Editor Version 5.00

; <PERSON><PERSON><PERSON> to register the anki-card:// URL protocol handler
; This script will associate anki-card:// URLs with the anki_linker.py script.

; IMPORTANT: 
; 1. You MUST replace "C:\\path\\to\\pythonw.exe" with the actual full path to your pythonw.exe.
;    Use double backslashes in the path (e.g., "C:\\Python311\\pythonw.exe").
; 2. You MUST replace "C:\\path\\to\\your_script\\anki_linker.py" with the actual full path to the anki_linker.py script.
;    Use double backslashes in the path (e.g., "C:\\Users\\<USER>\\Documents\\AnkiObsidianLinker\\anki_linker.py").
; 3. Save this file as anki_card_protocol.reg (or any name ending with .reg).
; 4. Double-click the .reg file and confirm to import it into your registry.
; 5. Ensure anki_linker.py is executable and your Python installation can run .py files directly, 
;    or adjust the command to explicitly call pythonw.exe with the script as an argument.

[HKEY_CLASSES_ROOT\anki-card]
@="URL:Anki Card Protocol"
"URL Protocol"=""

[HKEY_CLASSES_ROOT\anki-card\DefaultIcon]
@="C:\\Users\\<USER>\\miniforge3\\envs\\work\\pythonw.exe,1" ; You can change this to a more specific icon if desired, or leave as pythonw.exe's icon.

[HKEY_CLASSES_ROOT\anki-card\shell]
@="open"

[HKEY_CLASSES_ROOT\anki-card\shell\open]

[HKEY_CLASSES_ROOT\anki-card\shell\open\command]
@="\"C:\\Users\\<USER>\\miniforge3\\envs\\work\\pythonw.exe\" \"D:\\Users\\luozh\\Documents\\AnkiObsidianLinker\\anki_linker.py\" \"%1\""
; The "%1" passes the clicked URL (e.g., anki-card://12345) to the script as an argument.
; Using pythonw.exe is recommended to prevent a command prompt window from appearing briefly.

