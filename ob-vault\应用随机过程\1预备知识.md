## 第一章 预备知识

作者：郑旭  
<EMAIL>  
北京师范大学 统计学院

### 预备知识

1. 概率空间
2. 随机变量和分布函数
3. 数学特征、矩母函数与特征函数
4. 收敛性
5. 条件概率，条件期望和独立性

### 随机试验

具有如下三个特性的试验：

- 可以在相同的条件下重复进行；
- 每次试验的结果不止一个但预先知道试验的所有可能的结果；
- 每次试验前不能确定哪个结果会出现。

### 基本概念

样本点($\omega$)： 随机试验的基本结果  
样本空间($\Omega$)： 随机试验所有可能结果组成的集合  
基本事件： $\Omega$中的样本点$\omega$  
必然事件： 样本空间$\Omega$  
不可能事件： 空集$\emptyset$  
事件： 由基本事件组成的$\Omega$中的子集$A$

### $\sigma$代数

**定义**
设$\Omega$是一个样本空间（考虑意义一个集合），$\mathcal{F}$是$\Omega$的某些子集组成的集合。如果满足：  
(1) $\Omega \in \mathcal{F}$;  
(2) 若$A \in \mathcal{F}$, 则$A^c = \Omega \setminus A \in \mathcal{F}$;  
(3) 若$A_n \in \mathcal{F}, n = 1,2,\cdots$, 则$\bigcup_{n=1}^{\infty} A_n \in \mathcal{F}$;  
则称$\mathcal{F}$为$\sigma$代数，($\Omega, \mathcal{F}$)称为可测空间，$\mathcal{F}$中的元素称为事件。

注：如果$\mathcal{F}$是事件的$\sigma$代数，则(1)$\emptyset \in \mathcal{F}$; (2)$A_n \in \mathcal{F}, n \geq 1$, $\bigcap_{n\geq1} A_n \in \mathcal{F}$。

### Borel $\sigma$代数

**定义**
设$\Omega = \mathbb{R}$。由所有半无限区间$(-\infty, x)$生成的$\sigma$代数，即包含所有形如$\{(-\infty, x), x \in \mathbb{R}\}$的最小$\sigma$代数，称为$\mathbb{R}$上Borel $\sigma$代数。记为$\mathcal{B}(\mathbb{R})$, 其中的元素称为Borel 集合。类似地可定义$\mathbb{R}^n$上的Borel $\sigma$代数$\mathcal{B}(\mathbb{R}^n)$。

### 概率

**定义**
设($\Omega, \mathcal{F}$)为可测空间，$P(\cdot)$ 为定义在$\mathcal{F}$上的实值函数。如果  
(1) 对任何$A \in \mathcal{F}$, $0 \leq P(A) \leq 1$;  
(2) $P(\Omega) = 1$;  
(3) 对互不相交的一系列事件$A_1, A_2, \cdots$, （即当$i \neq j$时，$A_i \cap A_j = \emptyset$）有
$$P(\bigcup_{i=1}^{\infty}A_i) = \sum_{i=1}^{\infty}P(A_i),$$

则称$P$为($\Omega, \mathcal{F}$)上的概率，($\Omega, \mathcal{F}, P$)称为概率空间，$P(A)$称为事件$A$的概率。

### 概率常用性质

(1) 若$A, B \in \mathcal{F}$，则$P(A \cup B) + P(A \cap B) = P(A) + P(B)$.  
(2) 若$A, B \in \mathcal{F}$，且$A \subset B$，则$P(A) \leq P(B)$ (单调性).  
(3) 若$A, B \in \mathcal{F}$，且$A \subset B$，则$P(B - A) = P(B) - P(A)$.  
(4) 若$A_n \in \mathcal{F}, n \geq 1$, 则$P(\bigcup_{n\geq1} A_n) \leq \sum_{n\geq1} P(A_n)$  
(5) 若$A_n \in \mathcal{F}$且$A_n \uparrow A$, 即，$A_n \subset A_{n+1}, n \geq 1$，且$\bigcup_{n\geq1} A_n = A$, 则$P(A) = \lim_{n\rightarrow\infty} P(A_n)$ (下连续).  
(6) 若$A_n \in \mathcal{F}$且$A_n \downarrow A$, 即，$A_{n+1} \subset A_n, n \geq 1$且$\bigcap_{n\geq1} A_n = A$则$P(A) = \lim_{n\rightarrow\infty} P(A_n)$ (上连续).

### 集列极限

**定义**
$A_n \in \mathcal{F}, n \geq 1$. 样本点无穷多次属于集合$A_n$的集合称为集列$\{A_n\}$的上极限，记为$\lim \sup_{n\rightarrow\infty} A_n$. 也记为$\{A_n, i.o.\}$。 集列$\{A_n\}$的下极限定义为
$$\lim \inf_{n\rightarrow\infty} A_n = \{\omega \in \Omega : \exists n_0, \forall n > n_0, \omega \in A_n.\}.$$

注：可以证明
$$\lim \sup_{n\rightarrow\infty} A_n = \bigcap_{k=1}^{\infty} \bigcup_{n=k}^{\infty} A_n.$$
$$\lim \inf_{n\rightarrow\infty} A_n = \bigcup_{k=1}^{\infty} \bigcap_{n=k}^{\infty} A_n.$$

### 例1.1

**例**
设有某人在反复地投掷硬币，观察硬币朝上的面是正面还是反面。$\Omega = \{$所有由投掷结果"正面"和"反面"组成的序列$\}$, $\mathcal{F} = \{\Omega$的所有子集$\}$, 记$A_n$为第$n$次投掷的是"正面"的事件，则

$$\lim \sup_{n\rightarrow\infty} A_n = \{$有无限多个投掷结果是"正面"$\}.$$
$$\lim \inf_{n\rightarrow\infty} A_n = \{$除有限多个外，投掷结果都是"正面"$\}.$$

### 随机变量与分布函数

**定义**
设($\Omega, \mathcal{F}, P$)是(完备的)概率空间，$X$是定义在$\Omega$上取值于实数集$\mathbb{R}$的函数，如果对任意实数$x \in \mathbb{R}$, $\{\omega : X(\omega) \leq x\} \in \mathcal{F}$，则称$X(\omega)$为$\mathcal{F}$上的随机变量，简称为随机变量。

$$F(x) = P(\omega : X(\omega) \leq x), -\infty < x < \infty,$$

称为随机变量$X$的分布函数。

### 连续型随机变量&离散型随机变量

如果存在函数$f(x)$, 满足
$$F(x) = \int_{-\infty}^{x} f(t)dt,$$
则称$f(x)$是随机变量$X$对应分布函数$F(x)$的密度函数。如果$X$存在密度函数，则称$X$是连续型随机变量；

如果$X$只取有限个或可数个值，则称$X$是离散型随机变量。

### 几个概念

(1) 映射$X : \Omega \rightarrow \mathbb{R}^d$, 表示为$X = (X_1,\cdots,X_d)$, 若对所有的$k, 1 \leq k \leq d, X_k$都是随机变量, 则称$X$为随机向量。

(2)给定随机变量$X$, 可以生成$\Omega$上的$\sigma$-代数，即包含所有形如$\{X \leq a\}, a \in \mathbb{R}$的最小$\sigma$-代数，记为$\sigma(X)$. 类似地可定义由随机变量$X_1,\cdots ,X_n$生成的$\sigma$-代数$\sigma(X_1,\cdots ,X_n)$。

### 分布列、概率密度&联合分布

(1) 离散型随机变量$X$的概率分布用分布列描述：
$$p_k = P(X = x_k), \quad k = 1, 2, \cdots$$
其分布函数$F(x) = \sum_{x_k\leq x} p_k.$

(2)连续型随机变量$X$的概率分布用概率密度$f(x)$描述，其分布函数
$$F(x) = \int_{-\infty}^{x} f(t)dt.$$

(3)对于随机向量$X = (X_1,\cdots ,X_d)$, 它的(d维)分布函数（或联合分布函数）定义为
$$F(x_1,\cdots ,x_d) = P(X_1 \leq x_1,\cdots ,X_d \leq x_d).$$
这里$d \geq 1, x_k \in \mathbb{R}, 1 \leq k \leq d$.

### 常用定理

**定理**
若$F(x_1,\cdots ,x_d)$是联合分布函数，则  
(i) $F(x_1,\cdots ,x_d)$对每个变量都是单调的，  
(ii) $F(x_1,\cdots ,x_d)$对每个变量都是右连续的，  
(iii) 对$i = 1,2,\cdots ,d$
$$\lim_{x_i\rightarrow -\infty} F(x_1,\cdots ,x_i,\cdots ,x_d) = 0,$$
$$\lim_{x_1,\cdots,x_d\rightarrow \infty} F(x_1,\cdots ,x_d) = 1.$$

### 联合密度&边际分布

(1) 如果$f(x_1,\cdots ,x_d) = (\frac{\partial^d F}{\partial x_1\cdots\partial x_d})$对所有的$(x_1,\cdots ,x_d) \in \mathbb{R}^d$存在则称函数$f(x_1,\cdots ,x_d)$ 为$F(x_1,\cdots ,x_d)$或$X = (X_1,\cdots ,X_d)$的联合密度函数并且
$$F(x_1,\cdots ,x_d) = \int_{-\infty}^{x_1} \cdots \int_{-\infty}^{x_d} f(t_1,\cdots ,t_d)dt_d \cdots dt_1.$$

(2)设$F(x_1,\cdots ,x_d)$为$X_1,\cdots ,X_d$的联合分布函数，$1 \leq k_1 < k_2 < \cdots < k_n \leq d$, 则$X_1,\cdots ,X_d$的边际分布$F_{k_1,\cdots,k_n}(x_{k_1},\cdots ,x_{k_n})$ 定义为
$$F_{k_1,\cdots,k_n}(x_{k_1},\cdots ,x_{k_n}) = F(\infty,\cdots,\infty,x_{k_1},\infty,\cdots,\infty,x_{k_2},\infty,\cdots,\infty,x_{k_n},\infty,\cdots,\infty)$$

### 常用分布

**离散均匀分布**：如果其分布列为
$$p_k = \frac{1}{n}, k = 1, 2, \cdots , n,$$
则称之为离散均匀分布。

**二项分布**：如果其分布列为：对固定的$n$和$0 < p < 1$,
$$p_k = \binom{n}{k} p^k(1-p)^{n-k}, k \geq 0,$$
则称之为以$n$和$p$为参数的二项分布.

**几何分布**：如果其分布列$\{p_k\}, k \geq 0$表示为
$$p_k = pq^{k-1}, k \geq 1,$$
其中$p + q = 1$，则称之为几何分布.

### 常用分布

**Poisson 分布**：如果其分布列$\{p_k\}, k \geq 0$表示为
$$p_k = e^{-\lambda}\lambda^k/k!, \quad k = 0, 1, \cdots$$
则称之为参数为$\lambda > 0$的Poisson分布.

**连续的均匀分布**（简称为均匀分布）：如果其密度函数为
$$f(x) = \begin{cases}
[b - a]^{-1}, & 若 a \leq x \leq b, \\
0, & 其他,
\end{cases}$$
其中$a < b$，则称之为区间$[a, b]$上均匀分布.

**正态分布**：如果其密度函数为
$$f(x) = \frac{1}{\sigma\sqrt{2\pi}} \cdot \exp[-(x - \mu)^2/2\sigma^2], x \in \mathbb{R},$$
则称之为参数为$\mu$和$\sigma^2$的正态分布，也称为Gauss分布。若随机变量$X$服从正态分布，则记为$X \sim N(\mu, \sigma^2)$。

### 常用分布

**$\Gamma$ 分布**：如果其密度函数为
$$f(x) = \begin{cases}
[\lambda/\Gamma(\alpha + 1)](\lambda x)^{\alpha}e^{-\lambda x}, & x > 0, \\
0, & x \leq 0,
\end{cases}$$
则称之为以$\alpha > -1$, $\lambda > 0$为参数的$\Gamma$分布，这里$\Gamma$函数定义为
$$\Gamma(x) = \int_0^{\infty} e^{-y}y^{x-1}dy, \quad x > 0,$$

**指数分布**: 如果在$\Gamma$分布中令$\alpha = 0$, $\lambda > 0$, 即密度函数为
$$f(x) = \begin{cases}
\lambda e^{-\lambda x}, & x > 0, \\
0, & x \leq 0,
\end{cases}$$
则称之为指数分布.

### 常用分布

**$\chi^2$分布**:如果在$\Gamma$分布中取$\alpha = (n - 2)/2, n$是正整数, 并且$\lambda = \frac{1}{2}$, 则
$$f(x) = [2^{n/2}\Gamma(n/2)]^{-1}x^{(n-2)/2}e^{-x/2}, \quad x > 0,$$
称为自由度是$n$的$\chi^2$分布.

**d维正态分布**: 设$\mu = (\mu_1,\cdots ,\mu_d)$, $\Sigma$是$d$阶对称正定矩阵，并且记$|\Sigma|$. 如果其联合密度函数为
$$f(x_1,\cdots ,x_d) = (2\pi)^{-d/2}|\Sigma|^{-1/2}\exp[-\frac{1}{2}(x - \mu)^{\prime}\Sigma^{-1}(x - \mu)].$$
则称之为$d$维正态分布.若随机变量$X$服从$d$维正态分布，则记为$X \sim N(\mu, \Sigma)$.

### Riemann-Stieltjes积分

设$g(x), F(x)$ 为有限区间$(a, b]$上的实值函数，$a = x_0 < x_1 < \cdots < x_n = b$为$(a, b]$的一个分割，令$\Delta F(x_i) = F(x_i) - F(x_{i-1})$, $\xi_i \in [x_{i-1}, x_i], 1 \leq i \leq n, \lambda = \max_{1\leq i\leq n}(x_i - x_{i-1})$，如果当$\lambda \rightarrow 0$时，极限
$$\lim_{\lambda\rightarrow 0} \sum_{i=1}^{n} g(\xi_i)\Delta F(x_i)$$
存在，且与分割的选择以及$\xi_i \in [x_{i-1}, x_i]$的取法无关，则称该极限值为函数$g(x)$关于$F(x)$ 在$(a, b]$上Riemann-Stieltjes积分，记为
$$\int_a^b g(x)dF(x) = \lim_{\lambda\rightarrow 0} \sum_{i=1}^{n} g(\xi_i)\Delta F(x_i).$$

### 几点说明

(1)当$F(x) = x$时，Riemann-Stieltjes积分就是Riemann积分
$$\int_a^b g(x)dx$$

(2)当$F^{\prime}(X) = f(x)$存在时，Riemann-Stieltjes积分就是Riemann积分
$$\int_a^b g(x)f(x)dx.$$

(3)若函数$g(x)$连续，$F(x)$单调，则Riemann-Stieltjes积分存在。

(4)积分推广到无限区间上：
$$\int_{a}^{+\infty} g(x)dF(x) =: \lim_{b\rightarrow+\infty} \int_a^b g(x)dF(x)$$
$$\int_{-\infty}^{b} g(x)dF(x) =: \lim_{a\rightarrow-\infty} \int_a^b g(x)dF(x).$$

### Riemann-Stieltjes积分的性质

积分的线性性质：
$$\int_a^b [\alpha g_1(x)\pm\beta g_2(x)]dF(x) = \alpha \int_a^b g_1(x)dF(x)\pm\beta \int_a^b g_2(x)dF(x);$$

积分对子区间的可加性：
$$\int_a^b g(x)dF(x) = \int_a^c g(x)dF(x) + \int_c^b g(x)dF(x)$$

以及
$$\int_a^b dF(x) = F(b) - F(a)$$

### 特殊性质

(1)当$F(x)$在$x = a$处有跳跃时：
$$\int_{a-}^{a} dF(x) = \lim_{\delta\rightarrow0+} \int_{a-\delta}^{a} dF(x) = F(a) - F(a-).$$

(2)设$F(x)$在$x = x_i$处有跃度$p_i, i = 1, 2, \cdots$,则
$$\int_{-\infty}^{+\infty} g(x)dF(x) = \sum_{i=1}^{+\infty} g(x_i)p_i.$$

### 数学期望

**定义**
(1) 取值为$\{s_k\}$的离散型随机变量的数学期望（简称为期望）$EX$定义为
$$EX = \sum_k s_kp_k = \sum_k s_kP(X = s_k),$$
如果$\sum |s_k|p_k < \infty$。

(2) 连续型随机变量$X$的数学期望 $E[X]$定义为
$$E[X] = \int_{-\infty}^{\infty} xdF(x) = \int_{-\infty}^{\infty} xf(x)dx,$$
如果$\int_{-\infty}^{\infty} |x|dF(x) < \infty$, 这里$F(x)$是$X$的分布函数,$f(x)$是其密度函数.

### 数学期望

用Riemann-Stieltjes积分，我们可以将离散型随机变量和连续型随机变量的各阶矩统给出一个统一的表达式：
$$EX^k = \int_{-\infty}^{+\infty} x^kdF(x),$$

$$E(X - EX)^k = \int_{-\infty}^{+\infty} (x - EX)^kdF(x).$$

### 矩

**定义**
(4) 在(3)中若$g(X_1,\cdots ,X_d) = X_1^{k_1}\cdots X_d^{k_d}, k_i \geq 0, 1 \leq i \leq d$, 则$E[X_1^{k_1}\cdots X_d^{k_d}]$称为$(X_1,\cdots ,X_d)$的$(k_1,\cdots ,k_d)$阶矩

(5) $X$的$k$阶中心矩 定义为$m_k = \int_{\mathbb{R}}(x - \mu)^kdF(x)$, 这里$\mu = EX$. 二阶中心矩称为$X$的方差 记为$\sigma^2$.

(6) 对任何两个具有有限方差$\sigma_X^2$和$\sigma_Y^2$的随机变量$X$和$Y$的协方差 $Cov(X,Y)$定义为$Cov(X,Y) = E[(X - \mu_X)(Y - \mu_Y)]$.

### 简单可测函数

设($\Omega, \mathcal{F}, P$)是完备的概率空间，$\Omega$上的只取有限个值的随机变量称为简单函数。

如果存在实数$a_k, 1 \leq k \leq n$和$\Omega$的分割$A_k \in \mathcal{F}, 1 \leq k \leq n$ （即$\bigcup_1^n A_k = \Omega$, 且$A_i\cap A_j = \emptyset, i \neq j, 1 \leq i,j \leq n$），使得$h(\omega) = \sum_1^n a_kI_{A_k}(\omega)$, 则称$h$为简单可测函数。

注：通过搜分割中的集合合并可以得到$h$的最简单表达式，即表达式中的系数$a_k$互不相同。

### 非负简单可测函数的概率测度积分

**定义**
令$S$表示所有非负简单可测函数$h : \Omega \rightarrow R_+$。如果$h \in S$, 则定义其关于$P$的积分为
$$\int hdP = \sum_{1}^{n} a_kP(A_k) = E[h].$$

注：积分$\int hdP$与函数$h$的不同表示$\sum_{k=1}^n a_kI_{A_k}$无关。

### 非负可测函数的概率测度积分

**定义**
令$S^*$为$\Omega$ 上的所有非负可测函数，则对任何的$X \in S^*$, 存在$h_n \in S$, $h_n$单增使得$X = \sup_n h_n$。我们定义$X$的积分为
$$\int_{\Omega} XdP = \sup_n Eh_n = \int_{\Omega} h_ndP.$$

注：$X$的积分与单调增加序列$\{h_n\}$的选择无关。

### 可测函数的概率测度积分

**定义**
令$X$是$\Omega$上的任意随机变量。定义
$$X^+ = \max(X, 0) \text{ 和 } X^- = -\min(X, 0).$$
注意$X^+ \geq 0, X^- \geq 0, X = X^+ - X^-$, 以及$|X| = X^+ + X^-$。
如果$\int_{\Omega} X^+dP$和$\int_{\Omega} X^-dP$ 至少有一个为有限数, 我们定义$X$积分
$$\int_{\Omega} XdP = \int_{\Omega} X^+dP - \int_{\Omega} X^-dP,$$

当$\int_{\Omega} X^+dP$和$\int_{\Omega} X^-dP$都是有限数时，我们称$X$可积。
注：当随机变量$X$可积时，数学期望可以定义为
$$EX = \int_{\Omega} XdP,$$

### 重要定理

**定理**
(1) 设$X \in S^*$，则$EX = 0$当且仅当$X = 0$ a.s.  
(2)如果$EX$存在, 即$\int |X|dP < \infty$且$Y = X$ a.s.，则$EY$ 存在且$EX = EY$.  
(3) 设$X$和$Y$ 是两个随机变量, 使得$EY$ 存在而且$|X| \leq Y$则$EX$存在.  
(4) 如果$EX$存在，则$X$几乎处处有限，即$|X| < \infty$, a.s.  
(5) 如果$X$是非负整数值的，则$EX = \sum_n P(X > n)$.  
注：关于$\Omega$ 中子集称性质$\Pi$，如果包含$\Pi$不成立的点的集合的概率是零，则称$\Pi$ 几乎必然(almost surely) 成立(也称概率1(with probability one)成立)，记为a.s. 或w.p.1。

### 矩母函数

**定义**
若随机变量$X$的分布函数为$F_X(x)$, 则称
$$\phi_X(t) = E(e^{tX}) = \int_{\Omega} e^{tX(\omega)}P(d\omega) = \int_{-\infty}^{\infty} e^{tx}dF_X(x)$$
为$X$的矩母函数。

注：$X$的各阶矩与矩母函数的关系
$$\phi^{\prime}(t) = E(Xe^{tX})$$
$$\phi^{\prime\prime}(t) = E(X^2e^{tX})$$
$$\vdots$$
$$\phi^{(n)}(t) = E(X^ne^{tX}).$$
令$t = 0$, 得到$\phi^{(n)}(0) = EX^n$.

### 特征函数

**定义**
称
$$\psi(t) = E(e^{itX}) = \int_{\Omega} e^{itX(\omega)}P(d\omega) = \int_{-\infty}^{\infty} e^{itx}dF_X(x)$$
为$X$的特征函数。 其中，$F_X$是$X$的分布函数，如果$F_X$有密度$f$, 则$\psi(t)$就是$f$的Fourier变换
$$\psi(t) = \int_{-\infty}^{\infty} e^{itx}f(x)dx.$$

注：
- 特征函数是一个实变量的复值函数，因为$|e^{itx}| = 1$, 所以它对一切实数$t$都有定义。
- 特征函数只与分布函数有关, 因此也称之为某一分布的特征函数。

### 特征函数的常用性质

- 有界性: $|\psi(t)| \leq 1 = \psi(0)$;
- 共轭对称性: $\psi(-t) = \overline{\psi(t)}$;
- 一致连续性: $|\psi(t + h) - \psi(t)| \leq \int_{-\infty}^{\infty} |e^{ihx} - 1|dF(x)$;
- 线性变换的作用: 设$Y = aX + b$,则$Y$的特征函数是
$$\psi_Y(t) = e^{ibt}\psi(at);$$
- 两个相互独立的随机变量之和的特征函数等于它们的特征函数之积;
- 如何用特征函数表示两个随机变量$X,Y$独立？

### 例1.2

**例**
正态分布$N(0, 1)$的特征函数为
$$\psi(t) = \frac{1}{\sqrt{2\pi}} \int_{-\infty}^{\infty} e^{itx-\frac{x^2}{2}}dx.$$
进而有
$$\psi^{\prime}(t) = \frac{1}{\sqrt{2\pi}} \int_{-\infty}^{\infty} ixe^{itx-\frac{x^2}{2}}dx$$
$$= \frac{1}{\sqrt{2\pi}} \int_{-\infty}^{\infty} ie^{itx}d(-e^{-\frac{x^2}{2}})$$
$$= -\frac{1}{\sqrt{2\pi}}ie^{itx-\frac{x^2}{2}}\bigg|_{-\infty}^{\infty} - \frac{t}{\sqrt{2\pi}} \int_{-\infty}^{\infty} e^{itx-\frac{x^2}{2}}dx.$$

从而有$\psi^{\prime}(t) + t\psi(t) = 0$. 解得$\psi(t) = e^{-\frac{t^2}{2}}$.

### 常用结论

**定理**
分布函数由其特征函数唯一决定。

**性质**
若$X \sim N(0, I_d)$, 则$X$的任一线性函数$Y = A_{n\times d}X + \mu$服从$n$维正态分布$N(\mu, AA^{\prime})$。

**性质**
若$Y \sim N(\mu, \Sigma)$, 则$AY + b \sim N(A\mu + b, A\Sigma A^{\prime})$。

### 几乎必然收敛

**定义**
(1) 设$\{X_n, n \geq 1\}$是随机变量序列, 若存在随机变量$X$使得
$$P(\omega \in \Omega : X(\omega) = \lim_{n\rightarrow\infty} X_n(\omega)) = 1,$$
则称随机变量序列$\{X_n, n \geq 1\}$几乎必然收敛（或以概率1收敛）于$X$，记为$X_n \rightarrow X, a.s.$

### 依概率收敛

**定义**
(2) 设$\{X_n, n \geq 1\}$是随机变量序列, 若存在随机变量$X$使得对任意的$\varepsilon > 0$,有
$$\lim_{n\rightarrow\infty} P(|X_n - X| \geq \varepsilon) = 0.$$
则称随机变量序列$\{X_n, n \geq 1\}$依概率收敛于$X$, 记为$X_n \stackrel{P}{\longrightarrow} X$.

### 均方收敛

**定义**
(3) 设随机变量序列$\{X_n\} \subset L^p,p \geq 1$, 若存在随机变量$X \in L^p$使得
$$\lim_{n\rightarrow\infty} E|X_n - X|^p = 0,$$
则称随机变量序列$\{X_n, n \geq 1\}$p次平均收敛于$X \in L^p$, 或称为p阶矩收敛; 当$p = 2$时, 称为均方收敛.

### 依分布收敛

**定义**
(4) 设$\{F_n(x)\}$是分布函数列, 如果存在一个单调不减函数$F(x)$,使得在$F(\cdot)$的所有连续点$x$上有
$$\lim_{n\rightarrow\infty} F_n(x) = F(x),$$
则称$\{F_n(x)\}$弱收敛于$F(x)$; 设$\{X_n, n \geq 1\}$是随机变量序列, $F_n$是其分布函数列, 如果$\{F_n(x)\}$ 弱收敛于分布函数$F(x)$, 则称$\{X_n, n \geq 1\}$依分布收敛.

### 常用性质

**定理**
(1) 若$X_n \rightarrow X, a.s.$, 则$X_n \rightarrow X$依概率收敛于$X$, 反之不一定成立。  
(2) 随机变量序列$X_n \rightarrow X, a.s.$ 的充分必要条件是对任意的实数$\varepsilon > 0$,
$$\lim_{n\rightarrow\infty} P(\sup_{m\geq n} |X_m - X| \geq \varepsilon) = 0.$$
(3) 随机变量序列$X_n$ 依概率收敛于$X$ 的充分必要条件是$\{X_n\}$的任意子序列都包含几乎必然收敛于$X$的子序列.  
(4) 依p阶矩收敛蕴含依概率收敛, 反之不真。

### 收敛性之间的关系

几乎必然收敛$\Rightarrow$依概率收敛$\Rightarrow$依分布收敛；

p阶矩收敛$\Rightarrow$ 依概率收敛$\Rightarrow$依分布收敛。

注：几乎必然收敛与p阶矩收敛之间没有蕴含关系

### 例1.3

取$\Omega = (0, 1]$, $\mathcal{F}$为$(0, 1]$中全体Borel子集所构成的$\sigma$-代数，$P$为Lebesgue测度.我们可以构造出2个随机变量序列,其中之一是p阶矩收敛的,但是不几乎必然收敛;另外一个则几乎必然收敛, 但不是p阶矩收敛的。

### 例1.3

令
$$Y_{11}(\omega) = 1; \quad Y_{21}(\omega) = \begin{cases}
1, & \omega \in (0, \frac{1}{2}] \\
0, & \omega \in (\frac{1}{2}, 1],
\end{cases}$$

$$Y_{22} = \begin{cases}
0, & \omega \in (0, \frac{1}{2}] \\
1, & \omega \in (\frac{1}{2}, 1].
\end{cases}$$

一般地，将$(0, 1]$分成$k$个等长区间，并且令
$$Y_{ki} = \begin{cases}
1, & \omega \in (\frac{i-1}{k}, \frac{i}{k}], \\
0, & \omega \notin (\frac{i-1}{k}, \frac{i}{k}],
\end{cases} \quad i = 1,\cdots , k, \quad k = 1, 2, \cdots .$$

### 例1.3

定义随机变量序列
$$X_1 = Y_{11}, X_2 = Y_{21}, X_3 = Y_{22}, X_4 = Y_{31}, X_5 = Y_{32}, \cdots .$$
对任意的$\varepsilon > 0$, 由于
$$E|Y_{ki} - 0|^r = \frac{1}{k} \rightarrow 0,(k \rightarrow \infty)$$
可见$\{X_n\}$是$r$阶矩收敛的，但是对任一固定$\omega \in \Omega$, 对于某个固定的$i$，使得$Y_{ki}(\omega) = 1$, 而对其余的$j\neq i$, $Y_{kj}(\omega) = 0$。于是$\{X_n(\omega)\}$中既有无穷多个1又有无穷多个0，于是$\{X_n(\omega)\}$对每个$\omega \in \Omega$ 都不收敛。

### 例1.3

如果取
$$Z_n(\omega) = \begin{cases}
n^{\frac{1}{r}}, & 若\omega \in (0, \frac{1}{n}], \\
0 , & 若\omega \notin (0, \frac{1}{n}],
\end{cases}$$
$$Z(\omega) = 0,(\forall\omega \in \Omega)$$
显然，$Z_n(\omega) \rightarrow Z(\omega)(\forall\omega \in \Omega)$, 所以
$$Z_n \rightarrow Z, a.s.$$
但是$E(|Z_n - Z|^r) = n \cdot \frac{1}{n} = 1 \neq 0$.

### 重要定理

**定理**
(Levi 单调收敛定理)
令$\{X_n\}$是$S^*$中单调增加序列, 则
$$\sup_n X_n \in S^* \quad \text{且} \quad E(\sup_n X_n) = \sup_n EX_n.$$
因此
$$\sum_{n=1}^{\infty} X_n \in S^* \quad \text{且} \quad E(\sum_{n=1}^{\infty} X_n) = \sum_{n=1}^{\infty} EX_n$$
对每个序列$\{X_n\} \subset S^*$成立。

**定理**
(Fatou引理) 对任何序列$\{X_n\} \subset S^*$, 有
$$E(\lim \inf_{n\rightarrow\infty} X_n) \leq \lim \inf_{n\rightarrow\infty} EX_n \leq \lim \sup_{n\rightarrow\infty} EX_n \leq E(\lim \sup_{n\rightarrow\infty} X_n).$$

### 重要定理

**定理**
(Lebesgue控制收敛定理) 令$\{X_n\}$是$L^p(\Omega)$中的序列, 在$\Omega$上几乎必然收敛, $Y$是$L^p$中的非负函数且$|X_n| \leq Y, \forall n \geq 1$,则存在随机变量$X$,使得当$n \rightarrow \infty$时,
$$X_n(\omega) \rightarrow X(\omega), a.s., \quad X \in L^p \quad \text{且}E|X_n - X|^p \rightarrow 0.$$

注：用$L^p(\Omega), p \geq 1$表示使得$E[|X|^p] < \infty$的随机变量 (等价类) 的全体。

### 条件概率

**定义**
(条件概率) 设$B$是一个事件, 且$P(B) > 0$。则定义给定$B$, 事件$A$的条件概率为
$$P_B(A) = P(A \cap B)/P(B).$$
易见, 这样定义的映射$P_B(\cdot) : \mathcal{F} \rightarrow [0,1]$是$\mathcal{F}$上的测度且$P_B(B) = 1$, 习惯上记为$P(A|B)$。

### 全概率公式和贝叶斯公式

**定理**
(全概率公式) 设$\{B_n\}$是$\Omega$的一个分割, 且使得$P(B_n) > 0, \forall n$。如果$A \in \mathcal{F}$, 则
$$P(A) = \sum_n P(B_n)P(A|B_n).$$

**定理**
(Bayes公式) 设$\{B_n\}$是$\Omega$的一个分割, 且使得$P(B_n) > 0, \forall n$。如果$A \in \mathcal{F}$, 则
$$P(B_n|A) = \frac{P(B_n)P(A|B_n)}{\sum_k P(B_k)P(A|B_k)}, \quad n \geq 1.$$

### 条件期望

**定义**
设$X$是随机变量，$B$是事件且$P(B) > 0$，则给定事件$B$, 随机变量$X$的条件期望 定义为
$$E(X|B) = \int XdP_B = [P(B)]^{-1}\int_B XdP = [P(B)]^{-1}E[XI_B].$$

**定理**
设$X$是随机变量且$E(|X|) < \infty$。则对每个$\sigma$代数$\mathcal{B} \subset \mathcal{F}$, 存在唯一的（几乎必然意义下）随机变量$X^*$, 有$E[|X^*|] < \infty$, 使得$X^*$ 是$\mathcal{B}$可测随机变量，并且对所有的$B \in \mathcal{B}$，有$E[X^*I_B] = E[XI_B]$. (此时, 称随机变量$X^*$为$X$在给定$\mathcal{B}$下的条件期望，记为$X^* = E(X|\mathcal{B})$。) 我们有
$$\int_B E(X|\mathcal{B})dP = \int_B XdP, \quad \forall B \in \mathcal{B}.$$

### 常见性质

**定理**
(1) 若$X \in L^1$, 则$E[E(X|\mathcal{B})] = EX$.  
(2) 若$X$是$\mathcal{B}$随机变量, 则$E(X|\mathcal{B}) = X, a.s.$  
(3) 若$X = Y, a.s.$且$X \in L^1$, 则$E(X|\mathcal{B}) = E(Y|\mathcal{B}), a.s.$  
(4) 若$a, b \in R$, $X, Y \in L^1$, 则
$$E[aX + bY|\mathcal{B}] = aE(X|\mathcal{B}) + bE(Y|\mathcal{B})$$  
(5) 若$X, Y \in L^1$且$X \leq Y, a.s.$, 则$E(X|\mathcal{B}) \leq E(Y|\mathcal{B}), a.s.$。  
(6) 若$X_n, n \geq 1$, 是非负单调增加的随机变量序列, 则$E(\sup_n X_n|\mathcal{B}) = \sup_n E(X_n|\mathcal{B}), a.s.$。

### 常见性质

**定理**
(7) 若$\{X_n\}$是随机变量序列, $X_n(\omega) \rightarrow X(\omega), a.s.$且存在$Y \in L^1(\Omega)$使得$|X_n| \leq Y, \forall n$, 则$\lim_{n\rightarrow\infty} E(X_n|\mathcal{B}) = E(X|\mathcal{B}), a.s.$  
(8) 若$\mathcal{B}_1,\mathcal{B}_2$是两个$\sigma$代数, 使得$\mathcal{B}_1 \subset \mathcal{B}_2 \subset \mathcal{F}$, 则
$$E[E(X|\mathcal{B}_1)|\mathcal{B}_2] = E[E(X|\mathcal{B}_2)|\mathcal{B}_1] = E(X|\mathcal{B}_1), a.s.$$  
(9) 若$X, Y$是两个独立的随机变量, 函数$\phi(x, y)$使得$E(|\phi(X, Y)|) < +\infty$, 则
$$E[\phi(X, Y)|Y] = E[\phi(X, y)]|_{y=Y}, \quad a.s.$$
这里$E[\phi(X, y)]|_{y=Y}$的意义是, 先将$y$视为常数, 求得数学期望$E[\phi(X, y)]$ 后再将随机变量$Y$代入到$y$的位置。

### 条件密度

**定义**
设$f(x_1,\cdots ,x_d)$是随机变量$X_1,\cdots ,X_d$的联合密度函数, 则$X_1,\cdots ,X_k$在给定$X_{k+1},\cdots ,X_d$时的条件密度$f_{1,\cdots,k}(u_1,\cdots ,u_k|x_{k+1},\cdots ,x_d)$ 定义为
$$f_{1,\cdots,k}(u_1,\cdots ,u_k|x_{k+1},\cdots ,x_d) = \frac{f(u_1,\cdots ,u_k, x_{k+1},\cdots ,x_d)}{\int\cdots\int f(y_1,\cdots ,y_k, x_{k+1},\cdots ,x_d)dy_1 \cdots dy_k}.$$

### 独立性

**定义**
(1) 设$\{A_i, i \in I\}$是$\mathcal{F}$ 的事件族, 如果 对$I$的每个有限子集$\{i_1,\cdots ,i_k\} \neq \emptyset$, 有
$$P(\bigcap_{j=1}^{k} A_{i_j}) = \prod_{j=1}^{k}P(A_{i_j}), \tag{1}$$
则称$\{A_i, i \in I\}$关于$P$是相互独立的。

(2) 设$\{A_i, i \in I\}$是$\mathcal{F}$的$\sigma$代数族, 如果对$I$的每个有限子集$\{i_1,\cdots ,i_k\} \neq \emptyset$, $A_{i_j} \in A_{i_j}$ 有式成立，则称$\{A_i, i \in I\}$是独立的。

(3) 设$\{X_i, i \in I\}$是$\Omega$上随机变量族, 如果$\sigma$代数族$\{\sigma(X_i)\}, i \in I, 是独立的，则称$\{X_i, i \in I\}$是独立的。

注：随机变量$X_1,\cdots ,X_n$是独立的充分必要条件是它们的联合分布函数可以分解为$F(x_1,\cdots ,x_n) = F_{X_1}(x_1)\cdots F_{X_n}(x_n)$.

### 常用定理

**定理**
(1)$X_1,\cdots ,X_n$是独立且属于$L^1$, 则
$$E(\prod_{k=1}^n X_k) = \prod_{k=1}^n EX_k.$$

(2)$X_1,\cdots ,X_n \in L^2$是独立的, 则
$$var(\sum_{k=1}^{n}X_k) = \sum_{k=1}^{n}var(X_k).$$

### 常用定理

**定理**
(Borel-Cantelli第一引理)
设$\{A_n\}, n \geq 1$是一列事件, 且$A = \lim \sup A_n$.
若$\sum_n P(A_n) < \infty$, 则$P(A_n \text{ i.o.}) = P(A) = 0$.

**定理**
(Borel-Cantelli 第二引理)
如果$\{A_n\}$是独立的事件列, 使得$\sum_{n=1}^{\infty} P(A_n) = \infty$,
则$P(A_n \text{ i.o.}) = 1$.

可用于证明强大数律或者强和性。

### 独立随机变量和的分布

设随机变量$X_1,X_2$相互独立, $F_1,F_2$分别为它们的分布函数。令$X = X_1 + X_2$,其分布函数记为$F_X(x)$. 则由独立性,有
$$F_X(x) = P(X_1 + X_2 \leq x)$$
$$= \int_{-\infty}^{\infty} P(X_1 + X_2 \leq x|X_1 = t)dF_1(t)$$
$$= \int_{-\infty}^{\infty} F_2(x - t)dF_1(t)$$
$$\triangleq F_1 * F_2(x)$$
上述第三个等号右端表示分布函数$F_1, F_2$的卷积, 记为$F_1 * F_2(x)$。

### 卷积性质

- 对称性: $F_1 * F_2(x) = F_2 * F_1(x)$.
- 结合性: $(F * G) * H(x) = F * (G * H)(x)$
- 分配性: $F * (G + H)(x) = F * G(x) + F * H(x)$.

### $n$重卷积

设$X_k, k = 1, 2, \cdots , n$是独立同分布的随机变量, 令$S_0 = 0, S_n = \sum_{k=1}^{n} X_k, n = 1, 2, \cdots , S_n$的分布记作$F_n$, 则有
$$F_0(x) = \begin{cases}
0, & x < 0, \\
1, & x \geq 0,
\end{cases}$$
$$F_n(x) = F * F_{n-1}(x), n = 1, 2, \cdots$$
$F_n$称为$F$的$n$重卷积。