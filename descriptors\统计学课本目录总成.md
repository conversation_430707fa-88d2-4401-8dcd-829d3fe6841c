## 数学分析I/II

第一章 实数集与函数 1
§1 实数 1
一、实数及其性质 1
二、绝对值与不等式 3
§2 数集、确界原理 4
一、区间与邻域 4
二、有界集、确界原理 5
§3 函数概念 8
一、函数的定义 9
二、函数的表示法 9
三、函数的四则运算 10
四、复合函数 11
五、反函数 11
六、初等函数 13
§4 具有某些特性的函数 14
一、有界函数 15
二、单调函数 16
三、奇函数和偶函数 17
四、周期函数 17

第二章 数列极限 21
§1 数列极限概念 21
§2 收敛数列的性质 26
§3 数列极限存在的条件 32

第三章 函数极限 41
§1 函数极限概念 41
一、x趋于∞时函数的极限 41
二、x趋于x₀时函数的极限 42
§2 函数极限的性质 46
§3 函数极限存在的条件 50
§4 两个重要的极限 53
一、证明lim(sinx/x)=1 53
二、证明lim(1+1/x)ˣ=e 54

§5 无穷小量与无穷大量 56
一、无穷小量 56
二、无穷小量的比较 57
三、无穷大量 59
四、曲线的渐近线 61

第四章 函数的连续性 65
§1 连续性概念 65
一、函数在一点的连续性 65
二、间断点及其分类 66
三、区间上的连续函数 68
§2 连续函数的性质 69
一、连续函数的局部性质 69
二、闭区间上连续函数的基本性质 71
三、反函数的连续性 73
四、一致连续性 74
§3 初等函数的连续性 78
一、指数函数的连续性 78
二、初等函数的连续性 80

第五章 导数和微分 83
§1 导数的概念 83
一、导数的定义 83
二、导函数 85
三、导数的几何意义 87
§2 求导法则 90
一、导数的四则运算 90
二、反函数的导数 92
三、复合函数的导数 93
四、基本求导法则与公式 95
§3 参变量函数的导数 97
§4 高阶导数 100
§5 微分 104
一、微分的概念 104
二、微分的运算法则 106
三、高阶微分 106
四、微分在近似计算中的应用 107

第六章 微分中值定理及其应用 111
§1 拉格朗日定理和函数的单调性 111
一、罗尔定理与拉格朗日定理 111
二、单调函数 114
§2 柯西中值定理和不定式极限 117
一、柯西中值定理 117
二、不定式极限 118
§3 泰勒公式 125
一、带有佩亚诺型余项的泰勒公式 125
二、带有拉格朗日型余项的泰勒公式 128
三、在近似计算上的应用 130
§4 函数的极值与最大(小)值 132
一、极值判别 132
二、最大值与最小值 134
§5 函数的凸性与拐点 137
§6 函数图像的讨论 143
*§7 方程的近似解 145

第七章 实数的完备性 150
§1 关于实数集完备性的基本定理 150
一、区间套定理 150
二、聚点定理与有限覆盖定理 151
三、实数完备性基本定理之间的等价性 154
§2 上极限和下极限 156

第八章 不定积分 161
§1 不定积分概念与基本积分公式 161
一、原函数与不定积分 161
二、基本积分表 163
§2 换元积分法与分部积分法 166
一、换元积分法 166
二、分部积分法 171
§3 有理函数和可化为有理函数的不定积分 175
一、有理函数的不定积分 175
二、三角函数有理式的不定积分 179
三、某些无理式的不定积分 180

第九章 定积分 186
§1 定积分概念 186
一、问题提出 186
二、定积分的定义 187
§2 牛顿—莱布尼茨公式 190
§3 可积条件 192
一、可积的必要条件 193
二、可积的充要条件 193
三、可积函数类 194
§4 定积分的性质 198
一、定积分的基本性质 198
二、积分中值定理 202
§5 微积分基本定理·定积分计算(续) 205
一、变限积分与原函数的存在性 205
二、换元积分法与分部积分法 209
三、泰勒公式的积分型余项 212
*§6 可积性理论补叙 215
一、上积与下积的性质 215
二、可积的充要条件 217

第十章 定积分的应用 222
§1 平面图形的面积 222
§2 由平行截面面积求体积 225
§3 平面曲线的弧长与曲率 229
一、平面曲线的弧长 229
二、曲率 233
§4 旋转曲面的面积 236
一、微元法 236
二、旋转曲面的面积 237
§5 定积分在物理中的某些应用 239
一、液体静压力 239
二、引力 240
三、功与平均功率 241
*§6 定积分的近似计算 243
一、梯形法 243
二、抛物线法 244

第十一章 反常积分 247
§1 反常积分概念 247
一、问题提出 247
二、两类反常积分的定义 248
§2 无穷积分的性质与敛散判别 252
一、无穷积分的性质 252
二、非负函数无穷积分的敛散判别法 253
三、一般无穷积分的敛散判别法 255
§3 瑕积分的性质与敛散判别 258

附录I 实数理论 263
一、建立实数的原则 263
二、分析 264
三、分划全体所成的有序集 266
四、R中的加法 267
五、R中的乘法 268
六、R作为Q的扩充 270
七、实数的无限小数表示 271
八、无限小数四则运算的定义 272

附录II 积分表 275
一、含有xᵃ的形式 275
二、含有a+bx的形式 275
三、含有ax²+x²,a>0的形式 276
四、含有ax+bx+cx²,b²≠4ac的形式 276
五、含有√a+bx的形式 276
六、含有√x²±a²,a>0的形式 277
七、含有√a²-x²,a>0的形式 277
八、含有sinx或cosx的形式 278
九、含有tanx,cotx,secx,cscx的形式 279
十、含有反三角函数的形式 279
十一、含有eˣ的形式 280
十二、含有lnx的形式 280

第十二章 数项级数 1
§1 级数的敛散性 1
§2 正项级数 6
一、正项级数敛散性的一般判别原则 7
二、比式判别法和根式判别法 8
三、积分判别法 12
四、拉贝判别法 14
§3 一般项级数 17
一、交错级数 17
二、绝对收敛级数及其性质 17
三、阿贝尔判别法和狄利克雷判别法 21

第十三章 函数列与函数项级数 25
§1 一致收敛性 25
一、函数列及其一致收敛性 25
二、函数项级数及其一致收敛性 29
三、函数项级数的一致收敛性判别法 31
§2 一致收敛函数列与函数项级数的性质 34

第十四章 幂级数 41
§1 幂级数 41
一、幂级数的收敛区间 41
二、幂级数的性质 44
三、幂级数的运算 46
§2 函数的幂级数展开 49
一、泰勒级数 49
二、初等函数的幂级数展开式 50
§3 复变量的指数函数·欧拉公式 57

第十五章 傅里叶级数 60
§1 傅里叶级数 60
一、三角级数·正交函数系 60
二、以2π为周期的函数的傅里叶级数 61
三、收敛定理 63
§2 以2l为周期的函数的展开式 69
一、以2l为周期的函数的傅里叶级数 69
二、傅函数与奇函数的傅里叶级数 70
§3 收敛定理的证明 75

第十六章 多元函数的极限与连续 82
§1 平面点集与多元函数 82
一、平面点集 82
二、R²上的完备性定理 85
三、二元函数 87
四、n元函数 88
§2 二元函数的极限 89
一、二元函数的极限 89
二、累次极限 92
§3 二元函数的连续性 95
一、二元函数的连续性概念 96
二、有界闭域上连续函数的性质 97

第十七章 多元函数微分学 102
§1 可微性 102
一、可微性与全微分 102
二、偏导数 103
三、可微性条件 105
四、可微性几何意义及应用 107
§2 复合函数微分法 112
一、复合函数的求导法则 112
二、复合函数的全微分 116
§3 方向导数与梯度 118
§4 泰勒公式与极值问题 121
一、高阶偏导数 121
二、中值定理和泰勒公式 125
三、极值问题 128

第十八章 隐函数定理及其应用 136
§1 隐函数 136
一、隐函数的概念 136
二、隐函数存在性条件的分析 137
三、隐函数定理 138
四、隐函数求导等例 141
§2 隐函数组 143
一、隐函数组的概念 143
二、隐函数组定理 144
三、反函数组与坐标变换 146
§3 几何应用 150
一、平面曲线的切线与法线 150
二、空间曲线的切线与法平面 151
三、曲面的切平面与法线 153
§4 条件极值 155

第十九章 含参量积分 163
§1 含参量正常积分 163
§2 含参量反常积分 169
一、一致收敛性及其判别法 169
二、含参量反常积分的性质 173
§3 欧拉积分 179
一、Γ函数 179
二、B函数 181
三、Γ函数与B函数之间的关系 182

第二十章 曲线积分 185
§1 第一型曲线积分 185
一、第一型曲线积分的定义 185
二、第一型曲线积分的计算 186
§2 第二型曲线积分 189
一、第二型曲线积分的定义 189
二、第二型曲线积分的计算 191
三、两类曲线积分的联系 195

第二十一章 重积分 198
§1 二重积分的概念 198
一、平面图形的面积 198
二、二重积分的定义及其存在性 200
三、二重积分的性质 202
§2 直角坐标系下二重积分的计算 204
§3 格林公式·曲线积分与路线的无关性 209
一、格林公式 209
二、曲线积分与路线的无关性 212
§4 二重积分的变量变换 217
一、二重积分的变量变换公式 217
二、用极坐标计算二重积分 220
§5 三重积分 226
一、三重积分的概念 226
二、化三重积分为累次积分 226
三、三重积分换元法 230
§6 重积分的应用 234
一、曲面的面积 235
二、质心 238
三、转动惯量 239
四、引力 241
§7 n重积分 242
§8 反常二重积分 247
一、无界区域上的二重积分 247
二、无界函数的二重积分 251
§9 在一般条件下重积分变量变换公式的证明 253

第二十二章 曲面积分 259
§1 第一型曲面积分 259
一、第一型曲面积分的概念 259
二、第一型曲面积分的计算 259
§2 第二型曲面积分 262
一、曲面的侧 262
二、第二型曲面积分的概念 263
三、第二型曲面积分的计算 265
四、两类曲面积分的联系 267
§3 高斯公式与斯托克斯公式 269
一、高斯公式 269
二、斯托克斯公式 271
§4 场论初步 276
一、场的概念 276
二、梯度场 276
三、散度场 278
四、旋度场 279
五、管量场与有势场 281

第二十三章 向量函数微分学 285
§1 n维欧氏空间与向量函数 285
一、n维欧氏空间 285
二、向量函数 287
三、向量函数的极限与连续 288
§2 向量函数的微分 291
一、可微性与可微条件 291
二、可微函数的性质 294
三、黑塞矩阵与极值 296
§3 反函数定理和隐函数定理 299
一、反函数定理 299
二、隐函数定理 302
三、拉格朗日乘数法 304



## 高等代数

第一章 线性方程组与矩阵 1
1.1 线性方程组 1
习题一 11
1.2 矩阵的运算 13
习题二 20
1.3 矩阵的分块 22
习题三 26
1.4 矩阵的秩 28
习题四 35

第二章 行列式 37
2.1 映射、置换 37
习题一 42
2.2 置换的结构与奇偶性 43
习题二 49
2.3 行列式的定义 50
习题三 52
2.4 行列式的性质 54
习题四 59
2.5 行列式按行(列)的展开 61
习题五 66
2.6 行列式的应用 69
习题六 72

第三章 群、环、域的定义和例子 74
3.1 等价关系 74
习题一 78
3.2 二元运算 79
习题二 83
3.3 群的定义 84
习题三 87
3.4 子群 88
习题四 92
3.5 环的定义 93
习题五 97
3.6 域的定义 98
习题六 100

第四章 多项式环 101
4.1 一元多项式环的定义 101
习题一 104
4.2 多项式的整除性 105
习题二 110
4.3 多项式的因式分解 112
习题三 116
4.4 多项式的根 117
习题四 120
4.5 复数域、实数域和有理数域上的多项式 121
习题五 128
4.6 多元多项式环 129
习题六 133
4.7 对称多项式 134
习题七 139

第五章 向量空间 140
5.1 向量空间的定义 140
习题一 144
5.2 向量的线性关系 146
习题二 152
5.3 基和维数 153
习题三 158
5.4 向量的坐标、基变换 159
习题四 164
5.5 向量空间的同构 165
习题五 166
5.6 向量空间理论的应用 167
习题六 173

第六章 线性变换 175
6.1 线性映射及其运算 175
习题一 181
6.2 线性变换的矩阵 182
习题二 186
6.3 不变子空间 189
习题三 192
6.4 特征值和特征向量 193
习题四 198
6.5 可对角化矩阵 200
习题五 205
6.6 凯利-哈密尔顿定理 207
习题六 210
6.7* 根子空间 211
习题七 215
6.8* 循环子空间 216
习题八 219
6.9 若尔当标准形 220
习题九 222

第七章 二次型 223
7.1 二次型 223
习题一 229
7.2 实二次型 230
习题二 233
7.3 双线性函数 235
习题三 240

第八章 欧氏空间 242
8.1 欧氏空间 242
习题一 248
8.2 规范正交基 249
习题二 254
8.3 正交变换 256
习题三 264
8.4 对称变换 265
习题四 268
8.5* 酉空间 269
习题五 273



## 概率论

第一章 绪论 1
1.1 随机现象及基本概念 1
1.1.1 必然现象与随机现象 1
1.1.2 样本空间 3
1.1.3 事件及运算 4
1.1.4 事件域 8
1.1.5 频率与概率 11
1.1.6 练习题 12
1.2 古典概型和几何概型 14
1.2.1 古典概型 14
1.2.2 计数原理 16
1.2.3 古典概型的例子 17
1.2.4 几何概型的定义与例子 20
1.2.5 练习题 24

第二章 概率空间 26
2.1 概率空间及简单性质 26
2.1.1 练习题 32
2.2 条件概率 33
2.2.1 条件概率的定义 33
2.2.2 乘法公式 34
2.2.3 全概率公式 35
2.2.4 Bayes公式 38
2.2.5 练习题 39
2.3 事件的独立性 40
2.3.1 两个事件的独立性 40
2.3.2 多个事件的独立性 42
2.3.3 独立性在概率计算中的应用 43
2.3.4 随机实验的独立性 46
2.3.5 练习题 48

第三章 随机变量与随机向量 50
3.1 随机变量及其分布 50
3.1.1 随机变量的定义与等价条件 50
3.1.2 分布与分布函数 53
3.1.3 随机变量的结构 56
3.1.4 离散型随机变量与连续型随机变量 58
3.1.5 练习题 61
3.2 Bernoulli实验及相关的离散型分布 63
3.2.1 二项分布 63
3.2.2 几何分布 66
3.2.3 负二项分布与Pascal分布 68
3.2.4 练习题 69
3.3 Poisson分布 70
3.3.1 Poisson粒子流及其分布 70
3.3.2 Poisson分布的性质 72
3.3.3 练习题 74
3.4 常用的连续型分布 76
3.4.1 均匀分布 76
3.4.2 正态分布 77
3.4.3 Γ-分布与指数分布 79
3.4.4 练习题 82
3.5 随机向量与联合分布 83
3.5.1 随机向量 83
3.5.2 联合分布 84
3.5.3 边缘分布 88
3.5.4 二元均匀分布与二元正态分布 90
3.5.5 练习题 92
3.6 随机变量的条件分布与独立性 94
3.6.1 条件分布 94
3.6.2 随机变量的独立性 96
3.6.3 母函数 99
3.6.4 练习题 102
3.7 随机变量函数的分布 104
3.7.1 离散型情形 104
3.7.2 连续型情形 105
3.7.3 统计量的分布 112
3.7.4 随机变量的存在性 115
3.7.5 随机数 117
3.7.6 练习题 118

第四章 数字特征与特征函数 120
4.1 数学期望 120
4.1.1 数学期望的定义 120
4.1.2 数学期望的性质 123
4.1.3 数学期望的计算 127
4.1.4 练习题 131
4.2 其他数字特征 134
4.2.1 方差 134
4.2.2 方差矩阵 137
4.2.3 相关系数 140
4.2.4 矩 143
4.2.5 练习题 144
4.3 条件数学期望与最优预测 146
4.3.1 条件数学期望及性质 146
4.3.2 条件数学期望的应用 149
4.3.3 练习题 152
4.4 特征函数 153
4.4.1 特征函数的定义与基本性质 153
4.4.2 反演公式与唯一性定理 160
4.4.3 联合特征函数 164
4.4.4 练习题 165
4.5 多元正态分布 167
4.5.1 密度函数与特征函数 167
4.5.2 多元正态分布的性质 168
4.5.3 练习题 175

第五章 大数定律和中心极限定理 177
5.1 随机变量的收敛性 177
5.1.1 几种不同的收敛性 177
5.1.2 特征函数与弱收敛 184
5.1.3 练习题 188
5.2 大数定律 190
5.2.1 大数定律的定义 190
5.2.2 独立同分布情形的大数定律 192
5.2.3 独立情形的强大数定律 195
5.2.4 大数定律与Monte Carlo方法 198
5.2.5 练习题 198
5.3 中心极限定理 200
5.3.1 中心极限定理的定义 200
5.3.2 独立同分布情形的中心极限定理 201
5.3.3 独立情形的中心极限定理 203
5.3.4 中心极限定理的应用 208
5.3.5 练习题 209

部分练习答案与提示 211
参考文献 224
索引 225



## 数理统计

第1章 概率与分布 1
1.1 引论 1
1.2 集合理论 2
1.3 概率集函数 8
1.4 条件概率与独立性 16
1.5 随机变量 24
1.6 离散随机变量 31
1.6.1 变量变换 32
1.7 连续随机变量 34
1.7.1 变量变换 36
1.8 随机变量的期望 40
1.9 某些特殊期望 45
1.10 重要不等式 52

第2章 多元分布 57
2.1 二元随机变量的分布 57
2.1.1 期望 61
2.2 二元随机变量变换 65
2.3 条件分布与期望 72
2.4 相关系数 78
2.5 独立随机变量 84
2.6 多元随机变量的推广 90
2.6.1 多元变量的方差-协方差矩阵
2.7 多个随机向量的变换 96
2.8 随机变量的线性组合 102

第3章 某些特殊分布 106
3.1 二项分布及有关分布 106
3.2 泊松分布 114
3.3 Γ、χ2 以及β分布 118
3.4 正态分布 127
3.4.1 污染正态分布 132
3.5 多元正态分布 135
3.5.1 应用 139
3.6 t分布与F分布 143
3.6.1 t分布 143
3.6.2 F分布 144
3.6.3 学生定理 146
3.7 混合分布 148

第4章 统计推断基础 154
4.1 抽样与统计量 154
4.1.1 pmf与pdf的直方图估计 157
4.2 置信区间 162
4.2.1 均值之差的置信区间 164
4.2.2 比例之差的置信区间 166
4.3 离散分布参数的置信区间 169
4.4 次序统计量 172
4.4.1 分位数 175
4.4.2 分位数置信区间 178
4.5 假设检验 181
4.6 统计检验的深入研究 188
4.7 卡方检验 192
4.8 蒙特卡罗方法 198
4.8.1 筛选生成算法 203
4.9 自助法 206
4.9.1 百分位数自助置信区间 206
4.9.2 自助检验法 209
4.10 分布容许限 215

第5章 一致性与极限分布 218
5.1 依概率收敛 218
5.2 依分布收敛 221
5.2.1 概率有界 226
5.2.2 Δ方法 227
5.2.3 矩母函数方法 228
5.3 中心极限定理 231
5.4 多变量分布的推广 236

第6章 极大似然法 241
6.1 极大似然估计 241
6.2 拉奥-克拉默下界与有效性 246
6.3 极大似然检验 256
6.4 多参数估计 263
6.5 多参数检验 270
6.6 EM算法 276

第7章 充分性 283
7.1 估计量品质的测量 283
7.2 参数的充分统计量 287
7.3 充分统计量的性质 293
7.4 完备性与唯一性 296
7.5 指数分布类 300
7.6 参数的函数 303
7.7 多参数的情况 308
7.8 最小充分性与从属统计量 313
7.9 充分性、完备性以及独立性 319

第8章 最优假设检验 324
8.1 最大功效检验 324
8.2 一致最大功效检验 332
8.3 似然比检验 338
8.4 序贯概率比检验 347
8.5 极小化极大与分类方法 352
8.5.1 极小化极大方法 353
8.5.2 分类 355

第9章 正态模型的推断 358
9.1 二次型 358
9.2 单向方差分析 362
9.3 非中心χ2分布与F分布 365
9.4 多重比较法 367
9.5 方差分析 371
9.6 回归问题 376
9.7 独立性检验 383
9.8 基些二次型分布 386
9.9 基些二次型的独立性 390

第10章 非参数与稳健统计学 396
10.1 位置模型 396
10.2 样本中位数与符号检验 398
10.2.1 渐近相对有效性 401
10.2.2 基于符号检验的估计方程 405
10.2.3 中位数置信区间 406
10.3 威尔科克森符号秩 407
10.3.1 渐近相对有效性 411
10.3.2 基于威尔科克森符号秩的估计方程 413
10.3.3 中位数的置信区间 414
10.4 曼-惠特尼-威尔科克森方法 415
10.4.1 渐近相对有效性 418
10.4.2 基于MWW的估计方程 420
10.4.3 移位参数Δ的置信区间 420
10.5 一般秩得分 421
10.5.1 效力 424
10.5.2 基于一般得分的估计方程 425
10.5.3 最优化最佳估计 426
10.6 适应方法 431
10.7 简单线性模型 435
10.8 测量关联性 439
10.8.1 肯德尔τ 439
10.8.2 斯皮尔曼ρ 442
10.9 稳健概念 445
10.9.1 位置模型 445
10.9.2 线性模型 450

第11章 贝叶斯统计学 457
11.1 主观概率 457
11.2 贝叶斯方法 460
11.2.1 先验分布与后验分布 460
11.2.2 贝叶斯点估计 462
11.2.3 贝叶斯区间估计 465
11.2.4 贝叶斯检验方法 466
11.2.5 贝叶斯序贯方法 467
11.3 贝叶斯其他术语与思想 468
11.4 吉布斯抽样器 473
11.5 现代贝叶斯方法 477
11.5.1 经验贝叶斯 480

附录A 数学 483
附录B R函数 486
附录C 分布表 495
附录D 常用分布列表 506
附录E 参考文献 509
附录F 部分习题答案 513



## 统计学习

第1章 导论 1
1.1 统计学习概述 1
1.2 统计学习简史 4
1.3 关于这本书 4
1.4 这本书适用的读者群 6
1.5 记号与简单的矩阵代数 6
1.6 本书的内容安排 8
1.7 用于实验和习题的数据集 9
1.8 本书网站 10
1.9 致谢 10

第2章 统计学习 11
2.1 什么是统计学习 11
2.2 评价模型精度 21
2.3 实验：R语言简介 30
2.4 习题 37

第3章 线性回归 41
3.1 简单线性回归 42
3.2 多元线性回归 49
3.3 回归模型中的其他注意事项 57
3.4 营销计划 70
3.5 线性回归与k最近邻法的比较 72
3.6 实验：线性回归 75
3.7 习题 84

第4章 分类 89
4.1 分类问题概述 89
4.2 为什么线性回归不可用 90
4.3 逻辑斯谛回归 91
4.4 线性判别分析 96
4.5 分类方法的比较 105
4.6 R实验：逻辑斯谛回归、LDA、QDA和KNN 107
4.7 习题 117

第5章 重抽样方法 121
5.1 交叉验证法 121
5.2 自助法 129
5.3 实验：交叉验证法和自助法 131
5.4 习题 136

第6章 线性模型选择与正则化 140
6.1 子集选择 141
6.2 压缩估计方法 148
6.3 降维方法 157
6.4 高维问题 163
6.5 实验1：子集选择方法 167
6.6 实验2：岭回归和lasso 173
6.7 实验3：PCR和PLS回归 177
6.8 习题 180

第7章 非线性模型 184
7.1 多项式回归 185
7.2 阶梯函数 186
7.3 基函数 187
7.4 回归样条 188
7.5 光滑样条 192
7.6 局部回归 194
7.7 广义可加模型 196
7.8 实验：非线性建模 200
7.9 习题 207

第8章 基于树的方法 211
8.1 决策树基本原理 211
8.2 装袋法、随机森林和提升法 219
8.3 实验：决策树 225
8.4 习题 231

第9章 支持向量机 234
9.1 最大间隔分类器 234
9.2 支持向量分类器 238
9.3 狭义的支持向量机 241
9.4 多分类的SVM 246
9.5 与逻辑斯谛回归的关系 247
9.6 实验：支持向量机 248
9.7 习题 256

第10章 无指导学习 259
10.1 无指导学习的挑战 259
10.2 主成分分析 260
10.3 聚类分析方法 267
10.4 实验1：主成分分析 277
10.5 实验2：聚类分析 280
10.6 实验3：以NCI60数据为例 282
10.7 习题 287



## 抽样调查

第1章 绪论 1
1.1 统计信息与抽样调查 1
1.2 基本概念 6
1.3 几种基本的抽样方法 11
1.4 抽样调查程序 13
小结 16
习题 16

第2章 简单随机抽样 18
2.1 引言 18
2.2 估计量 22
2.3 样本量的确定 27
2.4 其他问题 32
小结 34
本章附录 简单随机抽样简单估计量性质的证明 34
习题 37

第3章 分层随机抽样 40
3.1 引言 40
3.2 估计量 43
3.3 样本量在各层的分配 47
3.4 样本量的确定 50
3.5 分层时的若干问题 55
小结 60
本章附录 分层抽样估计量性质的证明 60
习题 63

第4章 比率、回归与差值估计 67
4.1 引言 67
4.2 比率估计 69
4.3 回归估计 79
4.4 差值估计 86
小结 87
本章附录 比率估计量、回归估计量性质的证明 88
习题 92

第5章 不等概抽样 95
5.1 引言 95
5.2 放回不等概抽样 99
5.3 不放回不等概抽样 103
小结 111
本章附录 不等概抽样估计量性质的证明 111
习题 112

第6章 整群抽样 114
6.1 引言 114
6.2 群规模相等时的估计 116
6.3 群规模不等时的估计 122
6.4 总体比例的估计 131
小结 134
本章附录 整群抽样估计量性质的证明 134
习题 136

第7章 系统抽样 141
7.1 引言 141
7.2 等概率系统抽样估计量 147
7.3 不同特征总体的系统抽样 152
7.4 系统抽样的方差估计 158
小结 162
本章附录 不同特征总体系统抽样的性质证明 163
习题 165

第8章 多阶段抽样 168
8.1 引言 168
8.2 初级单元大小相等的二阶抽样 171
8.3 初级单元大小不等的二阶抽样 177
8.4 其他问题 183
小结 190
本章附录 多阶段抽样估计量性质的证明 190
习题 193

第9章 二重抽样 195
9.1 引言 195
9.2 为分层的二重抽样 198
9.3 为比率估计的二重抽样 202
9.4 为回归估计的二重抽样 206
小结 208
本章附录 二重抽样公式的证明 208
习题 210

第10章 复杂样本的方差估计 213
10.1 引言 213
10.2 随机组方法 216
10.3 平衡半样本方法 226
10.4 刀切法 236
10.5 泰勒级数法 242
10.6 方法的比较 244
小结 245
本章附录 复杂样本的方差估计的性质证明 245
习题 247

第11章 调查中的非抽样误差 249
11.1 引言 249
11.2 抽样框误差 251
11.3 无回答误差 257
11.4 计量误差 266
11.5 离群值的检测和处理 272
小结 275
习题 275

第12章 设计与方法——美国CPS案例 277
12.1 概述 277
12.2 CPS抽样设计 282
12.3 CPS目标量估计 286
12.4 CPS的方差估计 290
12.5 非抽样误差及控制 296

附录1 方差估计软件的介绍与比较 302
附录2 PC CARP软件的基本用法 308
附录3 随机数表 338
习题参考答案 347
参考文献 353



## 应用时间序列分析

第1章 时间序列分析简介 1
1.1 引言 1
1.2 时间序列的定义 1
1.3 时间序列分析方法 2
    1.3.1 描述性时序分析 2
    1.3.2 统计时序分析 4
1.4 时间序列分析软件 6
1.5 习题 7
1.6 上机指导 7
    1.6.1 SAS操作界面 7
    1.6.2 创建时间序列SAS数据集 8
    1.6.3 时间序列数据集的处理 13

第2章 时间序列的预处理 17
2.1 平稳性检验 17
    2.1.1 特征统计量 17
    2.1.2 平稳时间序列的定义 19
    2.1.3 平稳时间序列的统计性质 20
    2.1.4 平稳时间序列的意义 21
    2.1.5 平稳性的检验 23
2.2 纯随机性检验 27
    2.2.1 纯随机序列的定义 27
    2.2.2 白噪声序列的性质 28
    2.2.3 纯随机性检验 29
2.3 习题 33
2.4 上机指导 35
    2.4.1 绘制时序图 35
    2.4.2 平稳性与纯随机性检验 37

第3章 平稳时间序列分析 40
3.1 方法性工具 40
    3.1.1 差分运算 40
    3.1.2 延迟算子 41
    3.1.3 线性差分方程 41
3.2 ARMA模型的性质 43
    3.2.1 AR模型 43
    3.2.2 MA模型 56
    3.2.3 ARMA模型 62
3.3 平稳序列建模 65
    3.3.1 建模步骤 65
    3.3.2 样本自相关系数与偏自相关系数 66
    3.3.3 模型识别 66
    3.3.4 参数估计 72
    3.3.5 模型检验 77
    3.3.6 模型优化 79
3.4 序列预测 84
    3.4.1 线性预测函数 84
    3.4.2 预测方差最小原则 85
    3.4.3 线性最小方差预测的性质 86
    3.4.4 修正预测 91
3.5 习题 93
3.6 上机指导 96
    3.6.1 模型识别 97
    3.6.2 参数估计 99
    3.6.3 序列预测 102

第4章 非平稳序列的随机分析 103
4.1 时间序列的分解 103
    4.1.1 Wold分解定理 103
    4.1.2 Cramer分解定理 104
4.2 差分运算 105
    4.2.1 差分运算的定质 105
    4.2.2 差分方式的选择 106
    4.2.3 过差分 109
4.3 ARIMA模型 110
    4.3.1 ARIMA模型的结构 110
    4.3.2 ARIMA模型的性质 111
    4.3.3 ARIMA模型建模 113
    4.3.4 ARIMA模型预测 116
    4.3.5 殡系数模型 118
    4.3.6 季节模型 121
4.4 残差自回归模型 129
    4.4.1 模型结构 129
    4.4.2 残差自相关检验 131
    4.4.3 模型拟合 134
4.5 异方差的性质 136
    4.5.1 异方差的影响 136
    4.5.2 异方差的直观诊断 137
4.6 方差齐性变换 139
4.7 条件异方差模型 142
    4.7.1 ARCH模型 142
    4.7.2 GARCH模型 148
    4.7.3 GARCH的衍生模型 152
4.8 习题 154
4.9 上机指导 158
    4.9.1 拟合ARIMA模型 158
    4.9.2 拟合Auto-Regressive模型 161
    4.9.3 拟合GARCH模型 167

第5章 非平稳序列的确定性分析 173
5.1 确定性因素分解 173
5.2 X-11季节调整模型 175
    5.2.1 移动平均方法 175
    5.2.2 X-11季节调整模型的计算过程 188
5.3 X-12-ARIMA模型 191
5.4 指数平滑预测模型 196
    5.4.1 简单指数平滑 196
    5.4.2 Holt两参数指数平滑 199
    5.4.3 Holt-Winters三参数指数平滑 200
5.5 习题 201
5.6 上机指导 204
    5.6.1 X-11过程 204
    5.6.2 X-12过程 206
    5.6.3 Forecast过程 210

第6章 多元时间序列分析 215
6.1 平稳多元序列建模 215
6.2 虚假回归 218
6.3 单位根检验 220
    6.3.1 DF检验 220
    6.3.2 ADF检验 227
    6.3.3 PP检验 230
6.4 协整 233
    6.4.1 单整与协整 233
    6.4.2 协整检验 235
6.5 误差修正模型 237
6.6 习题 238
6.7 上机指导 241



## 线性模型引论

第一章 模型概论 1
§1.1 线性回归模型 1
§1.2 方差分析模型 7
§1.3 协方差分析模型 11
§1.4 混合效应模型 12
习题一 15

第二章 矩阵论的预备知识 17
§2.1 线性空间 17
§2.2 广义逆矩阵 20
§2.3 幂等方阵 28
§2.4 特征值的极值性质与不等式 33
§2.5 偏序 37
§2.6 Kronecker乘积与向量化运算 41
§2.7 矩阵微商 43
习题二 51

第三章 多元正态分布 55
§3.1 均值向量与协方差阵 55
§3.2 随机向量的二次型 56
§3.3 正态随机向量 60
§3.4 正态变量的二次型 68
§3.5 正态变量的二次型与线性型的独立性 73
习题三 76

第四章 参数估计 78
§4.1 最小二乘估计 78
§4.2 约束最小二乘估计 85
§4.3 广义最小二乘估计 88
§4.4 最小二乘统一理论 92
§4.5 LS估计的稳健性 99
§4.6 两步估计 103
§4.7 协方差改进法 108
§4.8 多元线性模型 111
习题四 118

第五章 假设检验及其它 121
§5.1 线性假设的检验 121
§5.2 置信椭球和同时置信区间 129
§5.3 预测 132
§5.4 最优设计 139
习题五 144

第六章 线性回归模型 147
§6.1 最小二乘估计 147
§6.2 回归方程和系数的检验 150
§6.3 回归自变量的选择 155
§6.4 回归诊断 164
§6.5 Box-Cox变换 175
§6.6 均方误差及复杂线性 178
§6.7 有偏估计 183
习题六 194

第七章 方差分析模型 198
§7.1 单向分类模型 198
§7.2 两向分类模型(无交互效应) 208
§7.3 两向分类模型(交互效应存在) 216
§7.4 套分类模型 225
§7.5 误差方差齐性及正态性检验 232
习题七 238

第八章 协方差分析模型 241
§8.1 一般分块线性模型 241
§8.2 参数估计 245
§8.3 假设检验 247
§8.4 计算方法 250
习题八 254

第九章 混合效应模型 256
§9.1 固定效应的估计 256
§9.2 随机效应的预测 259
§9.3 混合模型方程 260
§9.4 方差分析估计 262
§9.5 极大似然估计 268
§9.6 限制极大似然估计 273
§9.7 最小范数二次无偏估计 277
§9.8 方差分量的检验 283
习题九 285



## 应用随机过程

第1章 预备知识 1
1.1 概率空间 1
1.2 随机变量与分布函数 3
1.3 数字特征、矩母函数与特征函数 7
1.4 收敛性 13
1.5 独立性与条件期望 16

第2章 随机过程的基本概念和基本类型 22
2.1 基本概念 22
2.2 有限维分布与 Kolmogorov 定理 23
2.3 随机过程的基本类型 25
习题 32

第3章 Poisson 过程 33
3.1 Poisson 过程 33
3.2 与Poisson 过程相联系的若干分布 38
3.3 Poisson 过程的推广 43
习题 50

第4章 更新过程 52
4.1 更新过程的定义及若干分布 52
4.2 更新方程及其应用 55
4.3 更新定理 60
4.4 更新过程的推广 68
习题 72

第5章 Markov 链 73
5.1 基本概念 73
5.2 状态的分类及性质 82
5.3 极限定理及平稳分布 88
5.4 Markov 链的应用 97
5.5 连续时间 Markov 链 102
习题 111

第6章 鞅 113
6.1 基本概念 113
6.2 鞅的停时定理及其应用 118
6.3 一致可积性 128
6.4 鞅收敛定理 129
6.5 连续鞅 132
习题 134

第7章 Brown 运动 136
7.1 基本概念与性质 136
7.2 Gauss 过程 140
7.3 Brown 运动的鞅性质 142
7.4 Brown 运动的 Markov 性 143
7.5 Brown 运动的最大值变量及反正弦律 144
7.6 Brown 运动的几种变化 148
7.7 高维 Brown 运动 152
习题 154

第8章 随机积分 155
8.1 关于随机游动的积分 155
8.2 关于 Brown 运动的积分 156
8.3 Itô 积分过程 160
8.4 Itô 公式 164
8.5 随机微分方程 168
习题 170

第9章 随机过程在金融中的应用 172
9.1 金融市场的术语与基本假定 172
9.2 Black-Scholes 模型 174
习题 184

第10章 随机过程在保险精算中的应用 185
10.1 基本概念 185
10.2 经典破产理论介绍 186
习题 201

第11章 Markov链 Monte Carlo方法 202
11.1 计算积分的 Monte Carlo 方法 202
11.2 Markov 链 Monte Carlo 方法简介 206
11.3 Metropolis-Hastings 算法 209
11.4 Gibbs 抽样 210
11.5 贝叶斯 MCMC 估计方法 213
习题 218



## 应用多元统计分析

第1章 多元正态分布 1
1.1 多元分布的基本概念 1
1.2 统计距离 5
1.3 多元正态分布 8
1.4 均值向量和协方差阵的估计 13
1.5 常用分布及抽样分布 15

第2章 均值向量和协方差阵的检验 21
2.1 均值向量的检验 21
2.2 协方差阵的检验 27
2.3 有关检验的上机实现 28

第3章 聚类分析 36
3.1 聚类分析的基本思想 37
3.2 相似性度量 39
3.3 类和类的特征 44
3.4 系统聚类法 47
3.5 K-均值聚类和有序样品的聚类 56
3.6 模糊聚类分析 59
3.7 计算步骤与上机实现 61
3.8 社会经济案例研究 72

第4章 判别分析 82
4.1 判别分析的基本思想 82
4.2 距离判别 83
4.3 贝叶斯判别 86
4.4 费歇判别 86
4.5 逐步判别 88
4.6 判别分析应用的几个例子 89

第5章 主成分分析 106
5.1 主成分分析的基本原理 106
5.2 总体主成分及其性质 110
5.3 样本主成分的导出 115
5.4 有关问题的讨论 116
5.5 主成分分析步骤及框图 119
5.6 主成分分析的上机实现 120

第6章 因子分析 134
6.1 因子分析的基本理论 134
6.2 因子载荷的求解 138
6.3 因子分析的步骤与逻辑框图 143
6.4 因子分析的上机实现 144

第7章 对应分析 161
7.1 列联表及列联表分析 161
7.2 对应分析的基本理论 164
7.3 对应分析的步骤及逻辑框图 170
7.4 对应分析的上机实现 171

第8章 典型相关分析 187
8.1 典型相关分析的基本理论及方法 187
8.2 典型相关分析的步骤及逻辑框图 194
8.3 典型相关分析的上机实现 198
8.4 社会经济案例研究 202

第9章 定性数据的建模分析 210
9.1 对数线性模型的基本理论和方法 211
9.2 对数线性模型的上机实现 212
9.3 Logistic 回归的基本理论和方法 216
9.4 Logistic 回归的方法及步骤 224