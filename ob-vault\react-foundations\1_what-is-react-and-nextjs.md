Title: React Foundations: About React and Next.js | Next.js

URL Source: https://nextjs.org/learn/react-foundations/what-is-react-and-nextjs

Next.js is a flexible **React framework** that gives you building blocks to create fast, full-stack **web applications**.

But what exactly do we mean by this? Let's spend some time expanding on what React and Next.js are and how they can help you build web applications.

### [Building blocks of a web application](https://nextjs.org/learn/react-foundations/what-is-react-and-nextjs#building-blocks-of-a-web-application)

There are a few things you need to consider when building modern applications. Such as:

*   **User Interface** - how users will consume and interact with your application.
*   **Routing** - how users navigate between different parts of your application.
*   **Data Fetching** - where your data lives and how to get it.
*   **Rendering** - when and where you render static or dynamic content.
*   **Integrations** - what third-party services you use (for CMS, auth, payments, etc.) and how you connect to them.
*   **Infrastructure** - where you deploy, store, and run your application code (serverless, CDN, edge, etc.).
*   **Performance** - how to optimize your application for end-users.
*   **Scalability** - how your application adapts as your team, data, and traffic grow.
*   **Developer Experience** - your team's experience building and maintaining your application.

For each part of your application, you will need to decide whether you will build a solution yourself or use other tools, such as packages, libraries, and frameworks.

### [What is React?](https://nextjs.org/learn/react-foundations/what-is-react-and-nextjs#what-is-react)

[React](https://react.dev/) is a JavaScript **library** for building **interactive user interfaces**.

By user interfaces (UI), we mean the elements that users see and interact with on-screen.

![Image 1: User Interface example showing a browser window with a navigation, a sidebar, and a list of posts](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Flearn-react-components.png&w=3840&q=75)![Image 2: User Interface example showing a browser window with a navigation, a sidebar, and a list of posts](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Flearn-react-components.png&w=3840&q=75)
By library, we mean React provides helpful functions (APIs) to build UI, but leaves it up to the developer where to use those functions in their application.

Part of React's success is that it is relatively unopinionated about the other aspects of building applications. This has resulted in a flourishing ecosystem of third-party tools and solutions, including Next.js.

It also means, however, that building a complete React application from the ground up requires some effort. Developers need to spend time configuring tools and reinventing solutions for common application requirements.

[What is Next.js?](https://nextjs.org/learn/react-foundations/what-is-react-and-nextjs#what-is-nextjs)
------------------------------------------------------------------------------------------------------

Next.js is a React **framework** that gives you building blocks to create web applications.

By framework, we mean Next.js handles the tooling and configuration needed for React, and provides additional structure, features, and optimizations for your application.

![Image 3: Diagram showing how Next.js spans the server and client, and provides additional features such as routing, data fetching, and rendering.](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Flearn-ecosystem.png&w=3840&q=75)![Image 4: Diagram showing how Next.js spans the server and client, and provides additional features such as routing, data fetching, and rendering.](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Flearn-ecosystem.png&w=3840&q=75)
You can use React to build your UI, then incrementally adopt Next.js features to solve common application requirements such as routing, data fetching, and caching - all while improving the developer and end-user experience.

Whether you're an individual developer or part of a larger team, you can use React and Next.js to build fully interactive, highly dynamic, and performant web applications.

In the next chapters, we will discuss how you can get started with React and Next.js.