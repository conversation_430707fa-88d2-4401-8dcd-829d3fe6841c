## 第四章 更新过程

作者 郭旭

统计学院

北京师范大学

## 更新过程

1 更新过程定义及若干分布

2 更新方程及其应用 3 更新定理 4 更新过程的推广

## 更新过程的定义

## 定义

设 $\left\{  {{X}_{n},n = 1,2,\cdots }\right\}$ 是一串独立同分布的非负随机变量,分布函数为 $F\left( x\right)$ (为了避免平凡的情况,设 $F\left( 0\right)  = P\left( {{X}_{n} = }\right.$ $0) \neq  1$ ,记 $\mu  = E{X}_{n} = {\int }_{0}^{\infty }{xdF}\left( x\right)$ ,则 $0 < \mu  \leq  \infty )$ 。令 ${T}_{n} =$ $\mathop{\sum }\limits_{{i = 1}}^{n}{X}_{i},n \geq  1,{T}_{0} = 0$ 。我们把由

$$N\left( t\right)  = \sup \left\{  {n : {T}_{n} \leq  t}\right\}$$

定义的计数过程称为更新过程。

注：在更新过程中我们将事件发生一次叫做一次更新, ${X}_{n}$ 就是第 $n - 1$ 次和第 $n$ 次更新相距的时间, ${T}_{n}$ 是第 $n$ 次更新发生的时刻,而 $N\left( t\right)$ 就是 $t$ 时刻之前发生的总的更新次数。

## 几个记号

- ${X}_{i}$ 表示更新的时间间隔;

- ${T}_{n}$ 表示第 $n$ 次更新发生的时刻;

- $N\left( t\right)$ 表示 $\left\lbrack  {0,\mathrm{t}}\right\rbrack$ 内更新的次数。其等于第 $n$ 次更新在 $\left\lbrack  {0,\mathrm{t}}\right\rbrack$ 内发生的最大的 $n$ 的值。所以有 $N\left( t\right)  = \sup \left\{  {n : {T}_{n} \leq  t}\right\}$ 。 - 由于时间间隔是独立同分布的, 所以在各个更新时刻此过程在概率意义上重新开始。

由 $N\left( t\right)$ 的定义,我们有：

$${T}_{N\left( t\right) } \leq  t < {T}_{N\left( t\right)  + 1}.$$

${T}_{N\left( t\right) }$ : $\left\lbrack  {0,\mathrm{t}}\right\rbrack$ 内最后一个事件发生的时刻;

${T}_{N\left( t\right)  + 1}$ : $\left\lbrack  {0,\mathrm{t}}\right\rbrack$ 后第一个事件发生的时刻;

## 几个记号

对于更新过程,有 $N\left( t\right)  \geq  n \Leftrightarrow  {T}_{n} \leq  t$ ,但 $N\left( t\right)  > n$ 不等价于 ${T}_{n} < t$ 。需要注意对于更新过程,更新的时间间隔 ${X}_{i}$ 可以取值为 0 。所以即使 $N\left( t\right)  > n$ ,仍可以有 ${T}_{n} = {T}_{N\left( t\right) } = t$ 。实际上,此时只有 ${T}_{n} \leq  {T}_{N\left( t\right) } \leq  t$ 。

当 ${T}_{n} < t$ ,表明第 $n$ 次更新发生在 $(0,\mathrm{t}\rbrack$ 以内,而若在 $\left( {{T}_{n},t}\right\rbrack$ 时间内无更新,则 $N\left( t\right)  = n$ 。即 ${T}_{n} < t$ 推不出 $N\left( t\right)  > n$ 。根据逆反命题,可知 $N\left( t\right)  \leq  n$ 推不出 ${T}_{n} \geq  t$ 。

$N\left( t\right)$ 的分布

## $N\left( t\right)  < \infty$ 的概率为 1

事实上,由强大数定律知道

$$\frac{\mathop{\sum }\limits_{{i = 1}}^{n}{X}_{i}}{n} = \frac{{T}_{n}}{n} \rightarrow  \mu$$

以概率 1 成立。而 $\mu  > 0$ ,所以当 $n \rightarrow  \infty$ 时, ${T}_{n} \rightarrow  \infty$ ,也就是说无穷多次更新只可能在无限长的时间内发生。于是有限时间内最多只能发生有限次更新。

## $N\left( t\right)$ 的分布

我们有

$$\frac{N\left( t\right) }{t} \rightarrow  \frac{1}{\mu }$$

实际上：

$$\frac{{T}_{N\left( t\right) }}{N\left( t\right) } \leq  \frac{t}{N\left( t\right) } < \frac{{T}_{N\left( t\right)  + 1}}{N\left( t\right) } = \frac{{T}_{N\left( t\right)  + 1}}{N\left( t\right)  + 1}\frac{N\left( t\right)  + 1}{N\left( t\right) }.$$

根据大数定律:

$$\frac{{T}_{N\left( t\right) }}{N\left( t\right) } \rightarrow  \mu ,\;\frac{{T}_{N\left( t\right)  + 1}}{N\left( t\right)  + 1} \rightarrow  \mu .$$

长时间后,单位时间内更新发生的次数为 $1/\mu$ .

## $N\left( t\right)$ 的分布

$$N\left( t\right)  \geq  n \Leftrightarrow  {T}_{n} \leq  t$$

所以,

$$P\left( {N\left( t\right)  = n}\right)  = P\left( {N\left( t\right)  \geq  n}\right)  - P\left( {N\left( t\right)  \geq  n + 1}\right)$$

$$= P\left( {{T}_{n} \leq  t}\right)  - P\left( {{T}_{n + 1} \leq  t}\right)$$

$$= P\left( {\mathop{\sum }\limits_{{i = 1}}^{n}{X}_{i} \leq  t}\right)  - P\left( {\mathop{\sum }\limits_{{i = 1}}^{{n + 1}}{X}_{i} \leq  t}\right)$$

以 ${F}_{n}$ 记 ${T}_{n}$ 的分布,则 ${F}_{n}$ 是 $F$ 的 $n$ 重卷积,因此

$$P\left\lbrack  {N\left( t\right)  = n}\right\rbrack   = {F}_{n}\left( t\right)  - {F}_{n + 1}\left( t\right)$$

## $E\left\lbrack  {N\left( t\right) }\right\rbrack$ : 更新函数

定理

$$M\left( t\right)  = \mathop{\sum }\limits_{{n = 1}}^{\infty }{F}_{n}\left( t\right) .$$

其中: 以 $M\left( t\right)$ 记 $E\left\lbrack  {N\left( t\right) }\right\rbrack$ 并称之为更新函数证明：由定义可得

$$M\left( t\right)  = E\left\lbrack  {N\left( t\right) }\right\rbrack$$

$$= \mathop{\sum }\limits_{{n = 1}}^{\infty }{nP}\left( {N\left( t\right)  = n}\right)$$

$$\begin{matrix}  &  = & \mathop{\sum }\limits_{{n = 1}}^{\infty }n\left\lbrack  {P\left( {N\left( t\right)  \geq  n}\right)  - P\left( {N\left( t\right)  \geq  n + 1}\right) }\right\rbrack   \end{matrix}$$

$$= \mathop{\sum }\limits_{{n = 1}}^{\infty }P\left( {N\left( t\right)  \geq  n}\right)$$

$$= \mathop{\sum }\limits_{{n = 1}}^{\infty }P\left( {{T}_{n} \leq  t}\right)$$

$$= \mathop{\sum }\limits_{{n = 1}}^{\infty }{F}_{n}\left( t\right) \text{.}$$

## 更新函数的有限性

## 定理

$M\left( t\right)$ 是 $t$ 的不减函数,且对 $0 \leq  t <  + \infty$ , $M\left( t\right)  <  + \infty$ 。

证明: 由于 $N\left( t\right)$ 是关于 $t$ 不减的,故 $M\left( t\right)$ 也是不减的,下面证明 $M\left( t\right)$ 的有限性。

注意到事件 $\mathop{\sum }\limits_{{i = 1}}^{n}{X}_{i} \leq  t$ 可以推出对所有 $1 \leq  i \leq  n,{X}_{i} \leq  t$ 。

从而

$${F}_{n}\left( t\right)  = P\left( {\mathop{\sum }\limits_{{i = 1}}^{n}{X}_{i} \leq  t}\right)  \leq  \mathop{\prod }\limits_{{i = 1}}^{n}P\left( {{X}_{i} \leq  t}\right)  = F{\left( t\right) }^{n}.$$

进而可得:

$$E\left\lbrack  {N\left( t\right) }\right\rbrack   = \mathop{\sum }\limits_{{n = 1}}^{\infty }{F}_{n}\left( t\right)  \leq  \mathop{\sum }\limits_{{n = 1}}^{\infty }{F}^{n}\left( t\right)  = \frac{F\left( t\right) }{1 - F\left( t\right) } < \infty .$$

## 更新函数的有限性

因 $\Pr \left( {{X}_{n} = 0}\right)  < 1$ ,则由概率的连续性可知存在一个 $a > 0$ , 使得 $\Pr \left( {{X}_{n} \geq  a}\right)  > 0$ 。定义一个关联的更新过程 $\left\{  {{X}_{n}^{ * },n \geq  }\right.$ 1\}:

$${X}_{n}^{ * } = \left\{  \begin{array}{l} 0;\text{ if }{X}_{n} < a; \\  a;\text{ if }{X}_{n} \geq  a. \end{array}\right.$$

且令 ${N}^{ * }\left( t\right)  = \sup \left\{  {n : {X}_{1}^{ * } + \cdots  + {X}_{n}^{ * } \leq  t}\right\}$ 。易知,此过程更新只发生在时刻 $t = {na},n = 0,\cdots$ ,且在这些时刻的更新次数是独立的几何随机变量-1,其均值为

$$\frac{1}{P\left( {{X}_{n} \geq  a}\right) } - 1$$

于是

$$E\left\lbrack  {{N}^{ * }\left( t\right) }\right\rbrack   \leq  \frac{t/a + 1}{P\left( {{X}_{n} \geq  a}\right) } < \infty .$$

另一方面 ${X}_{i}^{ * } \leq  {X}_{i}$ ,所以 ${N}^{ * }\left( t\right)  \geq  N\left( t\right)$ 。

## 例4.1

## 例

考虑一个时间离散的更新过程 $\left\{  {{N}_{j},j = 1,2,\cdots }\right\}$ ,在每个时刻独立地做Bernoulli试验, 设成功的概率为 $p$ , 失败的概率为 $q = 1 - p$ . 以试验成功作为事件 (更新) 求此过程的更新函数 $M\left( k\right)$ 。

解 首先, 易知更新的时间间隔为独立的同几何分布

$$P\left( {{X}_{i} = n}\right)  = {q}^{n - 1}p,\;i = 1,2,\cdots ,\;n = 1,2,\cdots$$

则第 $r$ 次成功(更新)发生的时刻 ${T}_{r} = \mathop{\sum }\limits_{{i = 1}}^{r}{X}_{i}$ ,具有负二项分布

$$P\left( {{T}_{r} = n}\right)  = \left( \begin{matrix} n - 1 \\  r - 1 \end{matrix}\right) {q}^{n - r}{p}^{r}$$

## 例4.1

由此,有

$$P\left( {{N}_{m} = r}\right)$$

$$= P\left( {{T}_{r} \leq  m}\right)  - P\left( {{T}_{r + 1} \leq  m}\right)$$

$$= \mathop{\sum }\limits_{{n = r}}^{m}\left( \begin{matrix} n - 1 \\  r - 1 \end{matrix}\right) {q}^{n - r}{p}^{r} - \mathop{\sum }\limits_{{n = r + 1}}^{m}\left( \begin{matrix} n - 1 \\  r - 1 \end{matrix}\right) {q}^{n - r - 1}{p}^{r + 1}$$

所以,更新函数

$$M\left( k\right)  = \mathop{\sum }\limits_{{r = 0}}^{k}{rP}\left( {{N}_{k} = r}\right) .$$

## 更新密度

定义

在 $M\left( t\right)$ 导数存在的条件下,其导数 ${M}^{\prime }\left( t\right)$ 称为更新密度,记为 $m\left( t\right)$ 。由 $M\left( t\right)  = \mathop{\sum }\limits_{{n = 1}}^{\infty }{F}_{n}\left( t\right)$ 两边求导得

$$m\left( t\right)  = \mathop{\sum }\limits_{{n = 1}}^{\infty }{f}_{n}\left( t\right)$$

其中 ${f}_{n}\left( t\right)$ 是 ${F}_{n}\left( t\right)$ 的密度函数。

## $M\left( t\right)$ 和 $m\left( t\right)$ 的性质定理

## 定理

$M\left( t\right)$ 和 $m\left( t\right)$ 分别满足积分方程

$$M\left( t\right)  = F\left( t\right)  + {\int }_{0}^{t}M\left( {t - s}\right) {dF}\left( s\right)$$

$$m\left( t\right)  = f\left( t\right)  + {\int }_{0}^{t}m\left( {t - s}\right) f\left( s\right) {ds}$$

其中 $f\left( t\right)  = {F}^{\prime }\left( t\right)$ 。

## $M\left( t\right)$ 和 $m\left( t\right)$ 的性质定理

证明: 首先证明第一式, 由定义可得

$$M\left( t\right)  = \mathop{\sum }\limits_{{n = 1}}^{\infty }{F}_{n}\left( t\right)  = F\left( t\right)  + \mathop{\sum }\limits_{{n = 2}}^{\infty }{F}_{n}\left( t\right)$$

$$= F\left( t\right)  + \mathop{\sum }\limits_{{n = 2}}^{\infty }{F}_{n - 1}\left( t\right)  * F\left( t\right)$$

$$= F\left( t\right)  + \left( {\mathop{\sum }\limits_{{n = 1}}^{\infty }{F}_{n}\left( t\right) }\right)  * F\left( t\right)$$

$$= F\left( t\right)  + M * F\left( t\right)$$

由卷积定义知 $M * F\left( t\right)  = {\int }_{0}^{t}M\left( {t - s}\right) {dF}\left( s\right)$ ,从而第一式得证。第二式由第一式两边取导数可得。

## $M\left( t\right)$ 和 $m\left( t\right)$ 的性质定理

对首次更新时间取条件,可得

$$M\left( t\right)  = E\left\lbrack  {N\left( t\right) }\right\rbrack   = {\int }_{0}^{\infty }E\left\lbrack  {N\left( t\right)  \mid  {X}_{1} = x}\right\rbrack  {dF}\left( x\right) .$$

由于更新过程概率性地在一次更新发生后重新开始,可得：

$$E\left\lbrack  {N\left( t\right)  \mid  {X}_{1} = x}\right\rbrack   = \left\{  \begin{matrix} 1 + E\left\lbrack  {N\left( {t - x}\right) }\right\rbrack  ;\text{ if }x < t; \\  0;\text{ if }x > t \end{matrix}\right.$$

从而有

$$M\left( t\right)  = E\left\lbrack  {N\left( t\right) }\right\rbrack   = {\int }_{0}^{t}1 + E\left\lbrack  {N\left( {t - x}\right) }\right\rbrack  {dF}\left( x\right)$$

$$= F\left( t\right)  + {\int }_{0}^{t}M\left( {t - x}\right) {dF}\left( x\right) .$$

## 求解 $M\left( t\right)$

例题：若到达间隔分布服从(0,1)上的均匀分布,求更新函数的表达式。

解: $M\left( t\right)$ 满足 $M\left( t\right)  = t + {\int }_{0}^{t}M\left( {t - x}\right) {dx} = t + {\int }_{0}^{t}M\left( y\right) {dy}$ . 两端求导得到 ${M}^{\prime }\left( t\right)  = 1 + M\left( t\right)$ . 令 $h\left( t\right)  = 1 + M\left( t\right)$ ,易得 ${h}^{\prime }\left( t\right)  = h\left( t\right)$ 。从而有 $h\left( t\right)  = K{e}^{t}$ 或 $M\left( t\right)  = K{e}^{t} - 1$ 。又由 $M\left( 0\right)  = 0$ ,故得到 $M\left( t\right)  = {e}^{t} - 1$ .

## 检查悖论

我们可以得到：

$\Pr \left( {{T}_{N\left( t\right)  + 1} - t > y}\right)  = 1 - F\left( {t + y}\right)  + {\int }_{0}^{t}\left\lbrack  {1 - F\left( {t + y - x}\right) }\right\rbrack  {dM}\left( x\right) .$

$\Pr \left( {t - {T}_{N\left( t\right) } > y}\right)  = 1 - F\left( t\right)  + {\int }_{0}^{t - y}\left\lbrack  {1 - F\left( {t - x}\right) }\right\rbrack  {dM}\left( x\right) .$

令 ${X}_{N\left( t\right)  + 1} = {T}_{N\left( t\right)  + 1} - {T}_{N\left( t\right) }$ 。

## 检查悖论

根据上述结果可以说明: 对于泊松过程, $r\left( t\right)  = {T}_{N\left( t\right)  + 1} -$ $t$ 和 $s\left( t\right)  = t - {T}_{N\left( t\right) }$ 均服从参数为 $\lambda$ 的指数分布。 从而得到

$$E\left( {X}_{N\left( t\right)  + 1}\right)  = E\left( {r\left( t\right)  + s\left( t\right) }\right)$$

$$= \frac{2}{\lambda } = 2\frac{1}{\lambda } = {2E}\left( {X}_{1}\right) .$$

检查悖论

令 ${X}_{N\left( t\right)  + 1} = {T}_{N\left( t\right)  + 1} - {T}_{N\left( t\right) }$ 。则 ${X}_{N\left( t\right)  + 1}$ 表示包含点 $t$ 的更新区间的长度。对于任意 $x$ ,有

$$\Pr \left( {{X}_{N\left( t\right)  + 1} > x}\right)  \geq  \Pr \left( {{X}_{i} > x}\right) .$$

## 检查悖论

考虑这样一个例子。假设有一批灯泡,其中一半一点就灭, 另一半则能亮一个月。那么这批灯泡的平均寿命应为半个月。

现在有一个质检员, 随机地选定一些亮着的灯泡, 则他会以为灯泡的平均寿命为一个月!

假设这批灯泡的寿命服从均值为 $\mu$ 的指数分布,一个质检员从亮着的灯泡里挑了一个检查它的寿命,那么这个灯泡的预期寿命

$= \mu  +$ 已经亮着的时间 $> \mu$ .

## 更新方程

## 定义

(更新方程) 称如下形式的积分方程为更新方程

$$K\left( t\right)  = H\left( t\right)  + {\int }_{0}^{t}K\left( {t - s}\right) {dF}\left( s\right)$$

其中 $H\left( t\right) ,F\left( t\right)$ 为已知,且当 $t < 0$ 时 $H\left( t\right) \text{、}F\left( t\right)$ 均为0。 当 $H\left( t\right)$ 在任何区间上有界时,称上方程为适定(Proper)更新方程, 简称为更新方程。

## 卷积性质

## 性质

假设 $B\left( t\right)$ 是单调增加的右连续函数 $B\left( 0\right)  = 0$ 。 $C\left( t\right)$ ,

${C}_{1}\left( t\right) ,{C}_{2}\left( t\right)$ 为光滑有界函数(注：这些条件可以保证卷积有定义),则有

(1) $\mathop{\max }\limits_{{0 \leq  t \leq  T}}\left| {B * C\left( t\right) }\right|  \leq  \mathop{\max }\limits_{{0 \leq  t \leq  T}}\left| {C\left( t\right) }\right|  \cdot  B\left( T\right)$ ;

(2) $B * C\left( t\right)  + B * {C}_{2}\left( t\right)  = B * \left( {{C}_{1} + {C}_{2}}\right) \left( t\right)$ ;

(3) ${B}_{1} * \left( {{B}_{2} * C}\right) \left( t\right)  = \left( {{B}_{1} * {B}_{2}}\right)  * C\left( t\right)$ 。

## 更新方程的解

## 定理

设更新方程中 $H\left( t\right)$ 为有界函数,则方程存在惟一的在有限区间内有界的解

$$K\left( t\right)  = H\left( t\right)  + {\int }_{0}^{t}H\left( {t - s}\right) {dM}\left( s\right)  \tag{1}$$

其中 $M\left( t\right)  = \mathop{\sum }\limits_{{n = 1}}^{\infty }{F}_{n}\left( t\right)$ 是分布函数 $F\left( t\right)$ 的更新函数.

## 更新方程的解

证明：我们先证(1)确实是更新方程的解,并且满足有界性条件。由 $H\left( t\right)$ 有界, $M\left( t\right)$ 是更新函数,可知 $M\left( t\right)$ 有界且单调增加,所以对任何的 $T > 0$ ,由式 (1) ,有

$$\mathop{\sup }\limits_{{0 \leq  t \leq  T}}\left| {K\left( t\right) }\right|  \leq  \left| {H\left( t\right) }\right|  + {\int }_{0}^{T}\mathop{\sup }\limits_{{0 \leq  s \leq  T}}H\left( {T - s}\right) {dM}\left( s\right)$$

$$\leq  \mathop{\sup }\limits_{{0 \leq  t \leq  T}}\left| {H\left( t\right) }\right| \left( {1 + M\left( T\right) }\right)  < \infty .$$

所以在有界区间上 $K\left( t\right)$ 是有界的。

再来看它是否满足更新方程。由式(1),有

$$K\left( t\right)  = H\left( t\right)  + M * H\left( t\right)$$

$$= H\left( t\right)  + \left( {\mathop{\sum }\limits_{{n = 1}}^{\infty }{F}_{n}}\right)  * H\left( t\right)$$

$$= H\left( t\right)  + F * H\left( t\right)  + \left( {\mathop{\sum }\limits_{{n = 2}}^{\infty }{F}_{n}}\right)  * H\left( t\right)$$

## 更新方程的解

$$= H\left( t\right)  + F * H\left( t\right)  + \left( {\mathop{\sum }\limits_{{n = 2}}^{\infty }\left( {{F}_{n - 1} * F}\right) }\right)  * H\left( t\right)$$

$$= H\left( t\right)  + F * \left\lbrack  {H\left( t\right)  + \left( {\mathop{\sum }\limits_{{n = 1}}^{\infty }{F}_{n}}\right)  * H\left( t\right) }\right\rbrack$$

$$= H\left( t\right)  + F * K\left( t\right)$$

$$= H\left( t\right)  + {\int }_{0}^{t}K\left( {t - s}\right) {dF}\left( s\right) .$$

最后要证惟一性,只需证明更新方程的任何解都有(1)式的形式。设 $\widetilde{K}\left( t\right)$ 是更新方程的解,并且满足有界性条件,则由

$$\widetilde{K}\left( t\right)  = H + F * \widetilde{K}\left( t\right)$$

连续代换 $\widetilde{K}\left( t\right)$ ,有

## 更新方程的解

$\widetilde{K} = H\left( t\right)  + F * \left( {H + F * \widetilde{K}}\right) \left( t\right)$

$= H\left( t\right)  + F * H\left( t\right)  + F * \left( {F * \widetilde{K}}\right) \left( t\right)$

$= H\left( t\right)  + F * H\left( t\right)  + {F}_{2} * \widetilde{K}\left( t\right)$

$\begin{matrix}  &  = & H\left( t\right)  + F * H\left( t\right)  + {F}_{2} * \left( {H + F * \widetilde{K}}\right) \left( t\right)  \end{matrix}$

$= H\left( t\right)  + F * H\left( t\right)  + {F}_{2} * H\left( t\right)  + {F}_{3} * \widetilde{K}\left( t\right)$

$=$

$= H + \left( {\mathop{\sum }\limits_{{k = 1}}^{{n - 1}}{F}_{k}}\right)  * H + {F}_{n} * \widetilde{K}\left( t\right) .$

## 更新方程的解

注意到对任何 $t$ ,

$$\left| {{F}_{n} * \widetilde{K}\left( t\right) }\right|  = \left| {{\int }_{0}^{t}\widetilde{K}\left( {t - x}\right) d{F}_{n}\left( x\right) }\right|$$

$$\leq  \left\{  {\mathop{\sup }\limits_{{0 \leq  x \leq  t}}\left| {\widetilde{K}\left( {t - x}\right) }\right| }\right\}   \cdot  {F}_{n}\left( t\right) ,$$

由假定 $\mathop{\sup }\limits_{{0 \leq  x \leq  t}}\left| {\widetilde{K}\left( {t - x}\right) }\right|  < \infty$ 并且 $M\left( t\right)  = \mathop{\sum }\limits_{{n = 1}}^{\infty }{F}_{n}\left( t\right)  <$ ∞知,

$$\mathop{\lim }\limits_{{n \rightarrow  \infty }}{F}_{n}\left( t\right)  = 0,\;\forall t$$

从而

$$\mathop{\lim }\limits_{{n \rightarrow  \infty }}\left| {{F}_{n} * \widetilde{K}\left( t\right) }\right|  = 0.$$

## 更新方程的解

而

$$\mathop{\lim }\limits_{{n \rightarrow  \infty }}\left( {\mathop{\sum }\limits_{{k = 1}}^{{n - 1}}{F}_{k} * H\left( t\right) }\right)  = \left( {\mathop{\sum }\limits_{{k = 1}}^{\infty }{F}_{k}}\right)  * H\left( t\right)$$

$$= M * H\left( t\right)$$

于是推出

$$\widetilde{K}\left( t\right)  = H\left( t\right)  + \mathop{\lim }\limits_{{n \rightarrow  \infty }}\left\lbrack  {\mathop{\sum }\limits_{{k = 1}}^{{n - 1}}{F}_{k} * H\left( t\right)  + {F}_{n} * \widetilde{K}\left( t\right) }\right\rbrack$$

$$= H\left( t\right)  + M * H\left( t\right) ,$$

与式(1)一致。

## 例4.1

例

(Wald等式) 设 $E{X}_{i} < \infty ,i = 1,2,\cdots$ ,证明

$E\left( {T}_{N\left( t\right)  + 1}\right)  = E\left( {{X}_{1} + {X}_{2} + \cdots  + {X}_{N{\left( t\right) }_{ + }1}}\right)  = E{X}_{1}E\left( {N\left( t\right)  + 1}\right) .$

证明: 对第一次更新的时刻 ${X}_{1}$ 取条件

$$E\left( {{T}_{N\left( t\right)  + 1} \mid  {X}_{1} = x}\right)  = \left\{  \begin{array}{lll} x & , & \text{ 若 }x > t, \\  x + E\left( {T}_{N\left( {t - x}\right)  + 1}\right) & \text{ 若 }x \leq  t, &  \end{array}\right.$$

如图4.1:

## 例4.1

<!-- Media -->

<!-- figureText: $\square$ t $\mathrm{{TN}}\left( \mathrm{t}\right)  + 1$ $\mathrm{N}\left( \mathrm{t}\right)  = 0,\mathrm{\;T}1 = \mathrm{x}1$ $\mathrm{{TN}}\left( \mathrm{t}\right)  + 1$ $\mathrm{H}\left( \mathrm{t}\right)  = 0$ $\mathrm{{TN}}\left( {\mathrm{t} - \mathrm{x}1}\right)  + 1$ 0 x1 ${\mathrm{O}}^{0}$ t- $\mathrm{x}1$ -->

<img src="https://cdn.noedgeai.com/01963e87-a26b-7336-a011-d0e66ea95fa2_31.jpg?x=301&y=762&w=1838&h=960&r=0"/>

<!-- Media -->

## 例4.1

记 $K\left( t\right)  = E\left( {T}_{N\left( {t + 1}\right) }\right)$ ,则

$$K\left( t\right)  = E\left( {T}_{N\left( t\right)  + 1}\right)  = E\left\lbrack  {E\left( {{T}_{N\left( t\right)  + 1} \mid  {X}_{1} = x}\right) }\right\rbrack$$

$$= {\int }_{0}^{\infty }E\left( {{T}_{N\left( t\right)  + 1} \mid  {X}_{1} = x}\right) {dF}\left( x\right)$$

$= {\int }_{0}^{t}\left\lbrack  {x + K\left( {t - x}\right) }\right\rbrack  {dF}\left( x\right)  + {\int }_{t}^{\infty }{xdF}\left( x\right)$

$$= E{X}_{1} + {\int }_{0}^{t}K\left( {t - x}\right) {dF}\left( x\right) .$$

## 例4.1

这是更新方程,由更新方程解存在性定理,知

$$K\left( t\right)  = E{X}_{1} + {\int }_{0}^{t}E{X}_{1}{dM}\left( x\right)$$

$$= E\left( {X}_{1}\right)  \cdot  \left( {1 + M\left( t\right) }\right)$$

$$= E\left( {X}_{1}\right)  \cdot  \left( {E\left\lbrack  {N\left( t\right)  + 1}\right\rbrack  }\right) \text{.}$$

## Feller初等更新定理

## 定理

(Feller初等更新定理) 记 $\mu  = E{X}_{n}$ ,则

$$\frac{M\left( t\right) }{t} \rightarrow  \frac{1}{\mu }\left( {t \rightarrow  \infty }\right) ,\;\text{ 若 }\mu  = \infty ,\frac{1}{\mu } = 0.$$

证明: 首先假设 $\mu  < \infty$ ,显然

$${T}_{N\left( t\right)  + 1} > t\text{.}$$

两边取期望, 利用例 4.2 的结论, 得

$$\mu \left\lbrack  {M\left( t\right)  + 1}\right\rbrack   > t$$

即

$$\frac{M\left( t\right) }{t} > \frac{1}{\mu } - \frac{1}{t}$$

## Feller初等更新定理

从而推出

$$\mathop{\liminf }\limits_{{t \rightarrow  \infty }}\frac{M\left( t\right) }{t} \geq  \frac{1}{\mu }$$

另一方面,固定常数 $M$ ,令

$${X}_{n}^{c} = \left\{  \begin{array}{ll} {X}_{n}, & {X}_{n} \leq  M, \\  M, & {X}_{n} > M, \end{array}\right.$$

以 ${X}_{n}^{c},n = 1,2,\cdots$ 确定一个新的更新过程。令

$${T}_{n}^{c} = \mathop{\sum }\limits_{{i = 1}}^{n}{X}_{i}^{c}$$

$$N{\left( t\right) }^{c} = \sup \left\{  {n : {T}_{n}^{c} \leq  t}\right\}  ,$$

## Feller初等更新定理

由于 ${X}_{n}^{c} \leq  M$ ,则

$${T}_{{N}^{c}\left( t\right)  + 1}^{c} \leq  t + M,$$

再利用例4.2, 得

$$\left\lbrack  {M{\left( t\right) }^{c} + 1}\right\rbrack   \cdot  {\mu }_{M} \leq  t + M$$

其中 ${\mu }_{M} = E\left( {X}_{n}^{c}\right) ,M{\left( t\right) }^{c} = E\left\lbrack  {N{\left( t\right) }^{c}}\right\rbrack$ ,由上式,得

$$\frac{M{\left( t\right) }^{c}}{t + M} \leq  \frac{1}{{\mu }_{M}} - \frac{1}{t + M}$$

从而

$$\mathop{\limsup }\limits_{{t \rightarrow  \infty }}\frac{M{\left( t\right) }^{c}}{t} \leq  \frac{1}{{\mu }_{M}}$$

又因为 ${X}_{n}^{c} \leq  {X}_{n}$ ,得 ${T}_{n}^{c} \leq  {T}_{n},{N}^{(}t{)}^{c} \geq  N\left( t\right) ,M{\left( t\right) }^{c} \geq  M\left( t\right)$ , 于是

## Feller初等更新定理

$$\mathop{\limsup }\limits_{{t \rightarrow  \infty }}\frac{M\left( t\right) }{t} \leq  \frac{1}{{\mu }_{M}}$$

令 $M \rightarrow  \infty$ ,有 ${X}_{n}^{c} \rightarrow  {X}_{n},E\left( {X}_{n}^{c}\right)  \rightarrow  E{X}_{n}$ ,即

$${\mu }_{M} \rightarrow  \mu$$

所以

$$\mathop{\limsup }\limits_{{t \rightarrow  \infty }}\frac{M\left( t\right) }{t} \leq  \frac{1}{\mu }$$

利用上述各式得定理结论。

当 $\mu  = \infty$ 时,再考虑由 ${X}_{n}^{c}$ 确定的过程。当 $M \rightarrow  \infty$ 时, $\mu  \rightarrow$ $\infty$ 。由上述各式知结论成立。

## 格点分布

## 定义

(格点分布) 称随机变量 $X$ 服从格点分布(或称随机变量 $X$ 的分布 $F$ 是格点),若存在 $d \geq  0$ ,使得

$$\mathop{\sum }\limits_{{n = 0}}^{\infty }P\left( {X = {nd}}\right)  = 1$$

称满足上述条件的最大的 $d$ 为此格点分布的周期。

注：从上述定义只能知道格点分布在不是 $d$ 的整数倍处取值的概率为 0,但并不一定在所有 ${nd}\left( {n = 0,1,2,\cdots }\right)$ 都一一取到。

## Blackwell 更新定理

## 定理

(Blackwell更新定理) 记 $\mu  = E{X}_{n}$ ,

(1)若 $F$ 不是格点分布,则对一切 $a \geq  0$ ,当 $t \rightarrow  \infty$ 时

$$M\left( {t + a}\right)  - M\left( t\right)  \rightarrow  \frac{a}{\mu }.$$

(2)若 $F$ 是格点分布,周期为 $d$ ,则当 $n \rightarrow  \infty$ 时

$$P\left( {\text{ 在 }{nd}\text{ 处发生 更新 }}\right)  \rightarrow  \frac{d}{\mu }.$$

注：Blackwell定理指出,在远离原点的某长度为 $a$ 的区间内,更新次数的期望是 $\frac{a}{\mu }$

## 初等更新定理是Blackwell定理的特殊情形

事实上,令 ${b}_{n} = {M}_{n} - {M}_{n - 1}$ ,当 $F$ 是非格点时,由 (1) 知

$${b}_{n} \rightarrow  \frac{1}{\mu },\;n \rightarrow  \infty$$

从而, 由极限的性质得

$$\frac{{b}_{1} + {b}_{2} + \cdots  + {b}_{n}}{n} = \frac{M\left( n\right) }{n} \rightarrow  \frac{1}{\mu },\;n \rightarrow  \infty .$$

而对任何实数 $t$ ,

$$\frac{\left\lbrack  t\right\rbrack  }{t} \cdot  \frac{M\left( \left\lbrack  t\right\rbrack  \right) }{\left\lbrack  t\right\rbrack  } \leq  \frac{M\left( t\right) }{t} \leq  \frac{\left\lbrack  t\right\rbrack   + 1}{t} \cdot  \frac{M\left( {\left\lbrack  t\right\rbrack   + 1}\right) }{\left\lbrack  t\right\rbrack   + 1},$$

这里 $\left\lbrack  t\right\rbrack$ 表示 $t$ 的整数部分,即不超过 $t$ 的最大整数. 令 $t \rightarrow  \infty$ 可

得

$$\frac{M\left( t\right) }{t} \rightarrow  \frac{1}{\mu }$$

这就是Feller定理。

## 关键更新定理

## 定理

(关键更新定理) 记 $\mu  = E{X}_{n}$ ,设函数 $h\left( t\right) ,t \in  \lbrack 0,\infty )$ ,满足 (i) $h\left( t\right)$ 非负不增；(ii) ${\int }_{0}^{\infty }h\left( t\right) {dt} < \infty$ 。 $H\left( t\right)$ 是更新方程

$$H\left( t\right)  = h\left( t\right)  + {\int }_{0}^{t}H\left( {t - x}\right) {dF}\left( x\right)$$

的解。那么

(1)若 $F$ 不是格点分布,有

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}H\left( t\right)  = \left\{  \begin{array}{ll} \frac{1}{\mu }{\int }_{0}^{\infty }h\left( x\right) {dx}, & \mu  < \infty , \\  0, & \mu  = \infty . \end{array}\right.$$

( 2 )若 $F$ 是格点分布,对于 $0 \leq  c < d$ ,有

$$\mathop{\lim }\limits_{{n \rightarrow  \infty }}H\left( {c + {nd}}\right)  = \left\{  \begin{matrix} \frac{d}{\mu }\mathop{\sum }\limits_{{n = 0}}^{\infty }h\left( {c + {nd}}\right) , & \mu  < \infty \\  0, & \mu  = \infty  \end{matrix}\right.$$

## 关键更新定理与Blackwell定理的等价性

只考虑 $F$ 不是格点的情况。

(1)关键推Blackwell；在关键更新定理中,取满足(i)、(ii) 两个条件的

$$h\left( t\right)  = \left\{  \begin{matrix} 1, & 0 \leq  t < a, \\  0, & t \geq  a, \end{matrix}\right.$$

代入更新方程的解,则有(我们考虑的是极限情形,故取 $t$ 充分大)

$$H\left( t\right)  = h\left( t\right)  + {\int }_{0}^{t}h\left( {t - x}\right) {dM}\left( x\right)$$

$$= {\int }_{t - a}^{t}{dM}\left( x\right)  = M\left( t\right)  - M\left( {t - a}\right) .$$

又

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}H\left( t\right)  = \frac{1}{\mu }{\int }_{0}^{\infty }h\left( x\right) {dx} = \frac{a}{\mu },$$

## 关键更新定理与Blackwell定理的等价性

从而有

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}\left( {M\left( t\right)  - M\left( {t - a}\right) }\right)  = \frac{a}{\mu }.$$

(2)Blackwell 推关键；由 Blackwell 定理知道,对任意的 $a >$ 0,

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}\frac{M\left( {t + a}\right)  - M\left( t\right) }{a} = \frac{1}{\mu }$$

故

$$\mathop{\lim }\limits_{{a \rightarrow  0}}\mathop{\lim }\limits_{{t \rightarrow  \infty }}\frac{M\left( {t + a}\right)  - M\left( t\right) }{a} = \frac{1}{\mu }.$$

## 关键更新定理与Blackwell定理的等价性

假设极限次序可交换,将有

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}\frac{{dM}\left( t\right) }{dt} = \frac{1}{\mu }$$

即当 $t$ 很大时,有 ${dM}\left( t\right)  \sim  \frac{1}{\mu }{dt}$ .

更新方程的解为 $H\left( t\right)  = h\left( t\right)  + {\int }_{0}^{t}h\left( {t - x}\right) {dM}\left( x\right)$ 。又 ${\int }_{0}^{\infty }h\left( x\right) {dx} <$ $\infty$ ,则当 $t \rightarrow  \infty$ 时, $h\left( t\right)$ 将快速趋于 0,因此,当 $t$ 变得很大时, 对于 $h\left( {t - x}\right)$ 主要考虑当 $t - x$ 比较小也就是 $x$ 比较大的情况。 则有

$${\int }_{0}^{t}h\left( {t - x}\right) {dM}\left( x\right)  \approx  {\int }_{0}^{t}h\left( {t - x}\right) {dx} \cdot  \frac{1}{\mu } = \frac{1}{\mu }{\int }_{0}^{t}h\left( x\right) {dx}.$$

这正是关键更新定理的结论。

## 例：银行服务

例

设有一个单服务员的银行,顾客到达可看作速率为 $\lambda$ 的泊松过程,服务员为每一个顾客服务的时间服从参数为 $\mu$ 的指数分布。顾客到达门口只有在服务员空闲时才能进入银行。试求(1)顾客进银行的速率；(2)服务员工作的时间占营业时间的比例。

解: 两个相邻到达的顾客的时间间隔 ${X}_{i}$ 独立同分布于参数为 $\lambda$ 的指数分布；顾客接受服务的时间 ${Y}_{i}$ 独立同分布于参数为 $\mu$ 的指数分布。则顾客进入银行的平均时间为 $1/\lambda  + 1/\mu  =$ $\left( {\lambda  + \mu }\right) /{\lambda \mu }$ . 速率为 ${\lambda \mu }/\left( {\lambda  + \mu }\right)$ 。

服务员工作的时间占营业时间的比例为

$$\left( {1/\mu }\right) /\left( {1/\lambda  + 1/\mu }\right)  = \lambda /\left( {\lambda  + \mu }\right) \text{.}$$

## 例4.3 剩余寿命与年龄的极限分布

例

(剩余寿命与年龄的极限分布)以 $r\left( t\right)  = {T}_{N\left( t\right)  + 1} - t$ 表示时刻 $t$ 的剩余寿命,即从 $t$ 开始到下次更新剩余的时间, $s\left( t\right)  =$ $t - {T}_{N\left( t\right) }$ 为 $t$ 时刻的年龄。我们来求 $r\left( t\right)$ 和 $s\left( t\right)$ 的极限分布。 解: 令

$${\bar{R}}_{y} = P\left( {r\left( t\right)  > y}\right)$$

对第一次更新的时刻 ${X}_{1}$ 取条件,得

$$P\left( {r\left( t\right)  > y \mid  {X}_{1} = x}\right)  = \left\{  \begin{matrix} 1, & x > t + y \\  0, & t < x \leq  t + y \\  {\bar{R}}_{y}\left( {t - x}\right) , & 0 < x \leq  t \end{matrix}\right.$$

## 例4.3 剩余寿命与年龄的极限分布

由全概率公式,有

$${\bar{R}}_{y}\left( t\right)  = {\int }_{0}^{\infty }P\left( {r\left( t\right)  > y \mid  {X}_{1} = x}\right) {dF}\left( x\right)$$

$$= {\int }_{t + y}^{\infty }{dF}\left( x\right)  + {\int }_{0}^{t}{\bar{R}}_{y}\left( {t - x}\right) {dF}\left( x\right)$$

$$= 1 - F\left( {t + y}\right)  + {\int }_{0}^{t}{\bar{R}}_{y}\left( {t - x}\right) {dF}\left( x\right) ,$$

这是一个更新方程。它的解为

$${\bar{R}}_{y}\left( t\right)  = 1 - F\left( {t + y}\right)  + {\int }_{0}^{t}\left\lbrack  {1 - F\left( {t + y - x}\right) }\right\rbrack  {dM}\left( x\right) .$$

这时仍假设 $\mu  = E{X}_{1} < \infty$ ,则

$$\mu  = {\int }_{0}^{\infty }{xdF}\left( x\right)  = {\int }_{0}^{\infty }\left\lbrack  {1 - F\left( x\right) }\right\rbrack  {dx} < \infty ,$$

## 例4.3 剩余寿命与年龄的极限分布

所以

$${\int }_{0}^{\infty }\left\lbrack  {1 - F\left( {t + y}\right) }\right\rbrack  {dt} = {\int }_{y}^{\infty }\left\lbrack  {1 - F\left( z\right) }\right\rbrack  {dz} < \infty ,$$

即 $1 - F\left( {t + y}\right)$ 满足关键更新定理的条件,于是

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}P\left( {r\left( t\right)  > y}\right)  = \mathop{\lim }\limits_{{t \rightarrow  \infty }}{\bar{R}}_{y}\left( t\right)$$

$$= \frac{1}{\mu }{\int }_{y}^{\infty }\left( {1 - F\left( z\right) }\right) {dz},\;z > 0.$$

年龄 $s\left( t\right)$ 的分布可由上式导出. 注意到

$$\{ r\left( t\right)  > x,s\left( t\right)  > y\}  \Leftrightarrow  \{ r\left( {t - y}\right)  > x + y\}$$

## 例4.3 剩余寿命与年龄的极限分布

## 从而

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}P\left( {r\left( t\right)  > x,s\left( t\right)  > y}\right)  = \mathop{\lim }\limits_{{t \rightarrow  \infty }}P\left( {r\left( {t - y}\right)  > x + y}\right)$$

$$= \frac{1}{\mu }{\int }_{x + y}^{\infty }\left\lbrack  {1 - F\left( z\right) }\right\rbrack  {dz}$$

特别地,

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}P\left( {s\left( t\right)  > y}\right)  = \mathop{\lim }\limits_{{t \rightarrow  \infty }}P\left( {s\left( t\right)  > y,r\left( t\right)  > 0}\right)$$

$$= \frac{1}{\mu }{\int }_{y}^{\infty }\left\lbrack  {1 - F\left( z\right) }\right\rbrack  {dz}$$

## 延迟更新过程

## 定义

更新过程要求时间间隔 ${X}_{1},{X}_{2},{X}_{3},\cdots$ 是分布函数为 $F$ 的独立同分布序列。如果放宽对 ${X}_{1}$ 的要求,允许它服从别的分布 $G$ ,则称由 ${X}_{1},{X}_{2},\cdots$ 确定的计数过程是延迟更新过程。

## 更新回报过程

定义设

$$R\left( t\right)  = \mathop{\sum }\limits_{{i = 1}}^{{N\left( t\right) }}{R}_{i}$$

其中 $\{ N\left( t\right) ,t \geq  0\}$ 是一个更新过程, ${R}_{n},n = 1,2,\cdots$ 独立同分布且与 $\{ N\left( t\right) ,t \geq  0\}$ 独立,则称 $R\left( t\right)$ 是一个更新回报过程

注: 这一名称是由于我们可以把 ${R}_{n}$ 看做第 $n$ 次更新发生时带给我们的回报。

## 更新回报定理

## 定理

(更新回报定理)若更新间隔 ${X}_{1},{X}_{2},\cdots$ 满足 $E{X}_{1} < \infty$ ,每次得到的回报 $\left\{  {R}_{n}\right\}$ 满足 $E{R}_{1} < \infty$ 。则 (1)

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}\frac{1}{t}R\left( t\right)  = \frac{E{R}_{1}}{E{X}_{1}}\text{ w.p. }1$$

(2)

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}\frac{1}{t}{ER}\left( t\right)  = \frac{E{R}_{1}}{E{X}_{1}}$$

## 例4.4 产品保修策略

例

(产品保修策略)设某公司所售出商品采取如下更换策略： 若产品售出后,在期限 $w$ 内损坏,则免费更换同样产品。 若在 $(w,w + T\rbrack$ 期间损坏,则按使用时间折价更换新产品。 并且对在 $(0,w\rbrack$ 内更换的新产品执行原来的更换期,而对在 $(w,w + T\rbrack$ 内折价更换的新产品,从更换时刻重新计算更换期。讨论长期执行此策略对厂家的影响(即厂家的期望利润是多少),假定一旦产品损坏,顾客立刻更换、退换或者购买新的。

## 例4.4 产品保修策略

设用户相邻两次购买(包换全价购买和折扣更换, 但不包括免费更换)的间隔为 ${Y}_{1},{Y}_{2},\cdots$ . 则由 ${Y}_{i}$ 产生一个更新过程。这里

的更新指购买。记此更新过程为 ${N}_{Y}\left( t\right)$ 。每次购买用户的花费为 ${c}_{i}$ 。那么 ${R}_{Y}\left( t\right)  = \mathop{\sum }\limits_{{i = 1}}^{{{N}_{Y}\left( t\right) }}{c}_{i}$ 是一个更新回报过程。根据更新回报定理,长期平均费用为 $E\left( {c}_{1}\right) /E\left( {Y}_{1}\right)$ .

还存在另一个更新过程。设相邻两次更换产品的间隔为 ${X}_{1},{X}_{2}, \cdot$ 并记此更新过程为 $N\left( t\right)$ 。且 ${T}_{n}$ 是第 $n$ 次更换的时刻。则 ${Y}_{1}$ 是 $\omega$ 时刻后的首次更换时刻,即 ${Y}_{1} = {T}_{N\left( \omega \right)  + 1}$ 。

## 例4.4 产品保修策略

设 $t = 0$ 时用户购买一个新产品,售价为 $c$ ,成本 ${c}_{0} < c$ ,

产品寿命为 $X$ ,它的分布函数为 $F\left( t\right) ,{EX} = \mu  < \infty$ 。设用户相临两次购买(包换全价购买和折扣更换,但不包括免费更换)的间隔为 ${Y}_{1},{Y}_{2},\cdots$ 。容易求出 ${Y}_{1},{Y}_{2},\cdots$ 独立同

分布(因为每次更换的产品都是新的)。记 ${Y}_{i}$ 分布为 $G\left( t\right)$ 。 以 $N\left( t\right)$ 记(0,t)时间内的更换次数, ${T}_{n}$ 为第 $n$ 次更换时刻 $M\left( t\right)  =$ $E\left\lbrack  {N\left( t\right) }\right\rbrack$ 。

首先计算用户在 $\left( {0,{Y}_{1}}\right)$ 内的期望花费与 $E{Y}_{1}$ 。由所设条件知道,对用户而言,更换策略为

$$\left\{  \begin{array}{ll} 0, & y \leq  w, \\  \frac{c\left( {y - w}\right) }{T}, & y > w, \end{array}\right.$$

其中 $y$ 为使用时间,由 ${Y}_{1}$ 的含义知, ${Y}_{1} = w + {R}_{w}\left( {R}_{w}\right.$ 指产品在 $w$ 时刻的剩余寿命), ${Y}_{1} = {T}_{N\left( w\right)  + 1}$ ,如图 4.3 所示。

## 例4.4 产品保修策略

<!-- Media -->

<!-- figureText: Y1 T4 T0 T1 T2 T3 -->

<img src="https://cdn.noedgeai.com/01963e87-a26b-7336-a011-d0e66ea95fa2_56.jpg?x=295&y=709&w=1284&h=602&r=0"/>

<!-- Media -->

## 例4.4 产品保修策略

从而

$$\bar{G}\left( t\right)  = P\left( {{Y}_{1} > t}\right)$$

$$= \left\{  \begin{array}{ll} 1, & 0 \leq  t \leq  \iota \\  P\left( {w + {R}_{w} > t}\right)  = \bar{F}\left( t\right)  + {\int }_{0}^{w}\bar{F}\left( {t - x}\right) {dM}\left( x\right) , & w < t, \end{array}\right.$$

$$E{Y}_{1} = E\left\lbrack  {T}_{N\left( w\right)  + 1}\right\rbrack   = \mu \left( {1 + M\left( w\right) }\right)$$

设 $\left( {0,{Y}_{1}}\right\rbrack$ 内用户花费为 ${c}_{1}$ ,则

$${c}_{1} = \left\{  \begin{array}{ll} c & {Y}_{1} > w + T \\  \frac{c\left( {{Y}_{1} - w}\right) }{T} & w < {Y}_{1} \leq  w + T \end{array}\right.$$

从而

$$E{c}_{1} = c\bar{G}\left( {w + T}\right)  + \frac{c}{T}{\int }_{w}^{w + T}\left( {t - w}\right) {dG}\left( t\right) .$$

## 例4.4 产品保修策略

## 解得

$$E{c}_{1} = \frac{c}{T}{\int }_{0}^{T}\bar{G}\left( {w + x}\right) {dx} = \frac{c}{T}{\int }_{0}^{T}P\left( {{R}_{w} > x}\right) {dx},$$

于是

$c\left( {w,T}\right)  =$ 长期平均费用 $= \frac{E{c}_{1}}{E{Y}_{1}} = \frac{c{\int }_{0}^{T}P\left( {{R}_{w} > x}\right) {dx}}{{T\mu }\left\lbrack  {1 + M\left( w\right) }\right\rbrack  }.$

对公司而言,其利润为用户付费所得收入与成本之差, 在 $(0,w\rbrack$ 时间内免费更换产品的个数的期望值为 $E\left\lbrack  {N\left( w\right) }\right\rbrack   =$ $M\left( w\right)$ 。因此,在一个购买周期 $\left( {0,{Y}_{1}}\right\rbrack$ 内,公司所付成本为 ${c}_{0}\left\lbrack  {M\left( w\right)  + 1}\right\rbrack$ ,公司从每个用户所得的长期平均利润为

$$c\left( {w,T}\right)  - \frac{{c}_{0}\left\lbrack  {1 + M\left( w\right) }\right\rbrack  }{\mu \left\lbrack  {1 + M\left( w\right) }\right\rbrack  } = c\left( {w,T}\right)  - \frac{{c}_{0}}{\mu }.$$

## 交替更新过程

## 定义

在更新过程中,我们考虑系统只有一个状态的情况,比如机器一直是开的(即更换零件不需时间).而实际中,零件损坏之后,会有一个拆卸,更换的过程这段时间机器是“关”的.这里我们就来考虑有 “开” “关” 两种状态的更新过程, 我们将这种过程称做交替更新过程。

注：(1)设系统最初是开的,持续开的时间是 ${Z}_{1}$ ,而后关闭,时间为 ${Y}_{1}$ 之后再打开,时间为 ${Z}_{2}$ 又关闭,时间 ${Y}_{2},\cdots$ 交替进行,每当系统被打开称做一次更新。

注：(2)我们假设随机向量列 $\left\{  {\left( {{Z}_{n},{Y}_{n}}\right) ,n \geq  1}\right\}$ 是独立同分布的,从而 $\left\{  {Z}_{n}\right\}  ,\left\{  {Y}_{n}\right\}$ 都是独立同分布的,即 ${Z}_{i},{Y}_{j}$ 在 $i \neq  j$ 时独立,但 ${Z}_{i},{Y}_{i}$ 允许不独立。

## 定理4.10

## 定理

设 $H$ 是 ${Z}_{n}$ 的分布, $G$ 是 ${Y}_{n}$ 的分布, $F$ 是 ${Z}_{n} + {Y}_{n}$ 的分布。并记 $P\left( t\right)  = P\left( {t\text{ 时刻系统是开的 }}\right)$ ,设 $E\left( {{Y}_{n} + {Z}_{n}}\right)  < \infty$ ,且 $F$ 不是格点的,则

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}P\left( t\right)  = \frac{E{Z}_{n}}{E{Z}_{n} + E{Y}_{n}}$$

证明: 对第一次更新的时刻 ${X}_{1} = {Z}_{1} + {Y}_{1}$ 取条件概率,得

$P\left( {t\text{ 时刻系统开着 } \mid  {X}_{1} = x}\right)  = \left\{  \begin{array}{l} P\left( {{Z}_{1} > t \mid  {Z}_{1} + {Y}_{1} > t}\right) , \\  P\left( {t - x}\right) , \end{array}\right.$ $x < t$ .

## 定理4.10

则

$P\left( t\right)  = {\int }_{0}^{\infty }P\left( {t\text{ 时刻系统开着 } \mid  {X}_{1} = x}\right) {dF}\left( x\right)$

$$= P\left( {{Z}_{1} > t}\right)  + {\int }_{0}^{t}P\left( {t - x}\right) {dF}\left( x\right)$$

$$= \bar{H}\left( t\right)  + {\int }_{0}^{t}P\left( {t - x}\right) {dF}\left( x\right) .$$

上方程的解为

$$P\left( t\right)  = \bar{H}\left( t\right)  + {\int }_{0}^{t}\bar{H}\left( {t - x}\right) {dM}\left( x\right) .$$

又 ${\int }_{0}^{\infty }\bar{H}\left( t\right) {dt} = E{Z}_{1} < \infty$ 且显然 $\bar{H}\left( t\right)$ 非负不增,由关键更新定理得

$$\mathop{\lim }\limits_{{t \rightarrow  \infty }}P\left( t\right)  = \frac{{\int }_{0}^{\infty }\bar{H}\left( t\right) {dt}}{E\left( {{Y}_{1} + {Z}_{1}}\right) } = \frac{E{Z}_{1}}{E{Z}_{1} + E{Y}_{1}}.$$