{"cells": [{"cell_type": "markdown", "id": "e2b67799-340c-4c52-85e7-3f70b9e9079b", "metadata": {}, "source": ["```sh\n", "source /etc/network_turbo\n", "\n", "pip install git+https://github.com/illuin-tech/colpali\n", "pip install flash-attn\n", "```\n", "\n", "7/25 16:08"]}, {"cell_type": "code", "execution_count": 1, "id": "ccdb3cb9-cf3a-422e-80b5-bd519868b8a6", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'"]}, {"cell_type": "code", "execution_count": 2, "id": "fc92822a-08ac-49be-b3db-4e0b8d28eb7d", "metadata": {}, "outputs": [], "source": ["import torch\n", "from PIL import Image\n", "from transformers.utils.import_utils import is_flash_attn_2_available\n", "\n", "from colpali_engine.models import ColQwen2_5, ColQwen2_5_Processor\n", "\n", "is_flash_attn_2_available()"]}, {"cell_type": "code", "execution_count": 5, "id": "744e05df-25cf-4d0c-a6b6-cad3a8be5137", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7096247830e1407cb95145362dac7aff", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 2 files:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7b83e9e98319422a9312de9919ed28a5", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00002.safetensors:   0%|          | 0.00/5.00G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4e81e17688574acbb4d3e5c1ce08421a", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dc5885d816bd468ab43389168ef33657", "version_major": 2, "version_minor": 0}, "text/plain": ["adapter_model.safetensors:   0%|          | 0.00/240M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ab2555922c53454fbfef4dbd49678fc0", "version_major": 2, "version_minor": 0}, "text/plain": ["preprocessor_config.json:   0%|          | 0.00/328 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6a84ff9f4fb24f22b21573113b9f1bef", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "12621d3de6da4306965110bde65faaab", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f1546494130d43b0b60137ddb4161414", "version_major": 2, "version_minor": 0}, "text/plain": ["merges.txt: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "021363cbb6af4fd49d6e0588d150fb9b", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/11.4M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9cf423f16bfd4e6294177aa4ad4115e9", "version_major": 2, "version_minor": 0}, "text/plain": ["added_tokens.json:   0%|          | 0.00/237 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1c55d5943d35443790cc7d4e48fffa57", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/225 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e11b257ecd154d59beb69e33f68e4ea2", "version_major": 2, "version_minor": 0}, "text/plain": ["video_preprocessor_config.json:   0%|          | 0.00/53.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "906e94ca916745968b81dd95ca6bebef", "version_major": 2, "version_minor": 0}, "text/plain": ["chat_template.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = ColQwen2_5.from_pretrained(\n", "        \"vidore/colqwen2.5-v0.2\",\n", "        torch_dtype=torch.bfloat16,\n", "        device_map=\"cuda:0\",  # or \"mps\" if on Apple Silicon\n", "        attn_implementation=\"flash_attention_2\" if is_flash_attn_2_available() else None,\n", ").eval()\n", "\n", "processor = ColQwen2_5_Processor.from_pretrained(\"vidore/colqwen2.5-v0.2\")"]}, {"cell_type": "code", "execution_count": 6, "id": "a3ead051-74df-4a2a-960f-ce8f6a5f3558", "metadata": {}, "outputs": [], "source": ["import requests\n", "from PIL import Image\n", "import io\n", "\n", "def download_image(url):\n", "    \"\"\"下载图片并返回PIL Image对象\"\"\"\n", "    response = requests.get(url, headers={\"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64)\"})\n", "    response.raise_for_status()\n", "    return Image.open(io.BytesIO(response.content))\n", "\n", "# Configure truncate_dim, max_length (for texts), max_pixels (for images), vector_type, batch_size in the encode function if needed\n", "\n", "# Encode query\n", "queries =[\n", "    'How much percentage of Germanys population died in the 2nd World War?',\n", "    'How many million tons CO2 were captured from Gas processing in 2018?',\n", "    'What is the average CO2 emission of someone in Japan?'\n", "]\n", "\n", "# Encode image/document\n", "images=[\n", "    download_image('https://wiki-upload.yayeah.xyz/wikipedia/commons/3/35/Human_losses_of_world_war_two_by_country.png'),\n", "    download_image('https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/7/76/20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg/2560px-20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg.png'),\n", "    download_image('https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/f/f3/20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg/2880px-20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg.png')\n", "]"]}, {"cell_type": "code", "execution_count": 10, "id": "9794e641-d734-4b4a-8f2f-b5ea7ecd8eb3", "metadata": {}, "outputs": [], "source": ["# Process the inputs\n", "batch_images = processor.process_images(images).to(model.device)\n", "batch_queries = processor.process_queries(queries).to(model.device)"]}, {"cell_type": "code", "execution_count": 8, "id": "f98e2145-b8e3-4c3b-b707-d399f7c4c966", "metadata": {}, "outputs": [], "source": ["# Forward pass\n", "with torch.no_grad():\n", "    image_embeddings = model(**batch_images)\n", "    query_embeddings = model(**batch_queries)\n", "\n", "scores = processor.score_multi_vector(query_embeddings, image_embeddings)"]}, {"cell_type": "code", "execution_count": 9, "id": "bbda9b0b-bdf9-4b0c-9906-45f1a410a751", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[19.7500,  9.9375, 13.2500],\n", "        [ 9.1250, 24.3750, 13.2500],\n", "        [12.0625, 12.6875, 17.6250]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["scores"]}, {"cell_type": "code", "execution_count": null, "id": "9a128f8c-9bbe-4f78-9eb9-372f99dd000e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}