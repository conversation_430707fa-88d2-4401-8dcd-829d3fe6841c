# des11.py（方案2）
# 更激进的局部绑定 + while 循环 + memoryview（依赖 des9 的查表与轮密钥生成）
import struct, os, importlib.util

try:
    from des9 import IP8, FP8, E48, SP8, _generate_round_keys_from_u64, _KEY_SCHED_CACHE
except Exception:
    _p = os.path.join(os.path.dirname(__file__), "des9.py")
    _spec = importlib.util.spec_from_file_location("des9", _p)
    _m = importlib.util.module_from_spec(_spec)
    _spec.loader.exec_module(_m)
    IP8, FP8, E48, SP8 = _m.IP8, _m.FP8, _m.E48, _m.SP8
    _generate_round_keys_from_u64 = _m._generate_round_keys_from_u64
    _KEY_SCHED_CACHE = _m._KEY_SCHED_CACHE

_pack = struct.pack_into
_unpack = struct.unpack_from

def _pack_utf16be_and_pad(s: str) -> bytes:
    if not s:
        return b""
    b = s.encode("utf-16-be", "surrogatepass")
    r = len(b) & 7
    if r:
        b += b"\x00" * (8 - r)
    return b

def _enc_block_chained_u64(b64, brk,
    T0=IP8[0], T1=IP8[1], T2=IP8[2], T3=IP8[3],
    T4=IP8[4], T5=IP8[5], T6=IP8[6], T7=IP8[7],
    F0=FP8[0], F1=FP8[1], F2=FP8[2], F3=FP8[3],
    F4=FP8[4], F5=FP8[5], F6=FP8[6], F7=FP8[7],
    e0=E48[0], e1=E48[1], e2=E48[2], e3=E48[3],
    t0=SP8[0], t1=SP8[1], t2=SP8[2], t3=SP8[3],
    t4=SP8[4], t5=SP8[5], t6=SP8[6], t7=SP8[7]
):
    if not brk:
        return b64

    ip = (T0[(b64>>56)&0xFF] | T1[(b64>>48)&0xFF] |
          T2[(b64>>40)&0xFF] | T3[(b64>>32)&0xFF] |
          T4[(b64>>24)&0xFF] | T5[(b64>>16)&0xFF] |
          T6[(b64>> 8)&0xFF] | T7[ b64     &0xFF])

    L = ip >> 32
    R = ip & 0xFFFFFFFF

    for rk16 in brk:
        k0,k1,k2,k3,k4,k5,k6,k7,k8,k9,k10,k11,k12,k13,k14,k15 = rk16
        for k in (k0,k1,k2,k3,k4,k5,k6,k7,k8,k9,k10,k11,k12,k13,k14,k15):
            E  = (e0[(R>>24)&0xFF] | e1[(R>>16)&0xFF] |
                  e2[(R>> 8)&0xFF] | e3[ R      &0xFF])
            Et = E ^ k
            f  = (t0[(Et>>42)&0x3F] ^ t1[(Et>>36)&0x3F] ^
                  t2[(Et>>30)&0x3F] ^ t3[(Et>>24)&0x3F] ^
                  t4[(Et>>18)&0x3F] ^ t5[(Et>>12)&0x3F] ^
                  t6[(Et>> 6)&0x3F] ^ t7[ Et     &0x3F])
            L, R = R, (L ^ f)
        L, R = R, L

    pre = (L << 32) | R
    return (F0[(pre>>56)&0xFF] | F1[(pre>>48)&0xFF] |
            F2[(pre>>40)&0xFF] | F3[(pre>>32)&0xFF] |
            F4[(pre>>24)&0xFF] | F5[(pre>>16)&0xFF] |
            F6[(pre>> 8)&0xFF] | F7[ pre     &0xFF])

def strEnc(data, firstKey, secondKey, thirdKey):
    if not data:
        return ''

    keyt = (firstKey or '', secondKey or '', thirdKey or '')
    brk = _KEY_SCHED_CACHE.get(keyt)
    if brk is None:
        out_list = []
        for k in (firstKey, secondKey, thirdKey):
            if not k:
                continue
            kb = _pack_utf16be_and_pad(k)
            i = 0; n = len(kb)
            while i < n:
                (kb64,) = _unpack('>Q', kb, i)
                out_list.append(_generate_round_keys_from_u64(kb64))
                i += 8
        _KEY_SCHED_CACHE[keyt] = out_list
        brk = out_list

    db = _pack_utf16be_and_pad(data)
    n = len(db)
    out = bytearray(n)
    mv  = memoryview(db)
    i = 0
    enc = _enc_block_chained_u64
    while i < n:
        (v,) = _unpack('>Q', mv, i)
        if brk:
            v = enc(v, brk)
        _pack('>Q', out, i, v)
        i += 8
    return out.hex().upper()
