26王道数据结构.pdf - 第19页文本内容
==================================================
7 
int i=O; 
while(i*i*i<=n) 
i++; 
}.. 
A. O(n) 
B. O(nlog2n) 
for (j=l;j<i;j++) 
if (A [j] >A [j+l]) 
). 
A. O(n) 
B. O(nlog2n) 
C. 
). 
C. 
D. 
if(n>=O) ( 
for(int i=O;i<n;i++) 
for(int j=O;j<n;j++) 
else( 
for(int j=O;j<n;j++) 
A. 
O(n) 
C. 0(1) 
). 
int m=O,i,j; 
for(i=l;i<=n;i++} 
for (j=l; j <=2*i; j ++) 
D. O(nlo&n) 
A. n(n+ I) 
B. n 
). 
int Fune (int n) { 
if(n==l) return l; 
else return 2*Func(n/2)+n; 
C. n+ l 
D.,?-
A. O(n) 
8. O(nlog2n) 
C. O(log2n) 
D. 
x=2; 
while(x<n/2) 
x=2*x; 
A. O(lo&n) 
B. O(n) 
C. O(nlo&n) 
D. 
13. 
int fact (int n) { 
if(n<=l) return l; 
return n*fact(n-1); 
A. O(login) 
8. O(n) 
C. O(nlogin) 
14. 
). 
count=O; 
for(k=l;k<=n;k*=2} 
for (j=l; j <=n; j ++} 
D. 
3 cn 有多少忙
D
i : n→
1
j :
1 →[
|+
… +n差数列
= O (n3
A : } ,jt无小大
一
A
永不执行
A
第时间复裤1 库预亭!品:2
+ y
+ … + n 多差r
nai+ →
ln
-1)d
c
Δ
= 2n+ n ( n -1 )
列: hiz,k
= 108 ="=
n
( m +n)
A
x
: 2 →
*=^ 典
B
nx
: fin☆nf( n +)| n (
n -)finz) →n !
实质弄吹
c
永积规则
有效性
k
:
1 些, n
Ol 10gzn
,
初体验j
:
1
→n θ
cm ,
