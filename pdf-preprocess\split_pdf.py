import os
import time
import sys
from pathlib import Path
import pymupdf
from google import genai
from google.genai import types, errors
import asyncio
import aiofiles
from typing import List, Dict, Any, Optional

os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7897'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7897'

# 文件大小限制（MB），超过此大小将使用File API
FILE_API_THRESHOLD_MB = 20

class PDFSplitterWithGemini:
    def __init__(self, gemini_api_key=None, gemini_model="gemini-2.5-pro", max_concurrent_requests=5):
        """
        初始化PDF拆分器

        Args:
            gemini_api_key (str, optional): Gemini API密钥
            gemini_model (str): 使用的Gemini模型
            max_concurrent_requests (int): 最大并发请求数
        """
        self.api_key = gemini_api_key
        self.model = gemini_model
        self.max_concurrent_requests = max_concurrent_requests
        self._semaphore = asyncio.Semaphore(max_concurrent_requests)
        self._client = None
        if self.api_key:
            self._initialize_client()

    def _initialize_client(self):
        """初始化或重新初始化Gemini客户端"""
        if self.api_key:
            self._client = genai.Client(api_key=self.api_key)
        else:
            self._client = None
            
    @property
    def api_key(self):
        return self._api_key

    @api_key.setter
    def api_key(self, value):
        self._api_key = value
        self._initialize_client()

    def _is_large_file(self, file_size_mb: float) -> bool:
        """
        检查文件是否超过阈值需要使用File API

        Args:
            file_size_mb (float): 文件大小（MB）

        Returns:
            bool: True表示需要使用File API，False表示可以直接处理
        """
        return file_size_mb > FILE_API_THRESHOLD_MB

    def _get_file_status_icon(self, file_size_mb: float) -> str:
        """
        根据文件大小获取状态图标

        Args:
            file_size_mb (float): 文件大小（MB）

        Returns:
            str: 状态图标
        """
        return "🔄" if self._is_large_file(file_size_mb) else "✅"

    def _get_file_processing_status(self, file_size_mb: float) -> str:
        """
        根据文件大小获取处理状态描述

        Args:
            file_size_mb (float): 文件大小（MB）

        Returns:
            str: 处理状态描述
        """
        return "⬆️ File API" if self._is_large_file(file_size_mb) else "✅ 直接处理"

    def extract_pdf_toc(self, pdf_path):
        """
        提取PDF目录信息（保持同步，因为文件I/O较快）
        
        Args:
            pdf_path (str): PDF文件路径
            
        Returns:
            list: 二级目录列表，格式：[(title, start_page, end_page), ...]
        """
        doc = pymupdf.open(pdf_path)
        toc = doc.get_toc()

        if not toc:
            print("警告：PDF文档没有找到目录信息")
            doc.close()
            return []

        # ------------------------------------------------------------
        # 新策略：
        # 首选二级目录 (level==2) 进行拆分；
        # 对于**没有包含任何二级目录**的一级目录 (level==1)，
        # 也单独作为一个拆分章节。
        # ------------------------------------------------------------

        candidate_chapters = []  # (level, title, start_page)
        toc_len = len(toc)

        for idx, item in enumerate(toc):
            level, title, page = item

            if level == 2:
                # 普通二级目录直接作为拆分章节，保存level
                candidate_chapters.append((level, title, page))

            elif level == 1:
                # 检查该一级目录到下一个一级目录之间是否存在二级目录
                has_level2 = False
                j = idx + 1
                while j < toc_len and toc[j][0] != 1:
                    if toc[j][0] == 2:
                        has_level2 = True
                        break
                    j += 1

                # 如果没有二级目录，则将该一级目录本身作为章节，保存level
                if not has_level2:
                    candidate_chapters.append((level, title, page))

        if not candidate_chapters:
            # 仍没有找到章节，退回到一级目录，保存level
            candidate_chapters = [(1, item[1], item[2]) for item in toc if item[0] == 1]

        # 按开始页排序
        candidate_chapters.sort(key=lambda x: x[2])

        # 构建从子章节到父章节的映射
        parent_map = {}
        last_level1_item = None
        for item in toc:
            if item[0] == 1:
                last_level1_item = item
            elif item[0] > 1:
                if last_level1_item:
                    # 使用(level, title, page)作为键
                    parent_map[(item[0], item[1], item[2])] = last_level1_item

        # 确定结束页
        chapters = []
        num_candidates = len(candidate_chapters)
        for i in range(num_candidates):
            current_level, current_title, current_start_page = candidate_chapters[i]
            current_candidate_tuple = (current_level, current_title, current_start_page)

            if i < num_candidates - 1:
                next_level, next_title, next_start_page = candidate_chapters[i+1]
                next_candidate_tuple = (next_level, next_title, next_start_page)
                end_page = next_start_page

                # 检查是否需要包含下一节首页
                # 条件: 1. 当前和下一个都是二级目录
                #       2. 它们属于同一个父目录
                current_parent = parent_map.get(current_candidate_tuple)
                next_parent = parent_map.get(next_candidate_tuple)

                if current_level == 2 and next_level == 2 and current_parent is not None and current_parent == next_parent:
                    end_page += 1
            else:
                end_page = doc.page_count  # 最后一章直到文档结尾

            chapters.append((current_title, current_start_page, end_page))

        doc.close()
        return chapters
    
    def split_pdf_by_chapters(self, pdf_path, chapters=None, output_dir="_pdfs", verbose=True, toc_source_pdf_path=None):
        """
        根据章节信息拆分PDF，并为每个拆分文件生成独立的TOC
        """
        # 如果没有提供章节信息，自动提取
        if chapters is None:
            chapters = self.extract_pdf_toc(pdf_path)
            if not chapters:
                print("无法提取目录信息，拆分失败")
                return []
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        doc = pymupdf.open(pdf_path)
        
        # --- 确定TOC的来源 ---
        toc_doc = None
        if toc_source_pdf_path and os.path.exists(toc_source_pdf_path):
            toc_doc = pymupdf.open(toc_source_pdf_path)
            full_toc = toc_doc.get_toc(simple=False)
        else:
            full_toc = doc.get_toc(simple=False) # 保持原始行为
            
        split_files = []
        
        if verbose:
            print(f"\n🔄 开始拆分PDF: {pdf_path}")
            print(f"📁 输出目录: {output_dir}")
            print("=" * 60)
        
        total_chapters = len(chapters)
        for i, (title, start_page, end_page) in enumerate(chapters, 1):
            # 清理文件名中的特殊字符
            safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
            output_filename = f"{i:02d}_{safe_title}.pdf"
            output_path = os.path.join(output_dir, output_filename)
            
            # 创建新的PDF文档
            new_doc = pymupdf.open()
            
            # 确定正确的结束页码
            # PyMuPDF的to_page是包含的，而我们的end_page是下一章的起始页
            # 所以对于非最后一章，需要-2
            to_page_index = end_page - 2 if i < total_chapters else end_page - 1

            # 插入指定页面范围（PyMuPDF使用0-based索引）
            new_doc.insert_pdf(doc, from_page=start_page - 1, to_page=to_page_index)
            
            # --- 构建并设置新的TOC ---
            # 1. 筛选出属于当前章节范围的TOC条目
            chapter_toc_items = []
            chapter_toc_indices = []
            for item_idx, item in enumerate(full_toc):
                # item[2] is page number (1-based)
                if start_page <= item[2] < end_page:
                    chapter_toc_items.append(list(item)) # Use a copy
                    chapter_toc_indices.append(item_idx)

            if chapter_toc_items:
                # 2. 检查第一个条目的level，如果不为1，则向前追溯父级
                first_item_level = chapter_toc_items[0][0]
                if first_item_level > 1:
                    # 需要向前查找父级
                    current_idx = chapter_toc_indices[0]
                    
                    # 查找并前置所有父级条目
                    while full_toc[current_idx][0] > 1:
                        parent_level = full_toc[current_idx][0] - 1
                        parent_idx = -1
                        # 从当前位置向前反向搜索
                        for j in range(current_idx - 1, -1, -1):
                            if full_toc[j][0] == parent_level:
                                parent_idx = j
                                break
                        
                        if parent_idx != -1:
                            parent_item = list(full_toc[parent_idx])
                            # 根据用户方案，将父级条目页码强制设为1
                            parent_item[2] = 1
                            chapter_toc_items.insert(0, parent_item)
                            current_idx = parent_idx
                        else:
                            # 没找到父级，停止追溯
                            break
                
                # 3. 调整所有TOC条目的页码
                for item in chapter_toc_items:
                    # 只有在原始页面范围内的页码才进行转换
                    # 被强制设为1的父级条目页码不应再被修改
                    if start_page <= item[2] < end_page:
                        item[2] = item[2] - start_page + 1

                # 4. 设置新的TOC，并确保其完全展开
                new_doc.set_toc(chapter_toc_items, collapse=0)

                # 5. 设置PDF打开时默认显示目录面板
                new_doc.set_pagemode("UseOutlines")
            # --- TOC构建结束 ---

            # 保存拆分的PDF
            new_doc.save(output_path)
            new_doc.close()
            
            # 获取文件大小
            file_size = os.path.getsize(output_path)
            file_size_mb = file_size / (1024 * 1024)
            
            # 计算实际页数
            pages_count = to_page_index - (start_page - 1) + 1

            split_info = {
                'index': i,
                'title': title,
                'file_path': output_path,
                'filename': output_filename,
                'start_page': start_page,
                'end_page': to_page_index + 1, # 转换为1-based的结束页
                'file_size': file_size,
                'file_size_mb': file_size_mb,
                'pages_count': pages_count
            }
            
            split_files.append(split_info)
            
            if verbose:
                # 显示拆分进度
                status_icon = self._get_file_status_icon(file_size_mb)
                print(f"{status_icon} [{i:02d}] {safe_title}")
                print(f"    📄 页面: {split_info['start_page']}-{split_info['end_page']} ({pages_count}页)")
                print(f"    📦 大小: {file_size_mb:.2f}MB")
                print(f"    📁 文件: {output_filename}")
                if self._is_large_file(file_size_mb):
                    print(f"    ℹ️  文件超过 {FILE_API_THRESHOLD_MB}MB，将使用 File API 上传处理")
                print()
        
        if toc_doc:
            toc_doc.close()
        doc.close()
        
        if verbose:
            print("=" * 60)
            print(f"✅ 拆分完成！共生成 {len(split_files)} 个文件")
        return split_files
    
    def preview_split_tasks(self, split_files, custom_prompt=""):
        """
        预览即将发送到API的任务信息
        
        Args:
            split_files (list): 拆分文件信息列表
            custom_prompt (str): 自定义prompt
        """
        print("\n" + "=" * 80)
        print("📋 API 任务预览 (异步并发处理)")
        print("=" * 80)
        
        valid_files = [f for f in split_files if not self._is_large_file(f['file_size_mb'])]
        large_files = [f for f in split_files if self._is_large_file(f['file_size_mb'])]
        
        print(f"📊 统计信息:")
        print(f"   • 总文件数: {len(split_files)}")
        print(f"   • 直接处理文件: {len(valid_files)} (大小 <= {FILE_API_THRESHOLD_MB}MB)")
        print(f"   • File API处理文件: {len(large_files)} (大小 > {FILE_API_THRESHOLD_MB}MB)")
        print(f"   • 最大并发数: {self.max_concurrent_requests}")
        
        if split_files:
            total_size = sum(f['file_size_mb'] for f in split_files)
            total_pages = sum(f['pages_count'] for f in split_files)
            print(f"   • 总处理大小: {total_size:.2f}MB")
            print(f"   • 总页面数: {total_pages}页")
        
        print(f"\n🤖 使用模型: {self.model}")
        print(f"📝 自定义Prompt: {'已设置' if custom_prompt.strip() else '未设置'}")
        
        if custom_prompt.strip():
            print(f"\n📄 Prompt 内容预览:")
            print("─" * 40)
            preview_text = custom_prompt[:200] + "..." if len(custom_prompt) > 200 else custom_prompt
            print(preview_text)
            print("─" * 40)
        
        print(f"\n📋 处理任务列表:")
        print("─" * 80)
        
        for file_info in split_files:
            status = self._get_file_processing_status(file_info['file_size_mb'])
            print(f"{status} [{file_info['index']:02d}] {file_info['title']}")
            print(f"       📄 {file_info['start_page']}-{file_info['end_page']} | 📦 {file_info['file_size_mb']:.2f}MB")
        
        if large_files:
            print(f"\nℹ️  以下文件将通过File API上传处理:")
            for file_info in large_files:
                print(f"   • {file_info['filename']} ({file_info['file_size_mb']:.2f}MB)")
        
        print("=" * 80)
    
    def confirm_api_processing(self):
        """
        交互式确认是否继续API处理（保持同步）
        """
        print("\n❓ 确认选项:")
        print("   1. 继续提交到 Gemini API (异步并发处理)")
        print("   2. 取消，稍后手动处理")
        print("   3. 仅处理指定的文件")
        
        while True:
            choice = input("\n请选择 (1/2/3): ").strip()
            if choice == '1':
                return True, None
            elif choice == '2':
                print("✋ 已取消API处理")
                return False, None
            elif choice == '3':
                return True, self._select_files_to_process()
            else:
                print("❌ 请输入有效选择 (1/2/3)")
    
    def _select_files_to_process(self):
        """
        让用户选择要处理的文件
        
        Returns:
            list: 选中的文件索引列表
        """
        print("\n📝 请输入要处理的文件编号 (例如: 1,3,5-7):")
        while True:
            try:
                user_input = input("文件编号: ").strip()
                selected_indices = []
                
                for part in user_input.split(','):
                    part = part.strip()
                    if '-' in part:
                        start, end = map(int, part.split('-'))
                        selected_indices.extend(range(start, end + 1))
                    else:
                        selected_indices.append(int(part))
                
                return sorted(set(selected_indices))
            except ValueError:
                print("❌ 输入格式错误，请使用逗号分隔的数字或范围 (如: 1,3,5-7)")
    
    async def upload_file_async(self, file_path: str, display_name: str) -> Optional[types.File]:
        """
        异步上传文件到File API
        
        Args:
            file_path (str): 文件路径
            display_name (str): 显示名称
        
        Returns:
            Optional[types.File]: 上传成功返回File对象，否则返回None
        """
        try:
            print(f"   🔄  正在上传大文件: {display_name}...")
            # genai.upload_file 是一个同步操作, 我们需要在异步环境中运行它
            loop = asyncio.get_running_loop()
            uploaded_file = await loop.run_in_executor(
                None, 
                lambda: self._client.files.upload(file=file_path)
            )
            print(f"   ✅  上传成功: {display_name} -> {uploaded_file.name}")
            return uploaded_file
        except Exception as e:
            print(f"   ❌  上传失败: {display_name}, 错误: {e}")
            return None

    async def _handle_api_error_with_retry(self, error: Exception, file_info: Dict, attempt: int, max_retries: int) -> Dict[str, Any]:
        """
        处理API错误并决定是否重试

        Args:
            error (Exception): 捕获的异常
            file_info (Dict): 文件信息
            attempt (int): 当前尝试次数
            max_retries (int): 最大重试次数

        Returns:
            Dict: 如果是最后一次尝试，返回失败结果；否则返回None表示继续重试
        """
        if isinstance(error, errors.APIError):
            error_msg = f"API错误 (尝试 {attempt + 1}/{max_retries}): {error.code} - {error.message}"
        else:
            error_msg = f"其他错误 (尝试 {attempt + 1}/{max_retries}): {str(error)}"

        print(f"❌ [{file_info['index']:02d}] {error_msg}")

        if attempt == max_retries - 1:
            return {
                'success': False,
                'file_info': file_info,
                'response_text': None,
                'error': error_msg,
                'attempt': attempt + 1
            }

        # 指数退避重试
        await asyncio.sleep(2 ** attempt)
        return None  # 表示继续重试

    async def send_to_gemini_async(self, parts: List[Any], file_info: Dict, max_retries: int = 3) -> Dict[str, Any]:
        """
        异步发送内容到Gemini API
        
        Args:
            parts (List[Any]): 发送到模型的内容列表 (包含文本和文件)
            file_info (Dict): 文件信息
            max_retries (int): 最大重试次数
            
        Returns:
            Dict: 包含结果和状态的字典
        """
        async with self._semaphore:  # 限制并发数
            contents = [
                types.Content(
                    role="user",
                    parts=parts,
                ),
            ]
            
            generate_content_config = types.GenerateContentConfig(
                thinking_config=types.ThinkingConfig(
                    thinking_budget=file_info.get('thinking_budget', 32768),
                ),
                response_mime_type="text/plain",
            )
            
            for attempt in range(max_retries):
                try:
                    # 使用异步API
                    response = await self._client.aio.models.generate_content(
                        model=self.model,
                        contents=contents,
                        config=generate_content_config,
                    )
                    
                    return {
                        'success': True,
                        'file_info': file_info,
                        'response_text': response.text,
                        'error': None,
                        'attempt': attempt + 1
                    }
                    
                except (errors.APIError, Exception) as e:
                    error_result = await self._handle_api_error_with_retry(e, file_info, attempt, max_retries)
                    if error_result:  # 如果是最后一次尝试，返回错误结果
                        return error_result
                    # 否则继续重试
    
    async def save_text_output_async(self, content: str, output_path: str):
        """
        异步保存文本内容到文件
        """
        async with aiofiles.open(output_path, 'w', encoding='utf-8') as f:
            await f.write(content)
    
    async def process_single_file_async(self, file_info: Dict, custom_prompt: str, output_dir: str) -> Dict[str, Any]:
        """
        异步处理单个文件
        
        Args:
            file_info (Dict): 文件信息
            custom_prompt (str): 用户提供的预设prompt
            output_dir (str): 输出目录
            
        Returns:
            Dict: 处理结果
        """
        try:
            parts = [types.Part.from_text(text=custom_prompt)]
            uploaded_file_ref = None

            if self._is_large_file(file_info['file_size_mb']):
                # 使用File API处理大文件
                uploaded_file = await self.upload_file_async(file_info['file_path'], file_info['filename'])
                if uploaded_file:
                    # 将File对象转换为Part对象
                    parts.append(types.Part.from_uri(
                        file_uri=uploaded_file.uri,
                        mime_type=uploaded_file.mime_type,
                    ))
                    uploaded_file_ref = uploaded_file # 保存引用以便后续删除
                else:
                    return {
                        'success': False, 'file_info': file_info, 'error': "文件上传失败",
                        'response_text': None, 'attempt': 1
                    }
            else:
                # 异步读取小文件
                async with aiofiles.open(file_info['file_path'], 'rb') as f:
                    pdf_bytes = await f.read()
                parts.append(types.Part.from_bytes(
                    mime_type="application/pdf",
                    data=pdf_bytes,
                ))

            # 异步发送到Gemini
            result = await self.send_to_gemini_async(parts, file_info)
            
            # 如果使用File API上传了文件，处理完成后删除
            if uploaded_file_ref:
                try:
                    await asyncio.get_running_loop().run_in_executor(
                        None, 
                        lambda: self._client.files.delete(name=uploaded_file_ref.name)
                    )
                    print(f"   🗑️  已删除临时文件: {uploaded_file_ref.name}")
                except Exception as e:
                    print(f"   ⚠️  删除临时文件失败: {uploaded_file_ref.name}, 错误: {e}")

            if result['success']:
                # 异步保存响应文本
                text_filename = f"{Path(file_info['file_path']).stem}.txt"
                text_output_path = os.path.join(output_dir, text_filename)
                await self.save_text_output_async(result['response_text'], text_output_path)
                
                result['output_file'] = text_filename
                result['output_path'] = text_output_path
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'file_info': file_info,
                'response_text': None,
                'error': f"文件处理错误: {str(e)}",
                'attempt': 1
            }
    
    async def process_with_gemini_api_async(self, split_files: List[Dict], custom_prompt: str,
                                          output_dir: str = "_mds",
                                          selected_indices: Optional[List[int]] = None,
                                          thinking_budget: int = 32768):
        """
        异步并发处理拆分的PDF文件

        Args:
            split_files (List[Dict]): 拆分文件信息列表
            custom_prompt (str): 用户提供的预设prompt
            output_dir (str): 输出目录
            selected_indices (List[int], optional): 指定处理的文件索引
            thinking_budget (int): 思考预算，控制模型推理深度（-1为动态，0为关闭，默认32768）
        """
        if not self.api_key:
            print("❌ 错误：未提供Gemini API密钥")
            return
        
        if not self._client:
            print("❌ 错误：Gemini客户端未初始化。请先设置API密钥。")
            return

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 筛选要处理的文件
        if selected_indices:
            files_to_process = [f for f in split_files if f['index'] in selected_indices]
        else:
            files_to_process = split_files # 处理所有文件
        
        print(f"\n🤖 开始异步Gemini API处理...")
        print(f"📁 输出目录: {output_dir}")
        print(f"📋 处理文件数: {len(files_to_process)}")
        print(f"🔄 最大并发数: {self.max_concurrent_requests}")
        print("=" * 60)
        
        # 创建异步任务
        tasks = []
        for file_info in files_to_process:
            # 将thinking_budget添加到文件信息中
            file_info_with_budget = file_info.copy()
            file_info_with_budget['thinking_budget'] = thinking_budget

            task = asyncio.create_task(
                self.process_single_file_async(file_info_with_budget, custom_prompt, output_dir)
            )
            tasks.append(task)
        
        # 并发执行所有任务，同时显示进度
        completed = 0
        successful = 0
        failed = 0
        
        # 使用 asyncio.as_completed 来获取完成的任务
        for coro in asyncio.as_completed(tasks):
            result = await coro
            completed += 1
            
            if result['success']:
                successful += 1
                print(f"✅ [{result['file_info']['index']:02d}/{len(files_to_process)}] "
                      f"{result['file_info']['title']} -> {result['output_file']}")
            else:
                failed += 1
                print(f"❌ [{result['file_info']['index']:02d}/{len(files_to_process)}] "
                      f"{result['file_info']['title']}: {result['error']}")
            
            # 显示进度
            print(f"📊 进度: {completed}/{len(files_to_process)} "
                  f"(成功: {successful}, 失败: {failed})")
        
        print("\n" + "=" * 60)
        print(f"🎉 异步处理完成！")
        print(f"✅ 成功: {successful} 个文件")
        print(f"❌ 失败: {failed} 个文件")
        print(f"📁 输出目录: {output_dir}")
        print(f"⚡ 并发处理提升了处理速度")



async def main_async():
    """
    异步主函数
    """
    # 配置参数
    PDF_PATH = sys.argv[1]  # 请替换为您的PDF文件路径
    MAX_CONCURRENT_REQUESTS = 10  # 最大并发请求数，可根据API限制调整
    THINKING_BUDGET = 32768  # 思考预算，控制模型推理深度（-1为动态，0为关闭）

    # 预设prompt
    CUSTOM_PROMPT = """
    将pdf内容转换为markdown，注意下面记号格式：

    1. 忽略页眉和页脚上的内容；忽略pdf内容中装饰性图案。
    2. 标题级别
      - 章标题：2级比如## 第七章 相似标准型
      - 节标题：3级比如### § 6.4 特征值的估计
      - 节的习题标题：3级比如### 习题 6.4
      - 历史与展望、复习题X：3级标题比如### 复习题六
    3. 数学记号分隔符
      - 行内公式：使用$，**分隔符和公式之间**不要使用空格，比如$\lambda$（不要使用$ \lambda $这样的错误格式）。
      - 行间公式：使用$$，分隔符和公式块之间换行，比如$$\n\frac{1}{n}\n$$。对于多行递推公式，保留与课件中一致的的等号对齐格式。
    4. 使用**加粗强调的内容：定义、引理、定理、推论、注、例，包含相应的序号。
    5. 仅当pdf内容中的行间公式存在公式编号时，转换相应的公式编号，使用` \tag{xxx}`在行间公式末尾追加，比如$$\n\frac{1}{n} \tag{7.1.2}\n$$。
    6. 使用 ``` 包裹代码块。
    7. 不要使用HTML标签。
    8. 严格按照原始pdf内容图像进行转换，确保markdown内容完整一致，**不要进行**解释、修改、省略。

    此外，注意结合语境含义来理解pdf文档中的图像内容，从而更准确地识别不清晰文字或数学记号，比如区分 a 与 \alpha。
    """
    
    # 创建处理器
    processor = PDFSplitterWithGemini(max_concurrent_requests=MAX_CONCURRENT_REQUESTS)
    
    try:
        # 第一步：拆分PDF（同步操作）
        split_files = processor.split_pdf_by_chapters(PDF_PATH)
        
        if not split_files:
            print("❌ 拆分失败，程序退出")
            return
        
        # 第二步：预览任务
        processor.preview_split_tasks(split_files, CUSTOM_PROMPT)
        
        # 第三步：用户确认
        continue_processing, selected_indices = processor.confirm_api_processing()
        
        if continue_processing:
            # 第四步：设置API密钥并异步处理
            api_key = input("\n🔑 请输入您的Gemini API密钥: ").strip()
            if not api_key:
                print("❌ 未提供API密钥，退出")
                return
            
            processor.api_key = api_key
            
            # 异步并发处理
            start_time = time.time()
            await processor.process_with_gemini_api_async(
                split_files,
                CUSTOM_PROMPT,
                selected_indices=selected_indices,
                thinking_budget=THINKING_BUDGET
            )
            
            end_time = time.time()
            print(f"\n⚡ 总处理时间: {end_time - start_time:.2f}秒")
        
    except KeyboardInterrupt:
        print("\n\n✋ 操作被用户中断，已取消API处理。")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

def main():
    """
    主函数 - 使用异步处理
    """
    # 运行异步主函数
    asyncio.run(main_async())

if __name__ == "__main__":
    print("🚀 异步PDF拆分与Gemini API处理工具")
    print("=" * 50)

    if len(sys.argv) < 2:
        print("❌ 使用方法: python async_pdf_splitter_gemini.py <你的pdf文件路径>")
        print("💡 安装依赖: pip install -r requirements.txt")
        sys.exit(1)

    main()