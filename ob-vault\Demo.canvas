{"nodes": [{"id": "0989b1d6706eae3a", "type": "text", "text": "", "x": 40, "y": 220, "width": 250, "height": 60}, {"id": "b2c7672b65275aeb", "type": "file", "file": "react-foundations/7_updating-state.md", "x": -440, "y": 50, "width": 400, "height": 400}, {"id": "d5f283c62532c34f", "type": "text", "text": "最多播放\n【Obsidian】一个不能错过的Obsidian白板插件|Advanced Canvas\n1万\n0\n04:19\n【Obsidian】一个不能错过的Obsidian白板插件|Advanced Canvas\n负性玻色子\n· 01-14\nObsidian白板：笔记新维度，释放创意潜力！后半段很精彩！一定要看，不要叛逆！\n3.7万\n214\n43:39\nObsidian白板：笔记新维度，释放创意潜力！后半段很精彩！一定要看，不要叛逆！\nChris就是Chris\n· 2023-12-01\n\n![[26王道数据结构/01_目录.pdf#page=3&rect=78,671,223,696|01_目录, p.3]]", "x": -440, "y": -510, "width": 594, "height": 361}, {"id": "ae89ce0b7f1514f0", "type": "text", "text": " 我们没有什么可以", "x": 260, "y": -510, "width": 250, "height": 60}, {"id": "f51734a8bbec674a", "type": "text", "text": "$\\lambda$ 是一个可以联动的", "x": 260, "y": -300, "width": 250, "height": 60, "color": "3"}, {"id": "b946b6fd184a7839", "type": "text", "text": "", "x": 135, "y": 280, "width": 250, "height": 60}], "edges": [{"id": "59d01da11e9471a5", "fromNode": "f51734a8bbec674a", "fromSide": "top", "toNode": "ae89ce0b7f1514f0", "toSide": "bottom"}, {"id": "1e485a08aaf2dbcd", "fromNode": "b2c7672b65275aeb", "fromSide": "right", "toNode": "0989b1d6706eae3a", "toSide": "left"}, {"id": "f8e9d946868f0e4d", "fromNode": "d5f283c62532c34f", "fromSide": "right", "toNode": "ae89ce0b7f1514f0", "toSide": "bottom"}]}