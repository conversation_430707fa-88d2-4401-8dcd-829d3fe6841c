{"cells": [{"cell_type": "markdown", "id": "83586de8", "metadata": {}, "source": ["AUTODL ENV: PyTorch / 2.7.0 / 3.12(ubuntu22.04) / 12.8GB\n", "MODEL_REQUIREMENTS: transformers>=4.52.0 torch>=2.6.0 peft>=0.15.2 torchvision pillow\n", "\n", "7/12 21:03\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c176560f", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ['https_proxy'] = 'http://localhost:7897'"]}, {"cell_type": "code", "execution_count": null, "id": "91b97ac1", "metadata": {}, "outputs": [], "source": ["!pip install git+https://github.com/illuin-tech/colpali"]}, {"cell_type": "code", "execution_count": null, "id": "e3551ea7", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9ff632e07c054fd4849153939029e44d", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 2 files:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d320d017374a48f1bd9614f92202c429", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "66b9a070319d47009e8081a0d95e5820", "version_major": 2, "version_minor": 0}, "text/plain": ["adapter_model.safetensors:   0%|          | 0.00/959M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniforge3\\envs\\vdr-colembed\\Lib\\site-packages\\huggingface_hub\\file_download.py:143: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--tsystems--colqwen2.5-3b-multilingual-v1.0. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.\n", "To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development\n", "  warnings.warn(message)\n"]}], "source": ["import torch\n", "from PIL import Image\n", "\n", "from colpali_engine.models import ColQwen2_5, ColQwen2_5_Processor\n", "\n", "model = ColQwen2_5.from_pretrained(\n", "        \"tsystems/colqwen2.5-3b-multilingual-v1.0\",\n", "        torch_dtype=torch.bfloat16,\n", "        device_map=\"cuda:0\",  # or \"mps\" if on Apple Silicon\n", "    ).eval()\n"]}, {"cell_type": "code", "execution_count": 6, "id": "d30618a7", "metadata": {}, "outputs": [], "source": ["processor = ColQwen2_5_Processor.from_pretrained(\"tsystems/colqwen2.5-3b-multilingual-v1.0\")\n", "\n", "# Your inputs\n", "# images = [\n", "#     Image.new(\"RGB\", (32, 32), color=\"white\"),\n", "#     Image.new(\"RGB\", (16, 16), color=\"black\"),\n", "# ]\n", "# queries = [\n", "#     \"Is attention really all you need?\",\n", "#     \"What is the amount of bananas farmed in Salvador?\",\n", "# ]\n", "\n", "import requests\n", "from PIL import Image\n", "import io\n", "\n", "def download_image(url):\n", "    \"\"\"下载图片并返回PIL Image对象\"\"\"\n", "    response = requests.get(url, headers={\"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64)\"})\n", "    response.raise_for_status()\n", "    return Image.open(io.BytesIO(response.content))\n", "\n", "# Configure truncate_dim, max_length (for texts), max_pixels (for images), vector_type, batch_size in the encode function if needed\n", "\n", "# Encode query\n", "queries =[\n", "    'How much percentage of Germanys population died in the 2nd World War?',\n", "    'How many million tons CO2 were captured from Gas processing in 2018?',\n", "    'What is the average CO2 emission of someone in Japan?'\n", "]\n", "\n", "# Encode image/document\n", "images=[\n", "    download_image('https://wiki-upload.yayeah.xyz/wikipedia/commons/3/35/Human_losses_of_world_war_two_by_country.png'),\n", "    download_image('https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/7/76/20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg/2560px-20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg.png'),\n", "    download_image('https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/f/f3/20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg/2880px-20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg.png')\n", "]\n", "\n", "# Process the inputs\n", "batch_images = processor.process_images(images).to(model.device)\n", "batch_queries = processor.process_queries(queries).to(model.device)\n", "\n", "# Forward pass\n", "with torch.no_grad():\n", "    image_embeddings = model(**batch_images)\n", "    query_embeddings = model(**batch_queries)\n", "\n", "scores = processor.score_multi_vector(query_embeddings, image_embeddings)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "52b3911c", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[19.2500,  6.7500,  9.9375],\n", "        [ 9.2500, 21.7500, 11.8750],\n", "        [13.1875, 10.0000, 18.1250]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["scores"]}, {"cell_type": "code", "execution_count": null, "id": "5e983282", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "vdr-colembed", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}