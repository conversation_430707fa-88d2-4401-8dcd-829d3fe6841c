App Router: Fetching Data | Next.js

===============
[Skip to content](https://nextjs.org/learn/dashboard-app/fetching-data#geist-skip-nav)

[](https://vercel.com/home?utm_source=next-site&utm_medium=banner&utm_campaign=learn_dashboard-app_fetching-data "Go to Vercel homepage")

[![Image 1: Next.js uwu logo by SAWARATSUKI](https://nextjs.org/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1714730590%2Ffront%2Fnextjs%2Fuwu%2Fnext-uwu-logo.png&w=128&q=75)](https://nextjs.org/?uwu=true "Go to the homepage")

[](https://nextjs.org/ "Go to the homepage")

Search documentation...CtrlK Search...⌘K

[](https://vercel.com/home?utm_source=next-site&utm_medium=banner&utm_campaign=learn_dashboard-app_fetching-data "Go to Vercel homepage")

[![Image 2: Next.js uwu logo by SAWARATSUKI](https://nextjs.org/_next/image?url=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Fv1714730590%2Ffront%2Fnextjs%2Fuwu%2Fnext-uwu-logo.png&w=128&q=75)](https://nextjs.org/?uwu=true "Go to the homepage")

[](https://nextjs.org/ "Go to the homepage")

[Showcase](https://nextjs.org/showcase)[Docs](https://nextjs.org/docs "Documentation")[Blog](https://nextjs.org/blog)[Templates](https://vercel.com/templates/next.js?utm_source=next-site&utm_medium=navbar&utm_campaign=next_site_nav_templates)[Enterprise](https://vercel.com/contact/sales/nextjs?utm_source=next-site&utm_medium=navbar&utm_campaign=next_site_nav_enterprise)

Search documentation...CtrlK Search...⌘K[Deploy](https://vercel.com/new/clone?utm_source=next-site&utm_medium=banner&b=main&s=https%3A%2F%2Fgithub.com%2Fvercel%2Fvercel%2Ftree%2Fmain%2Fexamples%2Fnextjs&showOptionalTeamCreation=false&template=nextjs&teamCreateStatus=hidden&utm_campaign=learn_dashboard-app_fetching-data)[Learn](https://nextjs.org/learn)

Chapter 7

Fetching Data

[Sign in](https://nextjs.org/api/auth/authorize?slug=dashboard-app/fetching-data)

[Sign in to save progress](https://nextjs.org/api/auth/authorize?slug=dashboard-app/fetching-data)

7

Chapter 7

Fetching Data
=============

Now that you've created and seeded your database, let's discuss the different ways you can fetch data for your application, and build out your dashboard overview page.

In this chapter...

Here are the topics we'll cover

![Image 3](https://nextjs.org/_next/static/media/database.4700ee8c.svg)

Learn about some approaches to fetching data: APIs, ORMs, SQL, etc.

![Image 4](https://nextjs.org/_next/static/media/servers.5ea19712.svg)

How Server Components can help you access back-end resources more securely.

![Image 5](https://nextjs.org/_next/static/media/alignment-right.52e7a4ea.svg)

What network waterfalls are.

![Image 6](https://nextjs.org/_next/static/media/connection.d7666587.svg)

How to implement parallel data fetching using a JavaScript Pattern.

[Choosing how to fetch data](https://nextjs.org/learn/dashboard-app/fetching-data#choosing-how-to-fetch-data)
-------------------------------------------------------------------------------------------------------------

### [API layer](https://nextjs.org/learn/dashboard-app/fetching-data#api-layer)

APIs are an intermediary layer between your application code and database. There are a few cases where you might use an API:

*   If you're using third-party services that provide an API.
*   If you're fetching data from the client, you want to have an API layer that runs on the server to avoid exposing your database secrets to the client.

In Next.js, you can create API endpoints using [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers).

### [Database queries](https://nextjs.org/learn/dashboard-app/fetching-data#database-queries)

When you're creating a full-stack application, you'll also need to write logic to interact with your database. For [relational databases](https://aws.amazon.com/relational-database/) like Postgres, you can do this with SQL or with an [ORM](https://vercel.com/docs/storage/vercel-postgres/using-an-orm).

There are a few cases where you have to write database queries:

*   When creating your API endpoints, you need to write logic to interact with your database.
*   If you are using React Server Components (fetching data on the server), you can skip the API layer, and query your database directly without risking exposing your database secrets to the client.

### It's time to take a quiz!

Test your knowledge and see what you've just learned.

In which of these scenarios should you not query your database directly?

A

When you're fetching data on the client

B

When you're fetching data on the server

C

When you're creating your own API layer to interact with your database

Check Answer

Let's learn more about React Server Components.

### [Using Server Components to fetch data](https://nextjs.org/learn/dashboard-app/fetching-data#using-server-components-to-fetch-data)

By default, Next.js applications use **React Server Components**. Fetching data with Server Components is a relatively new approach and there are a few benefits of using them:

*   Server Components support JavaScript Promises, providing a solution for asynchronous tasks like data fetching natively. You can use `async/await` syntax without needing `useEffect`, `useState` or other data fetching libraries.
*   Server Components run on the server, so you can keep expensive data fetches and logic on the server, only sending the result to the client.
*   Since Server Components run on the server, you can query the database directly without an additional API layer. This saves you from writing and maintaining additional code.

### It's time to take a quiz!

Test your knowledge and see what you've just learned.

What's one advantage of using React Server Components to fetch data?

A

They automatically protect you from SQL injection.

B

They allow you to query the database directly from the server without an additional API layer.

C

They require you to use an API layer and create endpoints.

Check Answer

### [Using SQL](https://nextjs.org/learn/dashboard-app/fetching-data#using-sql)

For your dashboard application, you'll write database queries using the [postgres.js](https://github.com/porsager/postgres) library and SQL. There are a few reasons why we'll be using SQL:

*   SQL is the industry standard for querying relational databases (e.g. ORMs generate SQL under the hood).
*   Having a basic understanding of SQL can help you understand the fundamentals of relational databases, allowing you to apply your knowledge to other tools.
*   SQL is versatile, allowing you to fetch and manipulate specific data.
*   The `postgres.js` library provides protection against [SQL injections](https://github.com/porsager/postgres?tab=readme-ov-file#query-parameters).

Don't worry if you haven't used SQL before - we have provided the queries for you.

Go to `/app/lib/data.ts`. Here you'll see that we're using `postgres`. The `sql`[function](https://github.com/porsager/postgres) allows you to query your database:

/app/lib/data.ts

```
import postgres from 'postgres';
 
const sql = postgres(process.env.POSTGRES_URL!, { ssl: 'require' });
 
// ...
```

You can call `sql` anywhere on the server, like a Server Component. But to allow you to navigate the components more easily, we've kept all the data queries in the `data.ts` file, and you can import them into the components.

### It's time to take a quiz!

Test your knowledge and see what you've just learned.

What does SQL allow you to do in terms of fetching data?

A

Fetch all your data indiscriminately

B

Fetch and manipulate specific data

C

Automatically cache data for better performance

D

Change the database schema on the fly

Check Answer

> **Note:** If you used your own database provider in Chapter 6, you'll need to update the database queries to work with your provider. You can find the queries in `/app/lib/data.ts`.

[Fetching data for the dashboard overview page](https://nextjs.org/learn/dashboard-app/fetching-data#fetching-data-for-the-dashboard-overview-page)
---------------------------------------------------------------------------------------------------------------------------------------------------

Now that you understand different ways of fetching data, let's fetch data for the dashboard overview page. Navigate to `/app/dashboard/page.tsx`, paste the following code, and spend some time exploring it:

/app/dashboard/page.tsx

```
import { Card } from '@/app/ui/dashboard/cards';
import RevenueChart from '@/app/ui/dashboard/revenue-chart';
import LatestInvoices from '@/app/ui/dashboard/latest-invoices';
import { lusitana } from '@/app/ui/fonts';
 
export default async function Page() {
  return (
    <main>
      <h1 className={`${lusitana.className} mb-4 text-xl md:text-2xl`}>
        Dashboard
      </h1>
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {/* <Card title="Collected" value={totalPaidInvoices} type="collected" /> */}
        {/* <Card title="Pending" value={totalPendingInvoices} type="pending" /> */}
        {/* <Card title="Total Invoices" value={numberOfInvoices} type="invoices" /> */}
        {/* <Card
          title="Total Customers"
          value={numberOfCustomers}
          type="customers"
        /> */}
      </div>
      <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-4 lg:grid-cols-8">
        {/* <RevenueChart revenue={revenue}  /> */}
        {/* <LatestInvoices latestInvoices={latestInvoices} /> */}
      </div>
    </main>
  );
}
```

The code above is intentionally commented out. We will now begin to example each piece.

*   The `page` is an **async** server component. This allows you to use `await` to fetch data.
*   There are also 3 components which receive data: `<Card>`, `<RevenueChart>`, and `<LatestInvoices>`. They are currently commented out and not yet implemented.

[Fetching data for **`<RevenueChart/>`**](https://nextjs.org/learn/dashboard-app/fetching-data#fetching-data-for-revenuechart)
------------------------------------------------------------------------------------------------------------------------------

To fetch data for the `<RevenueChart/>` component, import the `fetchRevenue` function from `data.ts` and call it inside your component:

/app/dashboard/page.tsx

```
import { Card } from '@/app/ui/dashboard/cards';
import RevenueChart from '@/app/ui/dashboard/revenue-chart';
import LatestInvoices from '@/app/ui/dashboard/latest-invoices';
import { lusitana } from '@/app/ui/fonts';
import { fetchRevenue } from '@/app/lib/data';
 
export default async function Page() {
  const revenue = await fetchRevenue();
  // ...
}
```

Next, let's do the following:

1.   Uncomment the `<RevenueChart/>` component.
2.   Navigate to the component file (`/app/ui/dashboard/revenue-chart.tsx`) and uncomment the code inside it.
3.   Check `localhost:3000` and you should see a chart that uses `revenue` data.

![Image 7: Revenue chart showing the total revenue for the last 12 months](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Frecent-revenue.png&w=1920&q=75)![Image 8: Revenue chart showing the total revenue for the last 12 months](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Frecent-revenue.png&w=1920&q=75)
Let's continue importing more data and displaying it on the dashboard.

[Fetching data for **`<LatestInvoices/>`**](https://nextjs.org/learn/dashboard-app/fetching-data#fetching-data-for-latestinvoices)
----------------------------------------------------------------------------------------------------------------------------------

For the `<LatestInvoices />` component, we need to get the latest 5 invoices, sorted by date.

You could fetch all the invoices and sort through them using JavaScript. This isn't a problem as our data is small, but as your application grows, it can significantly increase the amount of data transferred on each request and the JavaScript required to sort through it.

Instead of sorting through the latest invoices in-memory, you can use an SQL query to fetch only the last 5 invoices. For example, this is the SQL query from your `data.ts` file:

/app/lib/data.ts

```
// Fetch the last 5 invoices, sorted by date
const data = await sql<LatestInvoiceRaw[]>`
  SELECT invoices.amount, customers.name, customers.image_url, customers.email
  FROM invoices
  JOIN customers ON invoices.customer_id = customers.id
  ORDER BY invoices.date DESC
  LIMIT 5`;
```

In your page, import the `fetchLatestInvoices` function:

/app/dashboard/page.tsx

```
import { Card } from '@/app/ui/dashboard/cards';
import RevenueChart from '@/app/ui/dashboard/revenue-chart';
import LatestInvoices from '@/app/ui/dashboard/latest-invoices';
import { lusitana } from '@/app/ui/fonts';
import { fetchRevenue, fetchLatestInvoices } from '@/app/lib/data';
 
export default async function Page() {
  const revenue = await fetchRevenue();
  const latestInvoices = await fetchLatestInvoices();
  // ...
}
```

Then, uncomment the `<LatestInvoices />` component. You will also need to uncomment the relevant code in the `<LatestInvoices />` component itself, located at `/app/ui/dashboard/latest-invoices`.

If you visit your localhost, you should see that only the last 5 are returned from the database. Hopefully, you're beginning to see the advantages of querying your database directly!

![Image 9: Latest invoices component alongside the revenue chart](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Flatest-invoices.png&w=1920&q=75)![Image 10: Latest invoices component alongside the revenue chart](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Flatest-invoices.png&w=1920&q=75)
[Practice: Fetch data for the `<Card>` components](https://nextjs.org/learn/dashboard-app/fetching-data#practice-fetch-data-for-the-card-components)
----------------------------------------------------------------------------------------------------------------------------------------------------

Now it's your turn to fetch data for the `<Card>` components. The cards will display the following data:

*   Total amount of invoices collected.
*   Total amount of invoices pending.
*   Total number of invoices.
*   Total number of customers.

Again, you might be tempted to fetch all the invoices and customers, and use JavaScript to manipulate the data. For example, you could use `Array.length` to get the total number of invoices and customers:

```
const totalInvoices = allInvoices.length;
const totalCustomers = allCustomers.length;
```

But with SQL, you can fetch only the data you need. It's a little longer than using `Array.length`, but it means less data needs to be transferred during the request. This is the SQL alternative:

/app/lib/data.ts

```
const invoiceCountPromise = sql`SELECT COUNT(*) FROM invoices`;
const customerCountPromise = sql`SELECT COUNT(*) FROM customers`;
```

The function you will need to import is called `fetchCardData`. You will need to destructure the values returned from the function.

> **Hint:**
> 
> 
> *   Check the card components to see what data they need.
> *   Check the `data.ts` file to see what the function returns.

Once you're ready, expand the toggle below for the final code:

Reveal the solution

Great! You've now fetched all the data for the dashboard overview page. Your page should look like this:

![Image 11: Dashboard page with all the data fetched](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Fcomplete-dashboard.png&w=1920&q=75)![Image 12: Dashboard page with all the data fetched](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Fcomplete-dashboard.png&w=1920&q=75)
However... there are two things you need to be aware of:

1.   The data requests are unintentionally blocking each other, creating a **request waterfall**.
2.   By default, Next.js **prerenders** routes to improve performance, this is called **Static Rendering**. So if your data changes, it won't be reflected in your dashboard.

Let's discuss number 1 in this chapter, then look into detail at number 2 in the next chapter.

[What are request waterfalls?](https://nextjs.org/learn/dashboard-app/fetching-data#what-are-request-waterfalls)
----------------------------------------------------------------------------------------------------------------

A "waterfall" refers to a sequence of network requests that depend on the completion of previous requests. In the case of data fetching, each request can only begin once the previous request has returned data.

![Image 13: Diagram showing time with sequential data fetching and parallel data fetching](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Flight%2Fsequential-parallel-data-fetching.png&w=3840&q=75)![Image 14: Diagram showing time with sequential data fetching and parallel data fetching](https://nextjs.org/_next/image?url=https%3A%2F%2Fh8DxKfmAPhn8O0p3.public.blob.vercel-storage.com%2Flearn%2Fdark%2Fsequential-parallel-data-fetching.png&w=3840&q=75)
For example, we need to wait for `fetchRevenue()` to execute before `fetchLatestInvoices()` can start running, and so on.

/app/dashboard/page.tsx

```
const revenue = await fetchRevenue();
const latestInvoices = await fetchLatestInvoices(); // wait for fetchRevenue() to finish
const {
  numberOfInvoices,
  numberOfCustomers,
  totalPaidInvoices,
  totalPendingInvoices,
} = await fetchCardData(); // wait for fetchLatestInvoices() to finish
```

This pattern is not necessarily bad. There may be cases where you want waterfalls because you want a condition to be satisfied before you make the next request. For example, you might want to fetch a user's ID and profile information first. Once you have the ID, you might then proceed to fetch their list of friends. In this case, each request is contingent on the data returned from the previous request.

However, this behavior can also be unintentional and impact performance.

### It's time to take a quiz!

Test your knowledge and see what you've just learned.

When might you want to use a waterfall pattern?

A

To satisfy a condition before making the next request

B

To make all requests simultaneously

C

To reduce the server load by doing one fetch at a time

Check Answer

[Parallel data fetching](https://nextjs.org/learn/dashboard-app/fetching-data#parallel-data-fetching)
-----------------------------------------------------------------------------------------------------

A common way to avoid waterfalls is to initiate all data requests at the same time - in parallel.

In JavaScript, you can use the [`Promise.all()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/all) or [`Promise.allSettled()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/allSettled) functions to initiate all promises at the same time. For example, in `data.ts`, we're using `Promise.all()` in the `fetchCardData()` function:

/app/lib/data.ts

```
export async function fetchCardData() {
  try {
    const invoiceCountPromise = sql`SELECT COUNT(*) FROM invoices`;
    const customerCountPromise = sql`SELECT COUNT(*) FROM customers`;
    const invoiceStatusPromise = sql`SELECT
         SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) AS "paid",
         SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) AS "pending"
         FROM invoices`;
 
    const data = await Promise.all([
      invoiceCountPromise,
      customerCountPromise,
      invoiceStatusPromise,
    ]);
    // ...
  }
}
```

By using this pattern, you can:

*   Start executing all data fetches at the same time, which is faster than waiting for each request to complete in a waterfall.
*   Use a native JavaScript pattern that can be applied to any library or framework.

However, there is one **disadvantage** of relying only on this JavaScript pattern: what happens if one data request is slower than all the others? Let's find out more in the next chapter.

7

You've Completed Chapter 7
--------------------------

You've learned about some of the different ways to fetch data in Next.js.

Next Up

8: Static and Dynamic Rendering

Learn about the different rendering modes in Next.js.

[Start Chapter 8](https://nextjs.org/learn/dashboard-app/static-and-dynamic-rendering)

Was this helpful?

supported.

Send

[](https://vercel.com/home?utm_source=next-site&utm_medium=footer&utm_campaign=next-website "Go to the Vercel website")

[](https://github.com/vercel/next.js)

* * *

[](https://x.com/nextjs)

* * *

[](https://bsky.app/profile/nextjs.org)

#### Resources

[Docs](https://nextjs.org/docs)[Support Policy](https://nextjs.org/support-policy)[Learn](https://nextjs.org/learn)[Showcase](https://nextjs.org/showcase)[Blog](https://nextjs.org/blog)[Team](https://nextjs.org/team)[Analytics](https://vercel.com/analytics?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_fetching-data)[Next.js Conf](https://nextjs.org/conf)[Previews](https://vercel.com/products/previews?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_fetching-data)

#### More

[Next.js Commerce](https://vercel.com/templates/next.js/nextjs-commerce?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_fetching-data)[Contact Sales](https://vercel.com/contact/sales?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_fetching-data)[Community](https://community.vercel.com/)[GitHub](https://github.com/vercel/next.js)[Releases](https://github.com/vercel/next.js/releases)[Telemetry](https://nextjs.org/telemetry)[Governance](https://nextjs.org/governance)

#### About Vercel

[Next.js + Vercel](https://vercel.com/solutions/nextjs?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_fetching-data)[Open Source Software](https://vercel.com/oss?utm_source=next-site&utm_medium=footer&utm_campaign=learn_dashboard-app_fetching-data)[GitHub](https://github.com/vercel)[Bluesky](https://bsky.app/profile/vercel.com)[X](https://x.com/vercel)

#### Legal

[Privacy Policy](https://vercel.com/legal/privacy-policy)Cookie Preferences

#### Subscribe to our newsletter

Stay updated on new releases and features, guides, and case studies.

Subscribe

© 2025 Vercel, Inc.

[](https://github.com/vercel/next.js)

* * *

[](https://x.com/nextjs)

* * *

[](https://bsky.app/profile/nextjs.org)