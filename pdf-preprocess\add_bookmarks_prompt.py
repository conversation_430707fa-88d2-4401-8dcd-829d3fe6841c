import fitz  # pymupdf
import sys
import os

def add_bookmarks_to_pdf(pdf_path, output_path, bookmark_data, page_offset=0):
    """
    向PDF添加书签目录结构
    
    Args:
        pdf_path: 原始PDF文件路径
        output_path: 输出PDF文件路径
        bookmark_data: 书签数据列表
        page_offset: 页码偏移量（实际页码 + offset = PDF页码）
    """
    doc = fitz.open(pdf_path)
    
    # 清除现有书签（可选）
    # doc.delete_toc()
    
    # 构建新的目录结构
    toc = []
    
    for item in bookmark_data:
        level = item['level']
        title = item['title']
        # 应用页码偏移，并确保页码从0开始（PDF标准）
        pdf_page = item['page'] + page_offset - 1
        # 确保页码不小于0
        pdf_page = max(0, pdf_page)
        
        toc.append([level, title, pdf_page])
    
    # 设置目录
    doc.set_toc(toc)
    
    # 保存PDF
    doc.save(output_path, garbage=4, clean=True)
    doc.close()
    
    print(f"已成功添加书签到PDF: {output_path}")
    print(f"使用页码偏移: {page_offset}")

def calculate_page_offset(actual_page, pdf_page_index):
    """
    计算页码偏移量
    
    Args:
        actual_page: 书中显示的实际页码
        pdf_page_index: 对应的PDF页面索引（从0开始）
    
    Returns:
        页码偏移量
    """
    return pdf_page_index - actual_page + 1

# 完整的书签数据（保持title中的页码不变）
bookmark_data = [
    # Chapter 1: Functions, Limits, and Continuity
    {'level': 1, 'title': '第一章 函数极限连续 1', 'page': 1},
    {'level': 2, 'title': '1.1 函数 2', 'page': 1},
    {'level': 3, 'title': '考试内容概要 2', 'page': 2},
    {'level': 3, 'title': '一、函数的概念及常见函数 2', 'page': 2},
    {'level': 3, 'title': '二、函数的性质 5', 'page': 5},
    {'level': 3, 'title': '常考题型与典型例题 7', 'page': 7},
    {'level': 2, 'title': '1.2 极限 9', 'page': 9},
    {'level': 3, 'title': '考试内容概要 9', 'page': 9},
    {'level': 3, 'title': '一、极限的概念 9', 'page': 9},
    {'level': 3, 'title': '二、极限的性质 14', 'page': 14},
    {'level': 3, 'title': '三、极限的存在准则 15', 'page': 15},
    {'level': 3, 'title': '四、无穷小量 18', 'page': 18},
    {'level': 3, 'title': '五、无穷大量 19', 'page': 19},
    {'level': 3, 'title': '常考题型与典型例题 22', 'page': 22},
    {'level': 2, 'title': '1.3 函数的连续性 51', 'page': 51},
    {'level': 3, 'title': '考试内容概要 51', 'page': 51},
    {'level': 3, 'title': '一、连续性的概念 51', 'page': 51},
    {'level': 3, 'title': '二、间断点及其分类 54', 'page': 54},
    {'level': 3, 'title': '三、连续性的运算与性质 56', 'page': 56},
    {'level': 3, 'title': '四、闭区间上连续函数的性质 56', 'page': 56},
    {'level': 3, 'title': '常考题型与典型例题 56', 'page': 56},

    # Chapter 2: Derivatives and Differentials
    {'level': 1, 'title': '第二章 导数与微分 61', 'page': 61},
    {'level': 2, 'title': '2.1 导数与微分 61', 'page': 61},
    {'level': 3, 'title': '考试内容概要 61', 'page': 61},
    {'level': 3, 'title': '一、导数与微分的概念 61', 'page': 61},
    {'level': 3, 'title': '二、导数公式及求导法则 68', 'page': 68},
    {'level': 3, 'title': '三、高阶导数 73', 'page': 73},
    {'level': 3, 'title': '常考题型与典型例题 74', 'page': 74},

    # Chapter 3: Mean Value Theorem and Applications of Derivatives
    {'level': 1, 'title': '第三章 微分中值定理及导数应用 83', 'page': 83},
    {'level': 2, 'title': '3.1 微分中值定理及导数应用 83', 'page': 83},
    {'level': 3, 'title': '考试内容概要 83', 'page': 83},
    {'level': 3, 'title': '一、微分中值定理 83', 'page': 83},
    {'level': 3, 'title': '二、导数应用 85', 'page': 85},
    {'level': 3, 'title': '常考题型与典型例题 92', 'page': 92},

    # Chapter 4: Indefinite Integrals
    {'level': 1, 'title': '第四章 不定积分 105', 'page': 105},
    {'level': 2, 'title': '4.1 不定积分 105', 'page': 105},
    {'level': 3, 'title': '考试内容概要 105', 'page': 105},
    {'level': 3, 'title': '一、不定积分的概念与性质 105', 'page': 105},
    {'level': 3, 'title': '二、不定积分基本公式 107', 'page': 107},
    {'level': 3, 'title': '三、三种主要积分法 108', 'page': 108},
    {'level': 3, 'title': '四、三类常见可积函数积分 115', 'page': 115},
    {'level': 3, 'title': '常考题型与典型例题 120', 'page': 120},

    # Chapter 5: Definite Integrals and Improper Integrals
    {'level': 1, 'title': '第五章 定积分与反常积分 127', 'page': 127},
    {'level': 2, 'title': '5.1 定积分 127', 'page': 127},
    {'level': 3, 'title': '考试内容概要 127', 'page': 127},
    {'level': 3, 'title': '一、定积分的概念 127', 'page': 127},
    {'level': 3, 'title': '二、定积分的性质 129', 'page': 129},
    {'level': 3, 'title': '三、积分上限的函数 129', 'page': 129},
    {'level': 3, 'title': '四、定积分的计算 130', 'page': 130},
    {'level': 3, 'title': '常考题型与典型例题 131', 'page': 131},
    {'level': 2, 'title': '5.2 反常积分 146', 'page': 146},
    {'level': 3, 'title': '考试内容概要 146', 'page': 146},
    {'level': 3, 'title': '一、无穷区间上的反常积分 146', 'page': 146},
    {'level': 3, 'title': '二、无界函数的反常积分 148', 'page': 148},
    {'level': 3, 'title': '常考题型与典型例题 150', 'page': 150},

    # Chapter 6: Applications of Definite Integrals
    {'level': 1, 'title': '第六章 定积分的应用 154', 'page': 154},
    {'level': 2, 'title': '6.1 定积分的应用 154', 'page': 154},
    {'level': 3, 'title': '考试内容概要 154', 'page': 154},
    {'level': 3, 'title': '一、几何应用 154', 'page': 154},
    {'level': 3, 'title': '二、物理应用(教学三不要求) 155', 'page': 155},
    {'level': 3, 'title': '常考题型与典型例题 155', 'page': 155},

    # Chapter 7: Differential Equations
    {'level': 1, 'title': '第七章 微分方程 162', 'page': 162},
    {'level': 2, 'title': '7.1 微分方程 162', 'page': 162},
    {'level': 3, 'title': '考试内容概要 163', 'page': 163},
    {'level': 3, 'title': '一、常微分方程的基本概念 163', 'page': 163},
    {'level': 3, 'title': '二、一阶微分方程 163', 'page': 163},
    {'level': 3, 'title': '三、可降阶的高阶方程( 三不要求) 167', 'page': 167},
    {'level': 3, 'title': '四、高阶线性微分方程 168', 'page': 168},
    {'level': 3, 'title': '常考题型与典型例题 176', 'page': 176},

    # Chapter 8: Differential Calculus of Multivariable Functions
    {'level': 1, 'title': '第八章 多元函数微分学 186', 'page': 186},
    {'level': 2, 'title': '8.1 多元函数的基本概念 186', 'page': 186},
    {'level': 3, 'title': '考试内容概要 186', 'page': 186},
    {'level': 3, 'title': '一、多元函数的极限 186', 'page': 186},
    {'level': 3, 'title': '二、多元函数的连续性 188', 'page': 188},
    {'level': 3, 'title': '三、偏导数 188', 'page': 188},
    {'level': 3, 'title': '四、全微分 190', 'page': 190},
    {'level': 3, 'title': '常考题型与典型例题 190', 'page': 190},
    {'level': 2, 'title': '8.2 多元函数的微分法 195', 'page': 195},
    {'level': 3, 'title': '考试内容概要 195', 'page': 195},
    {'level': 3, 'title': '一、复合函数微分法 195', 'page': 195},
    {'level': 3, 'title': '二、隐函数微分法 196', 'page': 196},
    {'level': 3, 'title': '常考题型与典型例题 196', 'page': 196},
    {'level': 2, 'title': '8.3 多元函数的极值与最值 206', 'page': 206},
    {'level': 3, 'title': '考试内容概要 206', 'page': 206},
    {'level': 3, 'title': '一、无约束极值 206', 'page': 206},
    {'level': 3, 'title': '二、条件极值及拉格朗日乘数法 207', 'page': 207},
    {'level': 3, 'title': '三、最大最小值 208', 'page': 208},
    {'level': 3, 'title': '常考题型与典型例题 208', 'page': 208},

    # Chapter 9: Double Integrals
    {'level': 1, 'title': '第九章 二重积分 213', 'page': 213},
    {'level': 2, 'title': '9.1 二重积分 213', 'page': 213},
    {'level': 3, 'title': '考试内容概要 213', 'page': 213},
    {'level': 3, 'title': '一、二重积分的概念及性质 213', 'page': 213},
    {'level': 3, 'title': '二、二重积分的计算 214', 'page': 214},
    {'level': 3, 'title': '常考题型与典型例题 215', 'page': 215},

    # Chapter 10: Infinite Series
    {'level': 1, 'title': '第十章 无穷级数 223', 'page': 223},
    {'level': 2, 'title': '10.1 常数项级数 224', 'page': 223},
    {'level': 3, 'title': '考试内容概要 224', 'page': 224},
    {'level': 3, 'title': '一、级数的概念与性质 224', 'page': 224},
    {'level': 3, 'title': '二、级数的审敛准则 226', 'page': 226},
    {'level': 3, 'title': '常考题型与典型例题 229', 'page': 229},
    {'level': 2, 'title': '10.2 幂级数 232', 'page': 232},
    {'level': 3, 'title': '考试内容概要 232', 'page': 232},
    {'level': 3, 'title': '一、幂级数的收敛半径、收敛区间及收敛域 232', 'page': 232},
    {'level': 3, 'title': '二、幂级数的性质 233', 'page': 233},
    {'level': 3, 'title': '三、函数的幂级数展开 234', 'page': 234},
    {'level': 3, 'title': '常考题型与典型例题 235', 'page': 235},
    {'level': 2, 'title': '10.3 傅里叶级数 243', 'page': 243},
    {'level': 3, 'title': '考试内容概要 243', 'page': 243},
    {'level': 3, 'title': '一、傅里叶系数与傅里叶级数 243', 'page': 243},
    {'level': 3, 'title': '二、收敛定理(狄利克雷) 244', 'page': 244},
    {'level': 3, 'title': '三、周期为2π的函数的展开 244', 'page': 244},
    {'level': 3, 'title': '四、周期为2l的函数的展开 245', 'page': 245},
    {'level': 3, 'title': '常考题型与典型例题 245', 'page': 245},

    # Chapter 11: Vector Algebra, Spatial Analytic Geometry, and Geometric Applications of Multivariable Calculus
    {'level': 1, 'title': '第十一章 向量代数与空间解析几何及多元微分学在几何上的应用 250', 'page': 250},
    {'level': 2, 'title': '11.1 向量代数 250', 'page': 250},
    {'level': 3, 'title': '考试内容概要 250', 'page': 250},
    {'level': 3, 'title': '常考题型与典型例题 252', 'page': 252},
    {'level': 2, 'title': '11.2 空间平面与直线 252', 'page': 252},
    {'level': 3, 'title': '考试内容概要 252', 'page': 252},
    {'level': 3, 'title': '常考题型与典型例题 253', 'page': 253},
    {'level': 2, 'title': '11.3 曲面与空间曲线 257', 'page': 257},
    {'level': 3, 'title': '考试内容概要 257', 'page': 257},
    {'level': 3, 'title': '常考题型与典型例题 258', 'page': 258},
    {'level': 2, 'title': '11.4 多元微分学在几何上的应用 260', 'page': 260},
    {'level': 3, 'title': '考试内容概要 260', 'page': 260},
    {'level': 3, 'title': '常考题型与典型例题 261', 'page': 261},

    # Chapter 12: Multiple Integrals and Their Applications
    {'level': 1, 'title': '第十二章 多元积分学及其应用 264', 'page': 264},
    {'level': 2, 'title': '12.1 三重积分 264', 'page': 264},
    {'level': 3, 'title': '考试内容概要 264', 'page': 264},
    {'level': 3, 'title': '三重积分 264', 'page': 264},
    {'level': 3, 'title': '常考题型与典型例题 266', 'page': 266},
    {'level': 2, 'title': '12.2 曲线积分 269', 'page': 269},
    {'level': 3, 'title': '考试内容概要 269', 'page': 269},
    {'level': 3, 'title': '一、对弧长的线积分(第一类线积分) 269', 'page': 269},
    {'level': 3, 'title': '二、对坐标的线积分(第二类线积分) 270', 'page': 270},
    {'level': 3, 'title': '常考题型与典型例题 272', 'page': 272},
    {'level': 2, 'title': '12.3 曲面积分 277', 'page': 277},
    {'level': 3, 'title': '考试内容概要 277', 'page': 277},
    {'level': 3, 'title': '一、对面积的面积分(第一类面积分) 277', 'page': 277},
    {'level': 3, 'title': '二、对坐标的面积分(第二类面积分) 278', 'page': 278},
    {'level': 3, 'title': '常考题型与典型例题 279', 'page': 279},
    {'level': 2, 'title': '12.4 多元积分应用 284', 'page': 284},
    {'level': 3, 'title': '考试内容概要 284', 'page': 284},
    {'level': 3, 'title': '常考题型与典型例题 285', 'page': 285},
    {'level': 2, 'title': '12.5 场论初步 288', 'page': 288},
    {'level': 3, 'title': '考试内容概要 288', 'page': 288},
    {'level': 3, 'title': '常考题型与典型例题 289', 'page': 289},

    # Miscellaneous
    {'level': 1, 'title': '金榜时代图书数目 305', 'page': 305},
]

# 使用示例
if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python add_bookmarks_prompt.py <PDF文件路径>")
        sys.exit(1)

    pdf_path = sys.argv[1]
    name, ext = os.path.splitext(pdf_path)
    output_path = f"{name}fitz{ext}"
    
    # bookmark_data 要求
    # 1. 26武忠祥高等数学基础篇
    #   - 部分章节可能因仅存在一节的原因，只存在level=3的TOC，需TOC Gen手动添加： 有的章没有小节，您可以在给这一章的内容之前加一个与章的页数同页的默认的第一小节。节标题采用与章相同的名称。
    #   - 对于level=1与子/首level=2的TOC项目page不一致的情况，需TOC Gen手动修改一致（不影响Goodnotes数据）： 对于每章的第一个二级目录，检查它是否与它最近的父级一级目录的页码page字段数值一致，如果不一致将二级目录的页码改为关联一级目录的页码。先告诉我你的分析以及计划做出的变更，再给出新的 bookmark data。 
    # 2. 统一、优化节标题格式：把level=2的项目的title改一下，从 `第x节` 改成 `<所在章序号:数字>.<节序号:数字>`

    # 方法2: 通过已知对应关系计算偏移量
    # 例如：已知书中第202页对应PDF的第180页
    actual_page = 289
    pdf_page_index = 301  # PDF页面索引（从0开始）
    calculated_offset = calculate_page_offset(actual_page, pdf_page_index)
    
    print(f"计算出的页码偏移量: {calculated_offset}")
    
    add_bookmarks_to_pdf(
        pdf_path=pdf_path, 
        output_path=output_path, 
        bookmark_data=bookmark_data, 
        page_offset=calculated_offset
    )
