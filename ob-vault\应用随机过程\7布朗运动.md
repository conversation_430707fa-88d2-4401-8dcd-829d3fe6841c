**第七章 Brown运动**

作者 郭旭

统计学院

北京师范大学

**布朗**

罗伯特·布朗（<PERSON>，1773年12月21日－1858年6月10日），出生于苏格兰的科学界的芒特罗斯。在爱丁堡大学学习医学，19世纪英国植物学家，主要贡献是对澳洲植物的勇察和发现了布朗运动。1827年在研究花粉和孢子在水中悬浮液态的现现行为时，发现花粉有不规则的运动，后来证实他观察到细粒和灰尘也有同样的现象。直称他并没有能从理论说明释这种现象，但后来的科学家用他的名字命名为布朗运动。1828年，布朗命名了细胞核，虽然并不是他第一个发现的，但是他认识了细胞核的普遍存在并命名。现代科学界对布朗运动是否由他第一个发现的还有争议。澳大利亚的植物有一个属和几个种的植物是以他的名字命名的。

**第7章 Brown运动**

**7.1 基本概念与性质**

我们从讨论简单的随机游动开始。设有一个粒子在直线上随机游动，在每个单位时间内若可能的向左或向右移动一个单位的长度。现在加速这个过程。在越来越小的时间间隔中走越来越小的步子。若能以正确的方式越于极限，我们就得到Brown运动。详细地说就是令此过程每隔$\Delta t$时间等概率地向左或向右移动$\Delta x$的距离。如果以$X(t)$记时刻$t$粒子的位置，则

$$

X(t) = \Delta x(X_1 + \cdots + X_{[t/\Delta t]}) \tag{1}

$$

其中$[t/\Delta t]$表示$t/\Delta t$的整数部分，令

$$

X_i = \begin{cases}

+1, & \text{如果第}i\text{步向右} \

-1, & \text{如果第}i\text{步向左}

\end{cases}

$$

且假设诸$X_i$相互独立，

$$

P(X_i = 1) = P(X_i = -1) = \frac{1}{2}

$$

由于$EX_i = 0, var(X_i) = E(X_i^2) = 1$及(1)，我们有$E[X(t)] = 0, var(X(t)) = (\Delta x)^2[t/\Delta t]$。现在要令$\Delta x$和$\Delta t$趋于零，并使得极限有意义。如果取$\Delta x = \Delta t$，令$\Delta t \to 0$，

则$var[X(t)] \to 0$，同时注意到

$$

X(t) = t \cdot \frac{1}{t/\Delta t} \sum_{i=1}^{t/\Delta t} X_i

$$

从而$X(t) = 0, a.s.$

如果$\Delta t = (\Delta x)^3$，则$Var(X(t)) \to \infty$，这是不合理的。因为粒子的运动是连续的，不可能在很短时间内运离出发点。

因此，我们做出下面的假设：令$\Delta x = \sigma\sqrt{\Delta t}$，$\sigma$为某个正常数，从上面的讨论可见，当$\Delta t \to 0$时，$E[X(t)] = 0, var[X(t)] \to \sigma^2 t$。

注意到：

$$

X(t) = \Delta x(X_1 + \cdots + X_{[t/\Delta t]}) = \frac{\sigma\sqrt{t}}{\sqrt{t/\Delta t}} \sum_{i=1}^{t/\Delta t} X_i.

$$

由中心极限定理可得：

(1) $X(t)$服从均值为0，方差为$\sigma^2 t$的正态分布。

此外，由于随机游动的值在不相重叠的时间区间中的变化是独立的，所以有

(2) ${X(t), t \geq 0}$有独立的增量。

又因为随机游动在任一时间区间中的位置变化的分布只依赖于区间的长度，可见

(3) ${X(t), t \geq 0}$有平稳增量。

下面我们就给出Brown运动的严格定义。

**定义 7.1**

随机过程${X(t), t \geq 0}$如果满足：

(1)$X(0) = 0$；

(2)${X(t), t \geq 0}$有独立的平稳增量；

(3)对每个$t > 0, X(t)$服从正态分布$N(0, \sigma^2 t)$。

则称${X(t), t \geq 0}$为Brown运动，也称为Wiener过程。

常记为${B(t), t \geq 0}$或${W(t), t \geq 0}$。

**Wiener过程**

数学中，维纳过程是一种连续时间随机过程，得名于诺伯特·维纳。由于与物理学中的布朗运动有密切关系，也常被称为"布朗运动过程"或简称为布朗运动。维纳过程是最稳定的高斯随机过程的平稳独立增量随机过程）中最有名的一类。在纯数学、应用数学、经济学与物理学中都有重要作用。维纳过程的地位在纯数学中与在应用数学中同等重要。在纯数学中，维纳过程导致了对连续鞅理论的研究，是刻画一系列重要的复杂过程的基本工具。它是随机分析、扩散过程和位势领域的研究中是不可或缺的。在应用数学中，维纳过程可以描述高维白噪声的积分形式。在电子工程中，维纳过程是建立噪音的数学模型的重要部分。控制论中，维纳过程可以用来表示不可知因素。维纳过程和物理学中的布朗运动有密切关系。布朗运动是悬浮在液体中的花粉颗粒粒径所进行的无休止随机运动。维纳运动也可以描述由福克-普朗克方程和朗之万方程定的其他随机运动。维纳过程构成了量子力学的严谨路径积分表述的基础（根据费曼-卡茨公式，薛定谔方程的解可以用维纳过程来表示）。金融数学中，维纳过程可以用于描述期权定价模型如布莱克-斯科尔斯模型。

**布朗**

诺伯特·维纳(Norbert Wiener，1894-1964)，美国数学家，控制论的创始人。维纳1894年11月26日生于密苏里州的哥伦比亚，1964年3月18日卒于斯德哥尔摩。维纳在其70年的科学生涯中，先后涉及哲学、数学、物理学和工程学，晚后转向生物学，在各个领域中都取了丰硕成果，称得上是博特新领物过的。本世纪多才多艺和学识渊博的科学天人。他一生发表论文240多篇，著作14本。他的主要著作有《控制论》(1948)、《维纳选集》(1964)和《维纳数学论文集》(1980)。维纳还有两本自传《昔日神童》和《我是一个数学家》。

如果$\sigma = 1$，我们称之为标准Brown运动，如果$\sigma \neq 1$，则可考虑${X(t)/\sigma, t \geq 0}$，它是标准Brown运动。故不失一般性，可以只考虑标准Brown运动的情形。

由于这一定义在应用中不是十分方便，我们不加证明的给出下面的性质作为Brown运动的等价定义，其证明可以在许多随机过程的著作中找到。

**性质 7.1**

Brown运动是具有下述性质的随机过程${B(t), t \geq 0}$：

(1) （正态增量）$B(t) - B(s) \sim N(0, t - s)$，即$B(t) - B(s)$服从均值为0，方差为$t - s$的正态分布。当$s = 0$时，$B(t) - B(0) \sim N(0, t)$。

(2) （独立增量）$B(t) - B(s)$独立于过程的过去状态$B(u), 0 \leq u \leq s$。

(3) （路径的连续性）$B(t), t \geq 0$是$t$的连续函数。

**注 7.1**

性质7.1中我们并没有假定$B(0) = 0$，因此我们称之为始于$x$的Brown运动，所以有时为了强调起始点，也记为${B^x(t)}$。这样，定义7.1所指的就是始于0的Brown运动${B^0(t)}$。易见，

$$

B^x(t) - x = B^0(t). \tag{2}

$$

式(2)按照下面的定义7.2称为Brown运动的空间齐次性。此性质也说明，$B^x(t)$和$x + B^0(t)$是相同的，我们只需研究始于0的Brown运动就可以了，如不加说明，Brown运动就是始于0的Brown运动。

**定义 7.2**

设${X(t), t \geq 0}$是随机过程，如果它的有限维分布是空间平移不变的，即

$$

\begin{aligned}

& P(X(t_1) \leq x_1, X(t_2) \leq x_2, \cdots, X(t_n) \leq x_n|X(0) = 0) \

= & P(X(t_1) \leq x_1 + x, X(t_2) \

& \leq x_2 + x, \cdots, X(t_n) \leq x_n + x|X(0) = x)

\end{aligned}

$$

则称此过程为空间齐次的。

下面给出关于Brown运动的概率计算的例子。

**例 7.1**

设${B(t), t \geq 0}$是标准Brown运动，计算$P(B(2) \leq 0)$和$P(B(t) \leq 0, t = 0, 1, 2)$。

**解** 由于$B(2) \sim N(0, 2)$，所以$P(B(2) \leq 0) = \frac{1}{2}$。因为$B(0) = 0$，所以$P(B(t) \leq 0, t = 0, 1, 2) = P(B(t) \leq 0, t = 1, 2) = P(B(1) \leq 0, B(2) \leq 0)$。虽然$B(1)$和$B(2)$不是独立的，但由性质7.1(2)和(3)可知$B(2) - B(1)$与$B(1)$是相互独立的标准正态分布随机变量，于是利用分解式

$$

B(2) = B(1) + (B(2) - B(1))

$$

我们有

$$

\begin{aligned}

P(B(1) \leq 0, B(2) \leq 0) &= P(B(1) \leq 0, B(1) + (B(2) - B(1)) \leq 0) \\

&= P(B(1) \leq 0, B(2) - B(1) \leq -B(1))

\end{aligned}

$$

由定理1.12(1)和(9)，我们有

$$
\begin{aligned}
& P(B(1) \leq 0, B(2) - B(1) \leq -B(1)) \\
= & \int_{-\infty}^0 P(B(2) - B(1) \leq -x)f(x)dx \\
= & \int_{-\infty}^0 \Phi(-x)d\Phi(x)
\end{aligned}
$$

这里$\Phi$和$f$分别表示标准正态分布的分布函数和密度函数。由积分的变量替换公式得

$$

\int_0^\infty \Phi(x)f(-x)dx = \int_0^\infty \Phi(x)d\Phi(x) = \int_{\frac{1}{2}}^1 ydy = \frac{3}{8}.

$$

如果过程从$x$开始，$B(0) = x$，则$B(t) \sim N(x,t)$，于是

$$

P_x(B(t) \in (a,b)) = \int_a^b \frac{1}{\sqrt{2\pi t}}e^{-\frac{(y-x)^2}{2t}}dy.

$$

这里，概率$P_x$的下标$x$表示过程始于$x$。积分号中的函数

$$

p_t(x,y) = \frac{1}{\sqrt{2\pi t}}e^{-\frac{(y-x)^2}{2t}} \tag{3}

$$

称为Brown运动的转移概率密度。利用独立增量性以及转移概率密度，我们可以计算任意Brown运动的有限维分布

$$

\begin{aligned}

& P_x{B(t_1) \leq x_1, \cdots, B(t_n) \leq x_n} \\

= & \int_{-\infty}^{x_1} p_{t_1}(x,y_1)dy_1 \int_{-\infty}^{x_2} p_{t_2-t_1}(y_1,y_2)dy_2 \cdots \\

& \int_{-\infty}^{x_n} p_{t_n-t_{n-1}}(y_{n-1},y_n)dy_n.

\end{aligned}

\tag{4}

$$

$$

\begin{aligned}

& P(B(t + t_0) > x_0|B(t_0) = x_0) \\

= & P(B(t + t_0) - B(t_0) + B(t_0) > x_0|B(t_0) = x_0) \\

= & P(B(t + t_0) - B(t_0) > 0|B(t_0) = x_0) \\

= & P(B(t + t_0) - B(t_0) > 0) = \frac{1}{2}.

\end{aligned}

$$

**定义 7.3**

Brown运动的二次变差$[B,B](t)$定义为当${t_i^n}_{i=0}^{n}$逼近$[0,t]$的分割，且其核$\delta_n = \max_{0 \leq i \leq n-1}(t_{i+1}^n - t_i^n) \to 0$时保概率收敛变义平的极限

$$

[B,B](t) = [B,B](%5B0,t%5D) = \lim_{\delta_n \to 0} \sum_{i=0}^{n-1} |B(t_{i+1}^n) - B(t_i^n)|^2. \tag{5}

$$

下面是Brown运动的路径性质。从时刻0到时刻$T$对Brown运动的一次观察称为Brown运动在区间$[0,T]$上的一个路径或一个实现。路径作为$t$的函数。Brown运动的几乎所有的样本路径$B(t), 0 \leq t \leq T$都具有下述性质：

(1) 是$t$的连续函数；

(2) 在任何区间（无论区间多么小）上都不是单调的；

(3) 在任何点都不是可微的；

(4) 在任何区间（无论区间多么小）上都是无限变差的；

(5) 对任何$t$，在$[0,t]$上的二次变差等于$t$。

上述性质(1)∼(3)不难理解，(4)可以从(5)得到。

注意到

$$

E[(B(t + h) - B(t))^2] = h

$$

因而在时间间隔$h \to 0$时，$B(t + h) - B(t)$也趋于0；但

$$

var\left(\frac{B(t + h) - B(t)}{h}\right) = \frac{1}{h} \to \infty

$$

所以导数不存在也就不奇怪了。

对于连续可微函数$g(t)$，其二次变差为0。实际上根据中值定理可有

$$

\sum_{i=0}^{n-1}[g(t_{i+1}^n) - g(t_i^n)]^2 = \sum_{i=0}^{n-1} g'(t_i^*)^2(t_{i+1}^n - t_i^n)^2

$$

$$

\leq \delta_n \sum_{i=0}^{n-1} g'(t_i^*)^2(t_{i+1}^n - t_i^n) \to 0.

$$

这里是因为

$$

\sum_{i=0}^{n-1} g'(t_i^*)^2(t_{i+1}^n - t_i^n) \to \int_0^t g'(s)^2ds < \infty.

$$

另外对于连续可微函数，形式上有：

$$

[dg(t)]^2 = g'(t)^2[dt]^2

$$

而$[dt]^2$作为高阶项是可忽略的。

**定理 7.1**

$[B,B](t) = t$.

证明 取区间$[0,t]$的分割${t_i^n}_{i=0}^n$使得$\sum_n \delta_n < \infty$。记

$$

S_n = \sum_{i=0}^{n-1}[B(t_{i+1}^n) - B(t_i^n)]^2,

$$

则

$$

ES_n = \sum_i E[B(t_{i+1}^n) - B(t_i^n)]^2 = \sum_{i=1}^{n-1}(t_{i+1}^n - t_i^n) = t.

$$

再由标准正态分布的4阶矩公式得

$$

\begin{aligned}

var(S_n) & = var(\sum_{i=0}^{n-1}(B(t_{i+1}^n) - B(t_i^n))^2) \\

& = \sum_{i=0}^{n-1} var((B(t_{i+1}^n) - B(t_i^n))^2) \\

& = \sum_{i=0}^{n-1} 2(t_{i+1}^n - t_i^n)^2 \leq 2\max_i(t_{i+1}^n - t_i^n)t = 2t\delta_n,

\end{aligned}

\tag{6}
$$

所以

$$

\sum_{n=1}^\infty \Pr(|S_n - t| > a) \leq \sum_{n=1}^\infty \frac{var(S_n)}{a^2} \leq \frac{2t}{a^2}\sum_{n=1}^\infty \delta_n < \infty

$$

由Borel-Cantelli引理可得

$S_n \to t \to 0,$ a.s.，故$[B,B](t) = t$.                                          □

注意到

$$

\sum_{i=0}^{n-1}[B(t_{i+1}^n)-B(t_i^n)]^2 \leq \sum_{i=0}^{n-1}|B(t_{i+1}^n)-B(t_i^n)|\times\max_i|B(t_{i+1}^n)-B(t_i^n)|

$$

若$\sum_{i=0}^{n-1}|B(t_{i+1}^n) - B(t_i^n)|$的极限有限，由于$B(t)$是连续的，故$\sum_{i=0}^{n-1}|B(t_{i+1}^n) - B(t_i^n)|^2$趋于0，矛盾。从而Brown运动的全变差是无穷大。

**7.2 Gauss过程**

所谓Gauss过程是指所有有限维分布都是多元正态分布的随机过程。本节的主要目的是证明Brown运动是特殊的Gauss过程。首先，利用例题1.2容易证明下面引理。

**引理 7.1**

设$X \sim N(\mu_1, \sigma_1^2)$，$Y \sim N(\mu_2, \sigma_2^2)$是相互独立的，则$(X, X + Y) \sim N(\mu, \Sigma)$，其中均值$\mu = (\mu_1, \mu_1 + \mu_2)'$，协方差矩阵$\Sigma = \begin{bmatrix} \sigma_1^2 & \sigma_1^2 \ \sigma_1^2 & \sigma_1^2 + \sigma_2^2 \end{bmatrix}$.

**定理 7.2**

Brown运动是均值函数为$m(t) = 0$，协方差函数为$\gamma(s,t) = \min(t,s)$的Gauss过程。

证明

由于Brown运动均值是0，所以其协方差函数为

$$

\gamma(s,t) = cov(B(t), B(s)) = E[B(t)B(s)].

$$

若$t < s$，则$B(s) = B(t) + B(s) - B(t)$，且由独立增量性可得

$$

E[B(t)B(s)] = E[B^2(t)] + E[B(t)(B(s) - B(t))] = E[B^2(t)] = t.

$$

类似地，若$t > s$，则$E[B(t)B(s)] = s$。再由上述引理及数学归纳法我们得到$B(t)$的任何有限维分布都是正态的。     □

下面举几个例子。

**例 7.2** 设${B(t)}$是Brown运动，求$B(1) + B(2) + B(3) + B(4)$的分布。

解 考虑随机向量$X = (B(1), B(2), B(3), B(4))'$，由定理7.2可知$X$是多元正态分布的，且具有零均值和协方差矩阵

$$

\Sigma = \begin{bmatrix}

1 & 1 & 1 & 1 \

1 & 2 & 2 & 2 \

1 & 2 & 3 & 3 \

1 & 2 & 3 & 4

\end{bmatrix}.

$$

令$A = (1,1,1,1)$，则

$$

AX = X_1 + X_2 + X_3 + X_4 = B(1) + B(2) + B(3) + B(4)

$$

具有均值为零，方差为$A\Sigma A' = 30$的正态分布。于是$B(1) + B(2) + B(3) + B(4)$是正态分布的随机变量，均值为0，方差为30。

**例 7.3** 求$B(\frac{1}{2}) + B(\frac{1}{2}) + B(\frac{3}{2}) + B(1)$的分布。

解 考虑随机向量$Y = (B(\frac{1}{2}), B(\frac{1}{2}), B(\frac{3}{2}), B(1))'$。易见，$Y$与上例中的$X$具有相同的分布。所以，它的协方差矩阵为$\frac{1}{2}\Sigma$。因此，$AY = B(\frac{1}{2}) + B(\frac{1}{2}) + B(\frac{3}{2}) + B(1)$具有均值为0，方差为30的正态分布。

**例 7.4**

求概率$P(\int_0^1 B(t)dt > \frac{2}{\sqrt{3}})$.

解 首先需要指出的是，Brown运动具有连续路径，所以对每个路径来说，Riemann积分$\int_0^1 B(t)dt$存在。我们只需找出$\int_0^1 B(t)dt$的分布。由Riemann积分的定义，我们可以从逼近和

$$

\sum B(t_i)\Delta t_i

$$

的极限分布而得到。这里$t_i$是$[0,1]$的分点，$\Delta t_i = t_{i+1} - t_i$。例如取$t_i = \frac{i}{n}$，当$n = 4$时，逼近和即为上例中的随机变量。一般地，类似地证明，所有逼近和的分布都是零均值的正态分布，因此它们的极限分布是正态分布。于是，$\int_0^1 B(t)dt$也是零均值的正态分布。下面求计算$\int_0^1 B(t)dt$的方差。

$$

\begin{aligned}

var\left(\int_0^1 B(t)dt\right) & = cov\left(\int_0^1 B(t)dt, \int_0^1 B(s)ds\right) \

& = E\left(\int_0^1 B(t)dt \int_0^1 B(s)ds\right) \

& = \int_0^1 \int_0^1 E[B(t)B(s)]dtds \

& = \int_0^1 \int_0^1 cov[B(t)B(s)]dtds \

& = \int_0^1 \int_0^1 \min(t,s)dtds \

& = \frac{1}{3}.

\end{aligned}

\tag{7}
$$

这样，$\int_0^1 B(t)dt \sim N(0, \frac{1}{3})$。于是，所求的概率为

$$

P(\int_0^1 B(t)dt > \frac{2}{\sqrt{3}}) = P(\sqrt{3}\int_0^1 B(t)dt > 2) = 1 - \Phi(2) \approx 0.025.

$$

这里，$\Phi(x)$是标准正态分布的分布函数，通过查表可得$\Phi(2)$的近似值。

设$X_1, \cdots, X_n \sim U(0,1)$，则$E[\min(X_i)] = 1/(n+1), E[\max(X_i)] = n/(n+1)$.

证明：令$Z_1 = \min(X_i), Z_2 = \max(X_i)$，则

$$

P(Z_1 > x) = \prod_{i=1}^n P(X_i > x) = (1-x)^n;

$$

$$

P(Z_2 \leq x) = \prod_{i=1}^n P(X_i \leq x) = x^n.

$$

从而

$$

E(Z_1) = \int_0^1 P(Z_1 > x)dx = \int_0^1 (1-x)^n dx = \frac{1}{n+1};

$$

$$

E(Z_2) = \int_0^1 x \cdot nx^{n-1}dx = \frac{n}{n+1}.

$$

计算$E[B(2)|B(3)], E[B(2)B(3)]$和$E[B(2)B(4)|B(3)]$.

解：由于当

$$

\begin{pmatrix} X_1 \ X_2 \end{pmatrix} \sim N\left(\begin{pmatrix} \mu_1 \ \mu_2 \end{pmatrix}, \begin{pmatrix} \Sigma_{11} & \Sigma_{12} \ \Sigma_{21} & \Sigma_{22} \end{pmatrix}\right),

$$

有$X_1|X_2 \sim N(\mu_{1.2}, \Sigma_{11.2})$这里$\mu_{1.2} = \mu_1 + \Sigma_{12}\Sigma_{22}^{-1}(X_2 - \mu_2)$.

从而$E[B(2)|B(3)] = 0 + 2/3(B(3) - 0) = 2B(3)/3.$

对于第二项$E[B(2)B(3)]$，可得$E[B(2)B(3)] = 2$。

而对于第三项，

$$

\begin{aligned}

E[B(2)B(4)|B(3)] &= E{E[B(2)B(4)|B(3), B(2)]|B(3)} \

&= E{B(2)E[B(4)|B(3), B(2)]|B(3)} \

&= E{B(2)E[B(4) - B(3) + B(3)|B(3), B(2)]|B(3)} \

&= E{B(2)B(3)|B(3)} = 2B(3)^2/3.

\end{aligned}

$$

计算$\int_0^1 B(t)dt$给定$B(1) = x$的条件分布。

解：注意到

$$

\begin{aligned}

& \text{cov}\left(\int_0^1 B(t)dt, B(1)\right) \\
= & E\left[\int_0^1 B(t)dtB(1)\right] = \int_0^1 E[B(t)B(1)]dt \\
= & \int_0^1 tdt = 1/2.

\end{aligned}

$$

于是有

$$

\begin{pmatrix} \int_0^1 B(t)dt \ B(1) \end{pmatrix} \sim N\left(\begin{pmatrix} 0 \ 0 \end{pmatrix}, \begin{pmatrix} 1/3 & 1/2 \ 1/2 & 1 \end{pmatrix}\right)

$$

**练习**

计算$\Pr(B(2) > 0|B(1) > 0), \Pr(B(2) > 0|B(1) = 0), \Pr(B(1) > 0|B(2) > 0)$和$\Pr(B(1) > 0|B(2) = 0)$。

**7.3 Brown 运动的热性质**

本节讨论与 Brown 运动相联系的几个热，首先回忆连续熛的定义。随熛过程 ${X(t), t \geq 0}$ 称为熛，如果对任何 $t$ 是可积的,$E[|X(t)|] < \infty$, 且对任何 $s > 0$, 有

$$

E[X(t + s)|\mathcal{F}_t] = X(t), \quad a.s. \tag{8}

$$

这里 $\mathcal{F}_t = \sigma{X(u) : 0 \leq u \leq t}$ (由 ${X(u) : 0 \leq u \leq t}$ 生成的 $\sigma$ 代数) ，其中的等式 (8) 是几乎必然成立的，在后面有关的证明中，有时也省略 a.s.

**定理 7.3**

设 ${B(t)}$ 是 Brown 运动，则

(1) ${B(t)}$ 是熛；

(2) ${B(t)^2 - t}$ 是熛；

(3) 对任何实数 $u$，${\exp[uB(t) - \frac{u^2}{2}t]}$ 是熛.

证明　首先，由 $B(t + s) - B(t)$ 与 $\mathcal{F}_t$ 的独立性可知对任何函数 $g(x)$，有

$$

E[g(B(t + s) - B(t))|\mathcal{F}_t] = E[g(B(t + s) - B(t))]. \tag{9}

$$

由 Brown 运动的定义，$B(t) \sim N(0, t)$，所以 $B(t)$ 可积，且 $E[B(t)] = 0$. 再由其他性质得

$$

\begin{aligned}

E[B(t + s)|\mathcal{F}_t] &= E[B(t) + (B(t + s) - B(t))|\mathcal{F}_t] \\

&= E[B(t)|\mathcal{F}_t] + E[B(t + s) - B(t)|\mathcal{F}_t] \\

&= B(t) + E[B(t + s) - B(t)] = B(t),

\end{aligned}

$$

从而 (1) 得证.

由于 $E[B^2(t)] = t < \infty$, 所以 $B^2(t)$ 可积. 于是得到

$$

\begin{aligned}

B^2(t + s) &= (B(t) + B(t + s) - B(t))^2 \

&= B^2(t) + 2B(t)(B(t + s) - B(t)) + (B(t + s) - B(t))^2.

\end{aligned}

$$

$$

\begin{aligned}

E[B^2(t + s)|\mathcal{F}_t] &= B^2(t) + 2E[B(t)(B(t + s) - B(t))|\mathcal{F}_t] \\

&\quad + E[(B(t + s) - B(t))^2|\mathcal{F}_t] \\

&= B^2(t) + s,

\end{aligned}
\tag{10}
$$

这里我们利用了 $B(t + s) - B(t)$ 与 $\mathcal{F}_t$ 的独立性且具有均值 0，并对 $g(x) = x^2$ 应用式 (9)。在式 (10) 两端同时减去 $(t + s)$ 则 (2) 得证.

考虑 $B(t) \sim N(0, t)$ 的矩母函数 $E[e^{uB(t)}] = e^{tu^2/2} < \infty$，这组合着 $e^{uB(t)}$ 是可积的，并且

$$

E[e^{uB(t)-\frac{u^2}{2}t}] = 1.

$$

取 $g(x) = e^{ux}$，利用式 (9) 可得

$$

\begin{aligned}

E\left[e^{uB(t+s)}|\mathcal{F}_t\right] &= E\left[e^{uB(t)+u(B(t+s)-B(t))}|\mathcal{F}_t\right] \\

&= e^{uB(t)}E\left[e^{u(B(t+s)-B(t))}|\mathcal{F}_t\right] \\

&= e^{uB(t)}E\left[e^{u(B(t+s)-B(t))}\right] \\

&= e^{uB(t)}e^{\frac{u^2}{2}s}.

\end{aligned}

$$

两端同时乘以 $e^{-\frac{u^2}{2}(t+s)}$，则 (3) 得证.

注　上述定理所给的这 3 个熛在理论上也有着十分重要的意义，比如熛 ${B^2(t) - t}$ 就是 Brown 运动的特征，即，如果连续熛 ${X(t)}$ 使得 ${X^2(t) - t}$ 也是熛，则 ${X(t)}$ 是 Brown 运动.

**7.4 Brown 运动的 Markov 性**

所谓 Markov 性是指在知道过程的现在与过去的状态的条件下，过程将来的表现与过去无关. 换言之，过程保柃了现在，但是并不记忆现在的状态是如何得到的，即"遗忘性". 在第 5 章中我们介绍了 Markov 链和连续时间离散状态的 Markov 过程, 而我们在这里所讨论的 Brown 运动是连续时间连续状态过程，为此我们从连续 Markov 过程的定义开始.

**定义 7.4**

设 ${X(t), t \geq 0}$ 是一个连续随机过程, 如果对任何 $t, s > 0$, 有

$$

P(X(t + s) \leq y|\mathcal{F}_t) = P(X(t + s) \leq y|X(t)), \quad a.s. \tag{11}

$$

则称 ${X(t)}$ 为 Markov 过程, 这里 $\mathcal{F}_t = \sigma{X(u), 0 \leq u \leq t}$. 性质 (11) 称为 Markov 性.

与定义 6.4 等价的，Markov 过程的另一种定义方式为：

**定义 7.5**

设 ${X(t), t \geq 0}$ 是一个连续随机过程，如果对任何的有界 Borel 可测函数 $f$, 实数 $t, h > 0$, 有

$$

E[f(X_{t+h})|\mathcal{F}_t](%5Comega) = E[f(X_{t+h})|X_t(\omega)]. \tag{12}

$$

则称 $X(t)$ 为 Markov 过程，这里 $\mathcal{F}_t = \sigma{X(u), 0 \leq u \leq t}$. 性质 (12) 也称为 Markov 性.

**定理 7.4**

Brown 运动 ${B(t)}$ 具有 Markov 性.

**证明**

用矩母函数方法容易得到 $B(t + s)$ 在给定条件 $\mathcal{F}_t$ 下的分布与在给定条件 $B(t)$ 下的分布是一致的. 事实上，

$$

\begin{aligned}

E[e^{uB(t+s)}|\mathcal{F}_t] &= e^{uB(t)}E\left[e^{u(B(t+s)-B(t))}|\mathcal{F}_t\right] \\

&= e^{uB(t)}E\left[e^{u(B(t+s)-B(t))}\right] \quad \text{(因为}e^{u(B(t+s)-B(t))}\text{独立于}\mathcal{F}_t\text{)} \\

&= e^{uB(t)}e^{\frac{u^2s}{2}} \quad \text{(因为}B(t + s) - B(t) \sim N(0, s)\text{)} \\

&= e^{uB(t)}E\left[e^{u(B(t+s)-B(t))}|B(t)\right] \\

&= E\left[e^{uB(t+s)}|B(t)\right].

\end{aligned}

$$

所以，${B(t)}$ 具有 Markov 性.

□

独立增量过程是否都具有Markov性？

$$

\begin{aligned}

E[e^{uX(t+s)}|\mathcal{F}_t] &= e^{uX(t)}E\left[e^{u(X(t+s)-X(t))}|\mathcal{F}_t\right] \\

&= e^{uX(t)}E\left[e^{u(X(t+s)-X(t))}\right] \\

&= e^{uX(t)}E\left[e^{u(X(t+s)-X(t))}|X(t)\right] \\

&= E\left[e^{u(X(t+s))}|X(t)\right].

\end{aligned}

$$

连续的 Markov 过程 ${X(t)}$ 的转移概率定义为在时刻 $s$ 过程处于状态 $x$ 的条件下，过程在时刻 $t$ 的分布函数：

$$

P(y, t, x, s) = P(X(t) \leq y|X(s) = x).

$$

在 Brown 运动的情况下，这一分布函数是正态的:

$$

P(y, t, x, s) = \int_{-\infty}^y \frac{1}{\sqrt{2\pi(t - s)}}e^{-\frac{(u-x)^2}{2(t-s)}}du.

$$

Brown 运动的转移概率函数满足方程

$$

P(y, t, x, s) = P(y, t - s, x, 0).

$$

换言之，

$$

P(B(t) \leq y|B(s) = x) = P(B(t - s) \leq y|B(0) = x). \tag{13}

$$

当 $s = 0$ 时，$P(y, t, x, 0)$ 具有密度函数

$$

p_t(x, y) = \frac{1}{\sqrt{2\pi t}}e^{-\frac{(y-x)^2}{2t}}.

$$

公式 (13) 给出的性质称为 Brown 运动的时齐性，即分布不随时间的平移而变化. 于是由 (4) 可知 Brown 运动的所有有限维分布都是时齐的.

下面讨论 Brown 运动的强 Markov 性，为此给出关于 $B(t)$ 停时的定义.

**定义 7.6**

如果非负随机变量 $T$ 可以取无穷值，既 $T : \Omega \to [0, \infty]$，并且对任何 $t$，有 ${T \leq t} \in \mathcal{F}_t = \sigma{B(u), 0 \leq u \leq t}$，则称 $T$ 为关于 ${B(t), t \geq 0}$ 的停时.

所谓的强 Markov 性，实际上是将 Markov 性中固定的时间 $t$ 用停时 $T$ 来代替. 下面我们不加证明的给出关于 Brown 运动的强 Markov 性定理，其证明见 [16].

**定理 7.5**

设 $T$ 是关于 Brown 运动 ${B(t)}$ 的有限停时，

$$

\mathcal{F}_T = {A \in \mathcal{F} : A \cap {T \leq t} \in \mathcal{F}_t, \forall t \geq 0}.

$$

则

$$

P(B(T + t) \leq y|\mathcal{F}_T) = P(B(T + t) \leq y|B(T)) \quad a.s.

$$

即 Brown 运动 ${B(t)}$ 具有强 Markov 性.

由此定理可以看出，如果定义

$$

\hat{B}(t) = B(T + t) - B(T). \tag{14}

$$

则 $\hat{B}(t)$ 是始于 0 的 Brown 运动并且独立于 $\mathcal{F}_T$.

**7.5 Brown 运动的最大值变量及反正弦律**

以 $T_x$ 记 Brown 运动首次击中 $x$ 的时刻，即

$$

T_x = \inf{t > 0 : B(t) = x}.

$$

当 $x > 0$ 时，为计算 $P(T_x \leq t)$，我们考虑 $P(B(t) \geq x)$。由全概率公式

$$

\begin{aligned}

P(B(t) \geq x) &= P(B(t) \geq x|T_x \leq t)P(T_x \leq t)\\

&\quad + P(B(t) \geq x|T_x > t)P(T_x > t),

\end{aligned}

\tag{15}
$$

若 $T_x \leq t$，则 $B(t)$ 在 $[0,t]$ 中的某个点击中 $x$，由对称性得

$$

P(B(t) \geq x|T_x \leq t) = \frac{1}{2}.

$$

再由连续性可知，$B(t)$ 不可能近水击中 $x$ 就太于 $x$，所以 (15) 中第 2 项为零。因此

$$

\begin{aligned}

P(T_x \leq t) &= 2P(B(t) \geq x)\\

&= \frac{2}{\sqrt{2\pi t}} \int_x^{\infty} e^{-u^2/2t}du\\

&= \frac{2}{\sqrt{2\pi}} \int_{x/\sqrt{t}}^{\infty} e^{-y^2/2}dy.

\end{aligned}

\tag{16}
$$

由此可见

$$

P(T_x < \infty) = \lim_{t \to \infty} P(T_x \leq t) = \frac{2}{\sqrt{2\pi}} \int_0^{\infty} e^{-y^2/2}dy = 1.

$$

对分布函数求导数可得其分布密度

$$

f_{T_x}(u) = \begin{cases}

\frac{x}{\sqrt{2\pi}u^{3/2}} e^{-\frac{x^2}{2u}}, & \text{如果 } u > 0,\

0, & \text{如果 } u \leq 0.

\end{cases} \tag{17}

$$

利用式 (16)，可以得到

$$

\begin{aligned}

ET_x &= \int_0^{\infty} P(T_x > t)dt\\

&= \int_0^{\infty} \left(1 - \frac{2}{\sqrt{2\pi}} \int_{x/\sqrt{t}}^{\infty} e^{-y^2/2}dy\right) dt\\

&= \frac{2}{\sqrt{2\pi}} \int_0^{\infty} \int_0^{x/\sqrt{t}} e^{-y^2/2}dydt\\

&= \frac{2}{\sqrt{2\pi}} \int_0^{\infty} \left(\int_0^{x^2/y^2} dt\right)e^{-y^2/2}dy\\

&= \frac{2x^2}{\sqrt{2\pi}} \int_0^{\infty} \frac{1}{y^2} e^{-y^2/2}dy\\

&\geq \frac{2x^2e^{-1/2}}{\sqrt{2\pi}} \int_0^1 \frac{1}{y^2}dy\\

&= \infty

\end{aligned}

$$

因此，$T_x$ 虽然几乎必然是有限的，但有无穷的期望。直观地看，就是 Brown 运动以概率 1 会击中 $x$，但它的平均时间是无穷的。性质 $P(T_x < \infty) = 1$ 称为 Brown 运动的常返性。由于始于点 $a$ 的 Brown 运动与 ${a + B(t)}$ 是相同的，这里 ${B(t)}$ 是始于 0 的 Brown 运动，所以

$$

p_a(T_x < \infty) = p_0(T_{x-a} < \infty) = 1,

$$

即 Brown 运动从任何一点出发，击中 $x$ 的概率都是 1。

当 $x < 0$ 时，由对称性，$T_x$ 与 $T_{-x}$ 有相同的分布。于是，有

$$

P(T_x \leq t) = \frac{2}{\sqrt{2\pi}} \int_{|x|/\sqrt{t}}^{\infty} e^{-y^2/2}dy.

$$

$$

f_{T_x}(u) = \begin{cases}

\frac{|x|}{\sqrt{2\pi}u^{3/2}} e^{-\frac{x^2}{2u}}, & u > 0, & \text{如果}\\

0, & u \leq 0. & \text{如果}

\end{cases} \tag{18}

$$

另一个有趣的随机变量是 Brown 运动在 $[0,t]$ 中达到的最大值

$$

M(t) = \max_{0 \leq s \leq t} {B(s)}.

$$

它的分布可由下述等式得到，对 $x > 0$，

$$

\begin{aligned}

P(M(t) \geq x) &= P(T_x \leq t)\\

&= 2P(B(t) \geq x)\\

&= \frac{2}{\sqrt{2\pi}} \int_{x/\sqrt{t}}^{\infty} e^{-y^2/2}dy.

\end{aligned}

$$

读者不难得到 Brown 运动在 $[0,t]$ 中达到的最小值

$$

m(t) = \min_{0 \leq s \leq t} {B(s)}

$$

的分布。

如果时间 $\tau$ 使得 $B(\tau) = 0$，则称 $\tau$ 为 Brown 运动的零点。我们有下述定理。

**定理 7.6**

设 ${B^x(t)}$ 为始于 $x$ 的 Brown 运动，则 $B^x(t)$ 在 $(0,t)$ 中至少有一个零点的概率为

$$

\frac{|x|}{\sqrt{2\pi}} \int_0^t u^{-\frac{3}{2}}e^{-\frac{x^2}{2u}}du.

$$

证明 如果 $x < 0$，则由 ${B^x(t)}$ 的连续性得

$$

P(B^x\text{在 }(0,t)\text{ 中至少有一个零点}) = P\left(\max_{0 \leq s \leq t} B^x(s) \geq 0\right).

$$

因为 $B^x(t) = B(t) + x$，我们有

$$

\begin{aligned}

P(B^x\text{在 }(0,t)\text{ 中至少有一个零点})\\

&= P\left(\max_{0 \leq s \leq t} B^x(s) \geq 0\right)\\

&= P\left(\max_{0 \leq s \leq t} B(s) + x \geq 0\right) = P\left(\max_{0 \leq s \leq t} B(s) \geq -x\right)\\

&= 2P(B(t) \geq -x) = P(T_x \leq t)\\

&= \int_0^t f_{T_x}(u)du = \frac{-x}{\sqrt{2\pi}} \int_0^t u^{-\frac{3}{2}}e^{-\frac{x^2}{2u}}du.

\end{aligned}

\tag{19}
$$

对于 $x > 0$ 的情况的证明类似，只要知道 Brown 运动的最小值的分布即可完成其证明。

利用此结果可以证明下面定理。

**定理 7.7**

$B^y(t)$ 在区间 $(a,b)$ 中至少有一个零点的概率为

$$

\frac{2}{\pi} \arccos \sqrt{\frac{a}{b}}.

$$

证明 记

$$

h(x) = P(B^y\text{在 }(a,b)\text{ 中至少有一个零点}|B(a) = x).

$$

由 Markov 性，$P(B^y\text{在 }(a,b)\text{ 中至少有一个零点}|B(a) = x)$ 与 $P(B^x\text{ 在 }(0,b-a)\text{ 中至少有一个零点})$ 相同。由条件概率

$$

\begin{aligned}

P{B^y\text{在 }(a,b)\text{ 中至少有一个零点}}\\

&= \int_{-\infty}^{\infty} P{B^y\text{在 }(a,b)\text{ 中至少有一个零点}|B(a) = x}P(dx)\\

&= \int_{-\infty}^{\infty} h(x)P(dx)

\end{aligned}

$$

$$

\begin{aligned}

&= \sqrt{\frac{2}{\pi a}} \int_0^{\infty} h(x)e^{-\frac{x^2}{2a}}dx\\

&= \sqrt{\frac{2}{\pi a}} \int_0^{\infty} \frac{x}{\sqrt{2\pi}} \left(\int_0^{b-a} u^{-\frac{3}{2}}e^{-\frac{x^2}{2u}}du\right) e^{-\frac{x^2}{2a}}dx\\

&= \frac{1}{\pi\sqrt{a}} \int_0^{b-a} u^{-\frac{3}{2}} \int_0^{\infty} xe^{-x^2\left(\frac{1}{2u} + \frac{1}{2a}\right)}dxdu\\

&= \frac{1}{\pi\sqrt{a}} \int_0^{b-a} u^{-\frac{3}{2}} \frac{au}{a + u} du\\

&= \frac{2\sqrt{a}}{\pi} \int_0^{b-a} \frac{u^{-\frac{1}{2}}}{a + u} du\\

&= \frac{2}{\pi} \arctan \frac{\sqrt{b-a}}{\sqrt{a}}\\

&= \frac{2}{\pi} \arccos \sqrt{\frac{a}{b}}.

\end{aligned}

$$

□

于是，我们得到 Brown 运动的反正弦律。

**定理 7.8**

设 ${B^y(t), t \geq 0}$ 是 Brown 运动，则

$$

P(B^y(t)\text{在 }(a,b)\text{ 中没有零点}) = \frac{2}{\pi} \arcsin \sqrt{\frac{a}{b}}.

$$

下面 Brown 运动在时刻 $t$ 之前最后一个零点以及在 $t$ 之后的第一个零点的分布情况。令

$$

\zeta_t = \sup{s \leq t : B(s) = 0} = t\text{ 之前最后一个零点,}

$$

$$

\beta_t = \inf{s \geq t : B(s) = 0} = t\text{ 之后的第一个零点.}

$$

注意，$\beta_t$ 是一个停时，而 $\zeta_t$ 不是停时（请读者验证）。由反正弦律，有

$$

P(\zeta_t \leq x) = P(B\text{ 在 }(x,t)\text{ 中没有零点}) = \frac{2}{\pi} \arcsin \sqrt{\frac{x}{t}},

$$

$$

P(\beta_t \geq y) = P(B\text{ 在 }(t,y)\text{ 中没有零点}) = \frac{2}{\pi} \arcsin \sqrt{\frac{t}{y}},

$$

$$

P(\zeta_t \leq x, \beta_t \geq y) = P(B\text{ 在 }(x,y)\text{ 中没有零点}) = \frac{2}{\pi} \arcsin \sqrt{\frac{x}{y}}.

$$

**7.6 Brown 运动的几种变化**

**7.6.1 Brown 桥**

由 Brown 运动，我们可以定义另一类在数理金融中经常用到的过程 Brown 桥过程。

**定义 7.7**

设 ${B(t), t \geq 0}$ 是 Brown 运动。令

$$

B^*(t) = B(t) - tB(1), \quad 0 \leq t \leq 1,

$$

则称随机过程 ${B^*(t), 0 \leq t \leq 1}$ 为 Brown 桥（Brown bridge）。

因为 Brown 运动是 Gauss 过程，所以 Brown 桥也是 Gauss 过程，其 $n$ 维分布由均值函数和方差函数完全确定。且对任何

$0 \leq s \leq t \leq 1$，有

$$

\begin{aligned}

EB^{*}(t) & = 0,\\

EB^{*}(s)B^*(t) &= E[(B(s) - sB(1))(B(t) - tB(1))]\\

&= E[B(s)B(t) - tB(s)B(1)\\

&\quad - sB(t)B(1) + tsB^2(1)]\\

&= s - ts - ts + ts = s(1 - t).

\end{aligned}

$$

此外，由定义可知 $B^*(0) = B^*(1) = 0$，即此过程的起始点是固定的，就象桥一样（见图 7.1）。这就是 Brown 桥名称的由来。

**7.6.2 有吸收值的 Brown 运动**

设 $T_x$ 为 Brown 运动 ${B(t)}$ 首次击中 $x$ 的时刻，$x > 0$。令

$$

Z(t) = \begin{cases}

B(t), & \text{若} t < T_x,\

x, & \text{若} t \geq T_x,

\end{cases}

$$

则 ${Z(t), t \geq 0}$ 是击中 $x$ 后，永远停留在那里的 Brown 运动。对任何的 $t > 0$，随机变量 $Z(t)$ 的分布有离散和连续两个部分。离散部分是

$$

P(Z(t) = x) = P(T_x \leq t) = \frac{2}{\sqrt{2\pi t}} \int_x^{\infty} e^{-\frac{y^2}{2t}}dy.

$$

连续部分的分布

$$

\begin{aligned}

P(Z(t) \leq y) &= P(B(t) \leq y, \max_{0 \leq s \leq t} B(s) < x)\

&= P(B(t) \leq y) - P(B(t) \leq y, \max_{0 \leq s \leq t} B(s) > x).(20)

\end{aligned}

$$

计算 (20) 式中的最后一项，由条件概率公式得

$$

P(B(t) \leq y, \max_{0 \leq s \leq t} B(s) > x) = P(B(t) \leq y| \max_{0 \leq s \leq t} B(s) > x)P(\max_{0 \leq s \leq t} B(s) > x). \tag{21}

$$

由事件 $\max_{0 \leq s \leq t} B(s) > x$ 等价于事件 $T_x < t$ 及 Brown 运动的对称性可知：$B(t)$ 在时刻 $T_x(< t)$ 击中 $x$，为了使在时刻 $t$ 不大于 $y$，则在 $T_x$ 之后的 $t - T_x$ 这段时间中就必须减少 $x - y$，而减少与增加 $x - y$ 的概率是相等的，所以

$$

P(B(t) \leq y| \max_{0 \leq s \leq t} B(s) > x) = P(B(t) \geq 2x - y| \max_{0 \leq s \leq t} B(s) > x). \tag{22}

$$

从 (21) 及 (22) 式得

$$

\begin{aligned}

P(B(t) \leq y, \max_{0 \leq s \leq t} B(s) > x) &= P(B(t) \geq 2x - y, \max_{0 \leq s \leq t} B(s) > x)\\

&= P(B(t) \geq 2x - y) \quad \text{(因为} y < x\text{),}

\end{aligned}

$$

由式 (20)，有

$$

\begin{aligned}

P(Z(t) \leq y) &= P(B(t) \leq y) - P(B(t) \geq 2x - y)\\

&= P(B(t) \leq y) - P(B(t) \leq y - 2x)\\

&= \frac{1}{\sqrt{2\pi t}} \int_{y-2x}^y e^{-\frac{u^2}{2t}}du.

\end{aligned}

$$

**7.6.3 在原点反射的 Brown 运动**

由

$$

Y(t) = |B(t)|, \quad t \geq 0

$$

定义的过程 ${Y(t), t \geq t}$ 称为在原点反射的 Brown 运动。它的概率分布为

$$

\begin{aligned}

P(Y(t) \leq y) &= P(B(t) \leq y) - P(B(t) < -y) \quad (y > 0)\\

&= 2P(B(t) \leq y) - 1\\

&= \frac{2}{\sqrt{2\pi t}} \int_{-\infty}^y e^{-\frac{u^2}{2t}}du - 1.

\end{aligned}

$$

**7.6.4 几何 Brown 运动**

由

$$

X(t) = e^{B(t)}, \quad t \geq 0

$$

定义的过程 ${X(t), t \geq t}$ 称为几何 Brown 运动。由于 Brown 运动的矩函数为 $E[e^{sB(t)}] = e^{ts^2/2}$，所以几何 Brown 运动的均值函数与方差函数分别为

$$

\begin{aligned}

E[X(t)] &= E[e^{B(t)}] = e^{t/2},\\

\text{var}(X(t)) &= E[X^2(t)] - (E[X(t)])^2\\

&= E[e^{2B(t)}] - e^t\\

&= e^{2t} - e^t.

\end{aligned}

$$

在金融市场中，人们经常假定股票的价格按照几何 Brown 运动变化，在下面的例子中我们假定如此假定。

**例 7.5** 股票期权的价值

设某人拥有某种股票的买权，时刻为 $T$，交割价格为 $K$ 的欧式看涨期权，即他（她）具有在时刻 $T$ 以固定的价格 $K$ 购买一股这种股票的权力。假设这种股票目前的价格为 $y$，并按照几何 Brown 运动变化，我们计算拥有这个期权的平均价值。设 $X(T)$ 表示时刻 $T$ 的股票价格，若 $X(T)$ 高于 $K$ 时，期权将被实施，因此该期权在时刻 $T$ 的平均价值应为

$$

\begin{aligned}

E[\max(X(T) - K, 0)] &= \int_0^{\infty} P(X(T) - K > u)du\\

&= \int_0^{\infty} P(ye^{B(T)} - K > u)du\\

&= \int_0^{\infty} P(B(T) > \log \frac{K + u}{y})du\\

&= \frac{1}{\sqrt{2\pi T}} \int_0^{\infty} \int_{\log[(K+u)/y]}^{\infty} e^{-x^2/2T}dxdu.

\end{aligned}

$$