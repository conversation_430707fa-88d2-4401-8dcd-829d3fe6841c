### Cross Platform Framework

#### Tauri

- [Developing Your Mobile Application | Tauri](https://v2.tauri.app/develop/#developing-your-mobile-application)

#### onejs

- [Configuration | One](https://onestack.dev/docs/configuration) Not Production Ready
  - Integrations: 
    - [Introduction — <PERSON>agu<PERSON>](https://tamagui.dev/docs/intro/introduction)
    - [tamagui/starter-free: Tamagui React Native Starter Repo - Next, Expo, Solito](https://github.com/tamagui/starter-free)
    - [🥡 Tamagui Takeout](https://tamagui.dev/takeout)
  - [onejs/one-test-camera-app](https://github.com/onejs/one-test-camera-app) dev not supported on windows

#### Ignite CLI

- [Innovation Platform For Blockchain Development | Ignite CLI](https://ignite.com/) 面向区块链的开发工具，用来基于 **Cosmos SDK** 脚手架、开发并启动“主权链”

#### Ignite - Infinite Red

- [Ignite - Infinite Red's battle-tested React Native boilerplate](https://infinite.red/ignite) React Native/Expo 的脚手架与模板，用来快速搭建移动（及可选 Web）应用

- [Pipelines - infinitered/ignite](https://app.circleci.com/pipelines/github/infinitered/ignite)

- [infinitered/ignite-cookbook: Ignite Cookbook for React Native](https://github.com/infinitered/ignite-cookbook)

- Demos:

  - [infinitered/ChainReactApp2023](https://github.com/infinitered/ChainReactApp2023) 

  - [Jpoliachik/ignite-trivia: A Trivia App](https://github.com/Jpoliachik/ignite-trivia) opentdb.com + Ignite Bowser + MobX State Tree + TS

  - [Lucas-DeLorenzi/FinanceApp-Mercury](https://github.com/Lucas-DeLorenzi/FinanceApp-Mercury) Ignite Boilerplate, 3 yrs

  - [fable-app/fable: Subtitles for books](https://github.com/fable-app/fable) Android + iOS, 7 mons

  - [softwarebyze/MarqueeApp: Shows large text scrolling across the screen](https://github.com/softwarebyze/MarqueeApp) Web




#### Tauri V2

- 移动端：https://v2.tauri.app/develop/#developing-your-mobile-application
- 能力实现（插件）：通知、对话框、剪贴板、HTTP、OS、进程/命令、深链、SQLite 等能力
  - https://tauri.app/plugin/
  - eg. 密钥存储：https://v2.tauri.app/plugin/store/



#### ReNative

- [Unified Development Platform | ReNative](https://www.renative.org/)
- 





### Expo Demos

- Codebase
  - [mitri-dvp/react-native-spacex-app](https://github.com/mitri-dvp/react-native-spacex-app)
    - Public API [apollographql/spacex: A re-creation of https://github.com/SpaceXLand/api](https://github.com/apollographql/spacex)

  - [KrunalLathiya/FetchReactNative: React Native Fetch Example Tutorial From Scratch is today’s leading topic. In this tutorial, we will see how to Integrating React native apps with back end code using fetch API.](https://github.com/KrunalLathiya/FetchReactNative)
  - [burakorkmez/react-native-recipe-app](https://github.com/burakorkmez/react-native-recipe-app)
    - Vid: [📱 React Native+Expo打造全栈移动App！一看就会！](https://www.bilibili.com/video/BV1sLNRzbEfF)

  - [Esubaalew/AddisStore: Mini expo react native project to demonstrate a simple Product Store showing basic on line buying operations.](https://github.com/Esubaalew/AddisStore)
  - [workos/react-native-expo-example-app: React Native Expo Example Application powered by WorkOS SSO](https://github.com/workos/react-native-expo-example-app)
  - [thirdweb-example/expo-starter: Starter kit to build with Expo and thirdweb without additional initial configuration.](https://github.com/thirdweb-example/expo-starter)
  - [mshivam019/StorySail: A reading writing app](https://github.com/mshivam019/StorySail)
  - [SKempin/Lyrics-King-React-Native: Lyrics King is React Native song lyrics search app, built with Expo. Designed with Adobe XD.](https://github.com/SKempin/Lyrics-King-React-Native)
  - [nklmantey/convene: shared social calendar for you & your friends](https://github.com/nklmantey/convene)
  - [robinhuy/react-native-expo-examples: Learn React Native (Expo) by examples.](https://github.com/robinhuy/react-native-expo-examples)

- Tutorial
  - [How to build a real-time todo app with React Native](https://www.freecodecamp.org/news/how-to-build-a-real-time-todo-app-with-react-native-19a1ce15b0b3/) Doc
  - [【React Native 】从零手撕「FinTech 银行 App」克隆](https://www.bilibili.com/video/BV15Rhxz8En4) Vid
  - [Expo快速上手](https://www.bilibili.com/video/BV1U66bYzEyL)
  - [小红书，记账App，](https://www.bilibili.com/video/BV1LmAPeMERL)
  - 




### Existing Projects

- [west2-online/fzuhelper-app: 基于 React Native 跨平台架构的福大助手客户端，每日服务超 23,000 名师生](https://github.com/west2-online/fzuhelper-app/tree/master) Expo
- [MosRat/BnuCrow3: 北师小鸦3，重制版北师小鸦，BNU教务助手😓](https://github.com/MosRat/BnuCrow3) Tauri 
- [thu-info-community/thu-info-app: An APP aimed at integrating various sources of campus information, developed with React Native](https://github.com/thu-info-community/thu-info-app) 

### Components

- [timetable · GitHub Topics](https://github.com/topics/timetable)
  - [mikezzb/react-native-timetable: React native timetable component](https://github.com/mikezzb/react-native-timetable)
- [schedule · GitHub Topics](https://github.com/topics/schedule)

