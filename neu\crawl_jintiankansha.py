import time
import argparse
import requests
import pandas as pd

BASE_URL = "http://www.jintiankansha.me/api3/query/my_columns"

def crawl_all(token: str,
              user: str,
              start_page: int = 1,
              out_path: str = "jintiankansha_columns.xlsx",
              delay: float = 0.2,
              max_pages: int = 10000) -> int:
    """
    逐页抓取，直到 data 为空列表停止；导出 xlsx，并返回去重后的数量。
    """
    sess = requests.Session()
    sess.headers.update({
        "User-Agent": "Mozilla/5.0 (compatible; crawler/1.0)"
    })

    rows = []
    page = start_page

    while page <= max_pages:
        params = {"page": page, "token": token, "user": user}
        try:
            resp = sess.get(BASE_URL, params=params, timeout=15)
            resp.raise_for_status()
            j = resp.json()
        except Exception as e:
            print(f"第 {page} 页请求失败：{e}")
            break

        status = j.get("status")
        data = j.get("data", [])

        if status != "success":
            print(f"第 {page} 页返回状态异常：{status!r}，停止。")
            break

        if not data:
            print(f"第 {page} 页 data 为空，停止。")
            break

        # 记录本页数据
        for item in data:
            rows.append({
                "page": page,
                "source": item.get("source"),
                "name": item.get("name"),
                "desc": item.get("desc"),
                "image": item.get("image"),
                "slug": item.get("slug"),
                # 方便后续访问：根据 slug 拼出专栏页面（如需要可删除）
                "profile_url": f"http://www.jintiankansha.me/column/{item.get('slug')}" if item.get("slug") else None,
            })

        print(f"已抓取第 {page} 页，新增 {len(data)} 条。")
        page += 1
        if delay:
            time.sleep(delay)

    if not rows:
        print("没有抓取到任何数据。")
        return 0

    # 导出前去重（以 slug 为唯一键）
    df = pd.DataFrame(rows)
    df.drop_duplicates(subset=["slug"], inplace=True, ignore_index=True)
    df.to_excel(out_path, index=False)
    print(f"共 {len(df)} 条，已导出：{out_path}")
    return len(df)

def main():
    parser = argparse.ArgumentParser(description="抓取 jintiankansha 专栏列表并导出为 xlsx")
    parser.add_argument("--token", default="eTAy38KkI3", help="接口 token")
    parser.add_argument("--user", default="<EMAIL>", help="user 参数")
    parser.add_argument("--start", type=int, default=1, help="起始页（默认 1）")
    parser.add_argument("--out", default="jintiankansha_columns.xlsx", help="输出 xlsx 路径")
    parser.add_argument("--delay", type=float, default=0.2, help="请求间隔秒数")
    args = parser.parse_args()

    total = crawl_all(
        token=args.token,
        user=args.user,
        start_page=args.start,
        out_path=args.out,
        delay=args.delay,
    )
    print("数量:", total)

if __name__ == "__main__":
    main()

