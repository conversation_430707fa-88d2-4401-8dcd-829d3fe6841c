#!/usr/bin/env python3
# des9.py
# 专用 DES 加密（固定 firstKey="1", secondKey="2", thirdKey="3"），纯 Python 实现

import struct

# ----- DES 固定表 -----
S_BOXES = [
    [
        [14,4,13,1,2,15,11,8,3,10,6,12,5,9,0,7],
        [0,15,7,4,14,2,13,1,10,6,12,11,9,5,3,8],
        [4,1,14,8,13,6,2,11,15,12,9,7,3,10,5,0],
        [15,12,8,2,4,9,1,7,5,11,3,14,10,0,6,13]
    ],[
        [15,1,8,14,6,11,3,4,9,7,2,13,12,0,5,10],
        [3,13,4,7,15,2,8,14,12,0,1,10,6,9,11,5],
        [0,14,7,11,10,4,13,1,5,8,12,6,9,3,2,15],
        [13,8,10,1,3,15,4,2,11,6,7,12,0,5,14,9]
    ],[
        [10,0,9,14,6,3,15,5,1,13,12,7,11,4,2,8],
        [13,7,0,9,3,4,6,10,2,8,5,14,12,11,15,1],
        [13,6,4,9,8,15,3,0,11,1,2,12,5,10,14,7],
        [1,10,13,0,6,9,8,7,4,15,14,3,11,5,2,12]
    ],[
        [7,13,14,3,0,6,9,10,1,2,8,5,11,12,4,15],
        [13,8,11,5,6,15,0,3,4,7,2,12,1,10,14,9],
        [10,6,9,0,12,11,7,13,15,1,3,14,5,2,8,4],
        [3,15,0,6,10,1,13,8,9,4,5,11,12,7,2,14]
    ],[
        [2,12,4,1,7,10,11,6,8,5,3,15,13,0,14,9],
        [14,11,2,12,4,7,13,1,5,0,15,10,3,9,8,6],
        [4,2,1,11,10,13,7,8,15,9,12,5,6,3,0,14],
        [11,8,12,7,1,14,2,13,6,15,0,9,10,4,5,3]
    ],[
        [12,1,10,15,9,2,6,8,0,13,3,4,14,7,5,11],
        [10,15,4,2,7,12,9,5,6,1,13,14,0,11,3,8],
        [9,14,15,5,2,8,12,3,7,0,4,10,1,13,11,6],
        [4,3,2,12,9,5,15,10,11,14,1,7,6,0,8,13]
    ],[
        [4,11,2,14,15,0,8,13,3,12,9,7,5,10,6,1],
        [13,0,11,7,4,9,1,10,14,3,5,12,2,15,8,6],
        [1,4,11,13,12,3,7,14,10,15,6,8,0,5,9,2],
        [6,11,13,8,1,4,10,7,9,5,0,15,14,2,3,12]
    ],[
        [13,2,8,4,6,15,11,1,10,9,3,14,5,0,12,7],
        [1,15,13,8,10,3,7,4,12,5,6,11,0,14,9,2],
        [7,11,4,1,9,12,14,2,0,6,10,13,15,3,5,8],
        [2,1,14,7,4,10,8,13,15,12,9,0,3,5,6,11]
    ]
]

PC2 = [
    13,16,10,23,0,4,2,27,14,5,20,9,
    22,18,11,3,25,7,15,6,26,19,12,1,
    40,51,30,36,46,54,29,39,50,44,32,47,
    43,48,38,55,33,52,45,41,49,35,28,31
]

IP_TABLE = [
    57,49,41,33,25,17,9,1,59,51,43,35,27,19,11,3,
    61,53,45,37,29,21,13,5,63,55,47,39,31,23,15,7,
    56,48,40,32,24,16,8,0,58,50,42,34,26,18,10,2,
    60,52,44,36,28,20,12,4,62,54,46,38,30,22,14,6
]

FP_TABLE = [
    39,7,47,15,55,23,63,31,
    38,6,46,14,54,22,62,30,
    37,5,45,13,53,21,61,29,
    36,4,44,12,52,20,60,28,
    35,3,43,11,51,19,59,27,
    34,2,42,10,50,18,58,26,
    33,1,41,9,49,17,57,25,
    32,0,40,8,48,16,56,24
]

E_TABLE = [
    31,0,1,2,3,4, 3,4,5,6,7,8,
    7,8,9,10,11,12, 11,12,13,14,15,16,
    15,16,17,18,19,20, 19,20,21,22,23,24,
    23,24,25,26,27,28, 27,28,29,30,31,0
]

P_TABLE = [
    15,6,19,20,28,11,27,16,
    0,14,22,25,4,17,30,9,
    1,7,23,13,31,26,2,8,
    18,12,29,5,21,10,3,24
]

LOOP_TABLE = [1,1,2,2,2,2,2,2,1,2,2,2,2,2,2,1]


# ----- 预计算 SP-融合表 _SP_BOXES -----
_P_DST_POS = [[0]*4 for _ in range(8)]
for dest, src in enumerate(P_TABLE):
    m, k = divmod(src, 4)
    _P_DST_POS[m][k] = dest

_SP_BOXES = []
for m in range(8):
    sb = S_BOXES[m]
    dst = _P_DST_POS[m]
    tbl = [0]*64
    for v in range(64):
        row = (((v>>5)&1)<<1)|(v&1)
        col = (v>>1)&0xF
        nib = sb[row][col]
        val = 0
        if (nib>>3)&1: val |= 1 << (31-dst[0])
        if (nib>>2)&1: val |= 1 << (31-dst[1])
        if (nib>>1)&1: val |= 1 << (31-dst[2])
        if nib&1:      val |= 1 << (31-dst[3])
        tbl[v] = val
    _SP_BOXES.append(tuple(tbl))
SP8 = tuple(_SP_BOXES)


# ----- 按字节的 IP/FP 查表 ----- 
def _build_byte_perm_tables(table):
    T8 = [[0]*256 for _ in range(8)]
    for i in range(8):
        for b in range(256):
            y = 0
            for j, src in enumerate(table):
                if src//8 == i:
                    if (b>>(7-(src%8)))&1:
                        y |= 1 << (63-j)
            T8[i][b] = y
    return tuple(tuple(row) for row in T8)

IP8 = _build_byte_perm_tables(IP_TABLE)
FP8 = _build_byte_perm_tables(FP_TABLE)


# ----- 预计算 E 扩展查表 ----- 
def _build_E48_byte_tables(E):
    T4 = [[0]*256 for _ in range(4)]
    for i in range(4):
        for b in range(256):
            y = 0
            for j, src in enumerate(E):
                if src//8 == i:
                    if (b>>(7-(src%8)))&1:
                        y |= 1 << (47-j)
            T4[i][b] = y
    return tuple(tuple(row) for row in T4)

E48 = _build_E48_byte_tables(E_TABLE)


# ----- key scheduling -----
def _generate_round_keys_from_bits(keyBits):
    # PC-1
    key56 = [0]*56
    for i in range(7):
        for j in range(8):
            key56[i*8+j] = keyBits[8*(7-j)+i]
    C, D = key56[:28], key56[28:]
    c_off = d_off = 0
    round_keys = []
    # PC-2 分组
    pc2_grps = [PC2[i*6:(i+1)*6] for i in range(8)]
    for shift in LOOP_TABLE:
        c_off = (c_off + shift) & 0x1F
        d_off = (d_off + shift) & 0x1F
        k48 = 0
        for grp in pc2_grps:
            if grp[0] < 28:
                b0 = C[(grp[0]+c_off)%28]; b1 = C[(grp[1]+c_off)%28]
                b2 = C[(grp[2]+c_off)%28]; b3 = C[(grp[3]+c_off)%28]
                b4 = C[(grp[4]+c_off)%28]; b5 = C[(grp[5]+c_off)%28]
            else:
                b0 = D[(grp[0]-28+d_off)%28]; b1 = D[(grp[1]-28+d_off)%28]
                b2 = D[(grp[2]-28+d_off)%28]; b3 = D[(grp[3]-28+d_off)%28]
                b4 = D[(grp[4]-28+d_off)%28]; b5 = D[(grp[5]-28+d_off)%28]
            v = (((((b0<<1)|b1)<<1|b2)<<1|b3)<<1|b4)<<1|b5
            k48 = (k48<<6) | v
        round_keys.append(k48)
    return round_keys

def _generate_round_keys_from_u64(key64):
    bits = [(key64>>(63-i))&1 for i in range(64)]
    return _generate_round_keys_from_bits(bits)


# ----- 固定密钥预生成 3×16 轮子密钥 -----
KB1 = (ord('1') & 0xFFFF) << 48
KB2 = (ord('2') & 0xFFFF) << 48
KB3 = (ord('3') & 0xFFFF) << 48
K1_ROUNDS = _generate_round_keys_from_u64(KB1)
K2_ROUNDS = _generate_round_keys_from_u64(KB2)
K3_ROUNDS = _generate_round_keys_from_u64(KB3)


# ----- 核心加密单块 (64bit → 64bit) -----
def _enc_block(block64,
    T0=IP8[0], T1=IP8[1], T2=IP8[2], T3=IP8[3],
    T4=IP8[4], T5=IP8[5], T6=IP8[6], T7=IP8[7],
    F0=FP8[0], F1=FP8[1], F2=FP8[2], F3=FP8[3],
    F4=FP8[4], F5=FP8[5], F6=FP8[6], F7=FP8[7],
    e0=E48[0], e1=E48[1], e2=E48[2], e3=E48[3],
    t0=SP8[0], t1=SP8[1], t2=SP8[2], t3=SP8[3],
    t4=SP8[4], t5=SP8[5], t6=SP8[6], t7=SP8[7]
):
    # 初始置换 IP
    b0 = T0[(block64>>56)&0xFF]; b1 = T1[(block64>>48)&0xFF]
    b2 = T2[(block64>>40)&0xFF]; b3 = T3[(block64>>32)&0xFF]
    b4 = T4[(block64>>24)&0xFF]; b5 = T5[(block64>>16)&0xFF]
    b6 = T6[(block64>> 8)&0xFF]; b7 = T7[ block64     &0xFF]
    ip = b0|b1|b2|b3|b4|b5|b6|b7

    L = ip>>32
    R = ip & 0xFFFFFFFF

    # 第一组 16 轮
    for k48 in K1_ROUNDS:
        E = (e0[(R>>24)&0xFF] | e1[(R>>16)&0xFF] |
             e2[(R>> 8)&0xFF] | e3[ R      &0xFF])
        Et = E ^ k48
        f = (t0[(Et>>42)&0x3F] ^ t1[(Et>>36)&0x3F] ^
             t2[(Et>>30)&0x3F] ^ t3[(Et>>24)&0x3F] ^
             t4[(Et>>18)&0x3F] ^ t5[(Et>>12)&0x3F] ^
             t6[(Et>> 6)&0x3F] ^ t7[ Et     &0x3F])
        L, R = R, L ^ f
    L, R = R, L

    # 第二组 16 轮
    for k48 in K2_ROUNDS:
        E = (e0[(R>>24)&0xFF] | e1[(R>>16)&0xFF] |
             e2[(R>> 8)&0xFF] | e3[ R      &0xFF])
        Et = E ^ k48
        f = (t0[(Et>>42)&0x3F] ^ t1[(Et>>36)&0x3F] ^
             t2[(Et>>30)&0x3F] ^ t3[(Et>>24)&0x3F] ^
             t4[(Et>>18)&0x3F] ^ t5[(Et>>12)&0x3F] ^
             t6[(Et>> 6)&0x3F] ^ t7[ Et     &0x3F])
        L, R = R, L ^ f
    L, R = R, L

    # 第三组 16 轮
    for k48 in K3_ROUNDS:
        E = (e0[(R>>24)&0xFF] | e1[(R>>16)&0xFF] |
             e2[(R>> 8)&0xFF] | e3[ R      &0xFF])
        Et = E ^ k48
        f = (t0[(Et>>42)&0x3F] ^ t1[(Et>>36)&0x3F] ^
             t2[(Et>>30)&0x3F] ^ t3[(Et>>24)&0x3F] ^
             t4[(Et>>18)&0x3F] ^ t5[(Et>>12)&0x3F] ^
             t6[(Et>> 6)&0x3F] ^ t7[ Et     &0x3F])
        L, R = R, L ^ f
    L, R = R, L

    # 逆置换 FP
    pre = (L<<32) | R
    b0 = F0[(pre>>56)&0xFF]; b1 = F1[(pre>>48)&0xFF]
    b2 = F2[(pre>>40)&0xFF]; b3 = F3[(pre>>32)&0xFF]
    b4 = F4[(pre>>24)&0xFF]; b5 = F5[(pre>>16)&0xFF]
    b6 = F6[(pre>> 8)&0xFF]; b7 = F7[ pre     &0xFF]
    return b0|b1|b2|b3|b4|b5|b6|b7


# ----- 对外 API -----  
def strEnc(data, a,b,c):
    """
    DES 加密（fixed keys '1','2','3'）
    data: 任意长度 unicode 字符串
    返回: 大写 16 进制拼接的字符串
    """
    if not data:
        return ''
    # UTF-16BE 编码后，每 8 字节一块
    db = data.encode('utf-16-be')
    pad = (-len(db)) & 7
    if pad:
        db += b'\0'*pad

    out = []
    unpack = struct.unpack_from
    enc = _enc_block
    for off in range(0, len(db), 8):
        (blk,) = unpack('>Q', db, off)
        v = enc(blk)
        out.append(f'{v:016X}')
    return ''.join(out)


# ----- 自测 -----  
if __name__ == '__main__':
    # 输出与原 des8.py strEnc("HelloWorld","1","2","3") 一致
    print(strEnc("HelloWorld"))
    print(strEnc("ABC"))
