{"cells": [{"cell_type": "markdown", "id": "708a892f", "metadata": {}, "source": ["7/25 16:14"]}, {"cell_type": "code", "execution_count": 2, "id": "00556c42-406c-4f2a-b005-8a2f7cce179e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: http://mirrors.aliyun.com/pypi/simple\n", "Requirement already satisfied: transformers==4.49.0 in /root/miniconda3/lib/python3.12/site-packages (4.49.0)\n", "Requirement already satisfied: filelock in /root/miniconda3/lib/python3.12/site-packages (from transformers==4.49.0) (3.14.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.26.0 in /root/miniconda3/lib/python3.12/site-packages (from transformers==4.49.0) (0.33.2)\n", "Requirement already satisfied: numpy>=1.17 in /root/miniconda3/lib/python3.12/site-packages (from transformers==4.49.0) (1.26.4)\n", "Requirement already satisfied: packaging>=20.0 in /root/miniconda3/lib/python3.12/site-packages (from transformers==4.49.0) (23.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /root/miniconda3/lib/python3.12/site-packages (from transformers==4.49.0) (6.0.1)\n", "Requirement already satisfied: regex!=2019.12.17 in /root/miniconda3/lib/python3.12/site-packages (from transformers==4.49.0) (2024.11.6)\n", "Requirement already satisfied: requests in /root/miniconda3/lib/python3.12/site-packages (from transformers==4.49.0) (2.31.0)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /root/miniconda3/lib/python3.12/site-packages (from transformers==4.49.0) (0.21.2)\n", "Requirement already satisfied: safetensors>=0.4.1 in /root/miniconda3/lib/python3.12/site-packages (from transformers==4.49.0) (0.5.3)\n", "Requirement already satisfied: tqdm>=4.27 in /root/miniconda3/lib/python3.12/site-packages (from transformers==4.49.0) (4.66.2)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /root/miniconda3/lib/python3.12/site-packages (from huggingface-hub<1.0,>=0.26.0->transformers==4.49.0) (2024.5.0)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /root/miniconda3/lib/python3.12/site-packages (from huggingface-hub<1.0,>=0.26.0->transformers==4.49.0) (4.12.1)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /root/miniconda3/lib/python3.12/site-packages (from huggingface-hub<1.0,>=0.26.0->transformers==4.49.0) (1.1.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /root/miniconda3/lib/python3.12/site-packages (from requests->transformers==4.49.0) (2.0.4)\n", "Requirement already satisfied: idna<4,>=2.5 in /root/miniconda3/lib/python3.12/site-packages (from requests->transformers==4.49.0) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /root/miniconda3/lib/python3.12/site-packages (from requests->transformers==4.49.0) (2.1.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /root/miniconda3/lib/python3.12/site-packages (from requests->transformers==4.49.0) (2024.2.2)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install transformers==4.49.0"]}, {"cell_type": "code", "execution_count": 2, "id": "42c752e6-f23d-4ec8-b9ac-63c282ac2635", "metadata": {}, "outputs": [], "source": ["# %pip install https://github.com/Dao-AILab/flash-attention/releases/download/v2.6.3/flash_attn-2.6.3+cu123torch2.3cxx11abiFALSE-cp312-cp312-linux_x86_64.whl"]}, {"cell_type": "code", "execution_count": 1, "id": "cfb34438-6dae-4e27-92c0-4260ea1d0858", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'"]}, {"cell_type": "code", "execution_count": 2, "id": "332c1aa4-1db5-49a9-a60d-3f12bdcae84d", "metadata": {}, "outputs": [], "source": ["import requests\n", "from PIL import Image\n", "from io import BytesIO\n", "import torch\n", "from transformers import AutoModel"]}, {"cell_type": "code", "execution_count": 3, "id": "1942d310-3e23-4ac5-b6ec-1f43cd99fb5e", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "59f3657d14294e79815a1824daa5f033", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a67c91fc69914aef8c458766fc358bf4", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bab0d65c48794a3b83b9dbe14ec4c38f", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/454 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load Model\n", "model = AutoModel.from_pretrained(\n", "    'nvidia/llama-nemoretriever-colembed-1b-v1',\n", "    device_map='cuda',\n", "    trust_remote_code=True,\n", "    torch_dtype=torch.bfloat16,\n", "    attn_implementation=\"flash_attention_2\",\n", "    revision='1f0fdea7f5b19532a750be109b19072d719b8177'\n", ").eval()"]}, {"cell_type": "code", "execution_count": 9, "id": "d31bc0bf-d341-455e-8b1b-7f12f78a33b7", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1f4db8271dee41a0a1ee53b9674063a0", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6ccbb2ff1c834b259b0d8ddfe90f1f60", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2bbc207587d448079b392179dc27fabd", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/454 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f0d3c726c6674a7fba6d490c562ac709", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load Model\n", "model = AutoModel.from_pretrained(\n", "    'nvidia/llama-nemoretriever-colembed-3b-v1',\n", "    use_safetensors=True,\n", "    device_map='cuda',\n", "    trust_remote_code=True,\n", "    torch_dtype=torch.bfloat16,\n", "    attn_implementation=\"flash_attention_2\",\n", "    revision='50c36f4d5271c6851aa08bd26d69f6e7ca8b870c'\n", ").eval()"]}, {"cell_type": "code", "execution_count": 10, "id": "328cbf04-fb31-4f61-8bc1-847ac132b766", "metadata": {}, "outputs": [], "source": ["# Queries\n", "queries = [\n", "    'How much percentage of Germanys population died in the 2nd World War?',\n", "    'How many million tons CO2 were captured from Gas processing in 2018?',\n", "    'What is the average CO2 emission of someone in Japan?'\n", "]\n", "\n", "# Documents\n", "image_urls = [\n", "    'https://wiki-upload.yayeah.xyz/wikipedia/commons/3/35/Human_losses_of_world_war_two_by_country.png',\n", "    'https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/7/76/20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg/2560px-20210413_Carbon_capture_and_storage_-_CCS_-_proposed_vs_implemented.svg.png',\n", "    'https://wiki-upload.yayeah.xyz/wikipedia/commons/thumb/f/f3/20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg/2880px-20210626_Variwide_chart_of_greenhouse_gas_emissions_per_capita_by_country.svg.png'\n", "]\n", "\n", "# Load into PIL\n", "headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64)\"\n", "}\n", "\n", "images = [Image.open(BytesIO(requests.get(image_url, headers=headers).content)) for image_url in image_urls]"]}, {"cell_type": "code", "execution_count": 19, "id": "ab6cb591-ab57-4281-a958-83232ad2d459", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Extracting query embeddings...: 100%|██████████| 3/3 [00:00<00:00,  6.65it/s]\n", "Extracting document embeddings...: 100%|██████████| 3/3 [00:01<00:00,  1.74it/s]\n"]}], "source": ["# Encoding\n", "query_embeddings = model.forward_queries(queries, batch_size=1)\n", "passage_embeddings = model.forward_passages(images, batch_size=1)"]}, {"cell_type": "code", "execution_count": 20, "id": "c4197000-6bfb-4b2d-b7bc-8bee68a2b916", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[13.4619, 10.1193, 11.3244],\n", "        [11.0310, 14.4793, 11.2596],\n", "        [ 9.2892,  9.0446, 10.9219]], device='cuda:0')\n"]}], "source": ["scores = model.get_scores(\n", "    query_embeddings,\n", "    passage_embeddings\n", ")\n", "# Diagonal should have high scores\n", "print(scores)\n"]}, {"cell_type": "markdown", "id": "31f6f33f-0c4d-4f26-b802-9a9afd4b312c", "metadata": {}, "source": ["1b\n", "```\n", "tensor([[13.9879, 11.4159, 12.1137],\n", "        [11.4137, 14.6339, 12.0341],\n", "        [ 9.9002,  9.8804, 11.3303]], device='cuda:0')\n", "```\n", "\n", "3b\n", "```\n", "tensor([[13.4660, 10.1190, 11.3106],\n", "        [11.0491, 14.4790, 11.2651],\n", "        [ 9.2999,  9.0384, 10.9142]], device='cuda:0')\n", "```"]}, {"cell_type": "code", "execution_count": 24, "id": "4880529e-ce04-42a3-9532-c27b009c21bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([19, 3072])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["query_embeddings[2].shape"]}, {"cell_type": "code", "execution_count": 21, "id": "e3259204-b148-43c3-b5c5-6c9a92e3a76d", "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([1802, 3072])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["passage_embeddings[2].shape"]}, {"cell_type": "markdown", "id": "d57e9ff9-83ef-4699-950c-55be7e3f4d32", "metadata": {}, "source": ["为什么query长度不同，却获得了长度一致的multi-vec(late interaction?)embedding?"]}, {"cell_type": "code", "execution_count": null, "id": "08fc021f-3f4a-4888-8085-7726ac7c02b9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}