# PDF拆分与Gemini API处理工具

异步PDF拆分与Markdown转换工具，支持根据PDF目录自动拆分文件，并使用Google Gemini API将PDF内容转换为Markdown格式。

## 功能特性

- 🔄 **PDF拆分**：根据PDF目录结构自动拆分为多个子文件
- 🤖 **AI转换**：使用Google Gemini API将PDF转换为Markdown
- ⚡ **异步处理**：支持并发处理多个文件，提升处理速度
- 🎯 **智能推理**：支持thinking_budget参数控制AI推理深度

## 安装与使用

```bash
# 安装依赖
pip install -r requirements.txt

# 运行程序
python async_pdf_splitter_gemini.py <PDF文件路径>
```

## 配置参数

- `MAX_CONCURRENT_REQUESTS`: 最大并发请求数（默认10）
- `THINKING_BUDGET`: AI推理预算（默认32768，-1为动态，0为关闭）
- `FILE_API_THRESHOLD_MB`: 大文件阈值（默认20MB）

## 输出目录

- 拆分的PDF文件：`split_pdfs/`
- 转换的Markdown文件：`gemini_output/`

## 使用要求

- 需要有效的Google Gemini API密钥
- PDF文件需包含目录信息
- 大文件（>20MB）将通过File API上传处理
