# des6.py
# 主要优化：
# - E 扩展改为 4×256 字节查表，一次完成 32->48 的扩展
# - 轮密钥改为 48bit 整数（k48），E(R) 直接异或 k48，再分组进 SP 表
# - 保留 IP/FP 的 8×256 查表与 SP（S-Box+P）融合表
# - 内层循环更少的位操作与索引（去除每组 6 位的逐位抽取）

S_BOXES = [
    [
        [14,4,13,1,2,15,11,8,3,10,6,12,5,9,0,7],
        [0,15,7,4,14,2,13,1,10,6,12,11,9,5,3,8],
        [4,1,14,8,13,6,2,11,15,12,9,7,3,10,5,0],
        [15,12,8,2,4,9,1,7,5,11,3,14,10,0,6,13]
    ],
    [
        [15,1,8,14,6,11,3,4,9,7,2,13,12,0,5,10],
        [3,13,4,7,15,2,8,14,12,0,1,10,6,9,11,5],
        [0,14,7,11,10,4,13,1,5,8,12,6,9,3,2,15],
        [13,8,10,1,3,15,4,2,11,6,7,12,0,5,14,9]
    ],
    [
        [10,0,9,14,6,3,15,5,1,13,12,7,11,4,2,8],
        [13,7,0,9,3,4,6,10,2,8,5,14,12,11,15,1],
        [13,6,4,9,8,15,3,0,11,1,2,12,5,10,14,7],
        [1,10,13,0,6,9,8,7,4,15,14,3,11,5,2,12]
    ],
    [
        [7,13,14,3,0,6,9,10,1,2,8,5,11,12,4,15],
        [13,8,11,5,6,15,0,3,4,7,2,12,1,10,14,9],
        [10,6,9,0,12,11,7,13,15,1,3,14,5,2,8,4],
        [3,15,0,6,10,1,13,8,9,4,5,11,12,7,2,14]
    ],
    [
        [2,12,4,1,7,10,11,6,8,5,3,15,13,0,14,9],
        [14,11,2,12,4,7,13,1,5,0,15,10,3,9,8,6],
        [4,2,1,11,10,13,7,8,15,9,12,5,6,3,0,14],
        [11,8,12,7,1,14,2,13,6,15,0,9,10,4,5,3]
    ],
    [
        [12,1,10,15,9,2,6,8,0,13,3,4,14,7,5,11],
        [10,15,4,2,7,12,9,5,6,1,13,14,0,11,3,8],
        [9,14,15,5,2,8,12,3,7,0,4,10,1,13,11,6],
        [4,3,2,12,9,5,15,10,11,14,1,7,6,0,8,13]
    ],
    [
        [4,11,2,14,15,0,8,13,3,12,9,7,5,10,6,1],
        [13,0,11,7,4,9,1,10,14,3,5,12,2,15,8,6],
        [1,4,11,13,12,3,7,14,10,15,6,8,0,5,9,2],
        [6,11,13,8,1,4,10,7,9,5,0,15,14,2,3,12]
    ],
    [
        [13,2,8,4,6,15,11,1,10,9,3,14,5,0,12,7],
        [1,15,13,8,10,3,7,4,12,5,6,11,0,14,9,2],
        [7,11,4,1,9,12,14,2,0,6,10,13,15,3,5,8],
        [2,1,14,7,4,10,8,13,15,12,9,0,3,5,6,11]
    ]
]

PC2 = [13,16,10,23,0,4,2,27,14,5,20,9,
       22,18,11,3,25,7,15,6,26,19,12,1,
       40,51,30,36,46,54,29,39,50,44,32,47,
       43,48,38,55,33,52,45,41,49,35,28,31]

IP_TABLE = [
    57,49,41,33,25,17,9,1,59,51,43,35,27,19,11,3,
    61,53,45,37,29,21,13,5,63,55,47,39,31,23,15,7,
    56,48,40,32,24,16,8,0,58,50,42,34,26,18,10,2,
    60,52,44,36,28,20,12,4,62,54,46,38,30,22,14,6
]

FP_TABLE = [
    39,7,47,15,55,23,63,31,
    38,6,46,14,54,22,62,30,
    37,5,45,13,53,21,61,29,
    36,4,44,12,52,20,60,28,
    35,3,43,11,51,19,59,27,
    34,2,42,10,50,18,58,26,
    33,1,41,9,49,17,57,25,
    32,0,40,8,48,16,56,24
]

E_TABLE = [
    31, 0, 1, 2, 3, 4,
    3, 4, 5, 6, 7, 8,
    7, 8, 9, 10,11,12,
    11,12,13,14,15,16,
    15,16,17,18,19,20,
    19,20,21,22,23,24,
    23,24,25,26,27,28,
    27,28,29,30,31,0
]

P_TABLE = [
    15,6,19,20,28,11,27,16,
    0,14,22,25,4,17,30,9,
    1,7,23,13,31,26,2,8,
    18,12,29,5,21,10,3,24
]

LOOP_TABLE = [1,1,2,2,2,2,2,2,1,2,2,2,2,2,2,1]

_HEX = '0123456789ABCDEF'


# -------------------- 预计算：P 落点、PC2 分组、SP 表 --------------------
_P_DST_POS = [[0,0,0,0] for _ in range(8)]
for dest, src in enumerate(P_TABLE):
    m, k = divmod(src, 4)
    _P_DST_POS[m][k] = dest

_PC2_GROUPS = [PC2[i*6:(i+1)*6] for i in range(8)]

_SP_BOXES = []
for m in range(8):
    dst = _P_DST_POS[m]
    table = [0] * 64
    sb = S_BOXES[m]
    for v in range(64):
        row = (((v >> 5) & 1) << 1) | (v & 1)
        col = (v >> 1) & 0xF
        nib = sb[row][col]
        val = 0
        if (nib >> 3) & 1:
            val |= 1 << (31 - dst[0])
        if (nib >> 2) & 1:
            val |= 1 << (31 - dst[1])
        if (nib >> 1) & 1:
            val |= 1 << (31 - dst[2])
        if nib & 1:
            val |= 1 << (31 - dst[3])
        table[v] = val
    _SP_BOXES.append(table)


# -------------------- 预计算：按字节的 64 位置换表（IP/FP） --------------------
def _build_byte_perm_tables(table: list[int]) -> list[list[int]]:
    T8 = [[0]*256 for _ in range(8)]
    for i in range(8):  # 源字节索引（0=MSB）
        for b in range(256):
            y = 0
            for j, src in enumerate(table):
                if src // 8 == i:
                    k = src % 8  # 源字节中的位（0=MSB）
                    if (b >> (7 - k)) & 1:
                        y |= 1 << (63 - j)  # 输出第 j 位 -> y 的 bit(63-j)
            T8[i][b] = y
    return T8

_IP8 = _build_byte_perm_tables(IP_TABLE)
_FP8 = _build_byte_perm_tables(FP_TABLE)

def _permute64_by_bytes(x: int, T8: list[list[int]]) -> int:
    return (T8[0][(x >> 56) & 0xFF] |
            T8[1][(x >> 48) & 0xFF] |
            T8[2][(x >> 40) & 0xFF] |
            T8[3][(x >> 32) & 0xFF] |
            T8[4][(x >> 24) & 0xFF] |
            T8[5][(x >> 16) & 0xFF] |
            T8[6][(x >> 8) & 0xFF]  |
            T8[7][x & 0xFF])

def _ip(x: int) -> int:
    return _permute64_by_bytes(x, _IP8)

def _fp(x: int) -> int:
    return _permute64_by_bytes(x, _FP8)


# -------------------- 预计算：E 扩展 32->48，按字节查表 --------------------
def _build_E48_byte_tables(E: list[int]) -> list[list[int]]:
    """
    将 E 扩展编译为 4×256 查表：
    R 的第 i 个字节（0=MSB byte，涵盖位 31..24）取值 b，映射到 48 位贡献值。
    输出 48 位整数的 bit(47-j) 对应 E_TABLE[j] 的源位。
    """
    T4 = [[0]*256 for _ in range(4)]
    for i in range(4):  # 源字节索引（0=MSB，R bits 31..24）
        for b in range(256):
            y = 0
            for j, src in enumerate(E):
                if (src // 8) == i:
                    k = src % 8  # 源字节中的位（0=MSB）
                    if (b >> (7 - k)) & 1:
                        y |= 1 << (47 - j)
            T4[i][b] = y
    return T4

_E48 = _build_E48_byte_tables(E_TABLE)


# -------------------- 基础工具 --------------------
def _pack_block4(s: str) -> int:
    """最多4字符，按 16bit/字符打包成 64 位整数（不足补 '\\0'）。"""
    s = s + '\0' * (4 - len(s))
    v = 0
    v = (v << 16) | (ord(s[0]) & 0xFFFF)
    v = (v << 16) | (ord(s[1]) & 0xFFFF)
    v = (v << 16) | (ord(s[2]) & 0xFFFF)
    v = (v << 16) | (ord(s[3]) & 0xFFFF)
    return v

def _u64_to_hex16(x: int) -> str:
    return f"{x & 0xFFFFFFFFFFFFFFFF:016X}"

def _str_to_keyblocks(key: str) -> list[int]:
    if not key:
        return []
    n = (len(key) + 3) // 4
    return [_pack_block4(key[i*4:(i+1)*4]) for i in range(n)]


# -------------------- 轮密钥生成（改为 48bit 整数 k48） --------------------
def _generate_round_keys_from_bits(keyBits: list[int]) -> list[int]:
    """
    输入：64位bit列表（index0 为 MSB）
    输出：长度为16的列表，每个元素是 48bit 的整型子密钥（按 E 组顺序拼接）。
    """
    # PC1 变换（按原逻辑丢弃每字节最低位）
    key56 = [0] * 56
    for i in range(7):
        base_dest = i * 8
        for j in range(8):
            key56[base_dest + j] = keyBits[8 * (7 - j) + i]

    C = key56[:28]
    D = key56[28:56]
    c_off = 0
    d_off = 0

    loop = LOOP_TABLE
    pc2_groups = _PC2_GROUPS

    keys48: list[int] = []
    for rnd in range(16):
        shift = loop[rnd]
        c_off = (c_off + shift) % 28
        d_off = (d_off + shift) % 28

        k48 = 0
        for m in range(8):
            v = 0
            grp = pc2_groups[m]
            # 6bit 组
            idx0, idx1, idx2, idx3, idx4, idx5 = grp
            b0 = C[(idx0 + c_off) % 28] if idx0 < 28 else D[(idx0 - 28 + d_off) % 28]
            b1 = C[(idx1 + c_off) % 28] if idx1 < 28 else D[(idx1 - 28 + d_off) % 28]
            b2 = C[(idx2 + c_off) % 28] if idx2 < 28 else D[(idx2 - 28 + d_off) % 28]
            b3 = C[(idx3 + c_off) % 28] if idx3 < 28 else D[(idx3 - 28 + d_off) % 28]
            b4 = C[(idx4 + c_off) % 28] if idx4 < 28 else D[(idx4 - 28 + d_off) % 28]
            b5 = C[(idx5 + c_off) % 28] if idx5 < 28 else D[(idx5 - 28 + d_off) % 28]
            v = (((((b0 << 1) | b1) << 1 | b2) << 1 | b3) << 1 | b4) << 1 | b5
            k48 = (k48 << 6) | v
        keys48.append(k48)
    return keys48

def _generate_round_keys_from_u64(key64: int) -> list[int]:
    keyBits = [ (key64 >> (63 - i)) & 1 for i in range(64) ]
    return _generate_round_keys_from_bits(keyBits)


# -------------------- 加密核心：支持多密钥块串联，只做一次 IP/FP --------------------
def _enc_block_chained_u64(block64: int,
                           blocks_round_keys: list[list[int]]
                          ) -> int:
    """
    对单个 64 位块执行“密钥块链”加密：
    blocks_round_keys: 列表，每个元素是该“4字符密钥块”的16轮 48bit 子密钥列表。
    仅在整条链首、尾分别执行 IP/FP；相邻密钥块之间做一次 (L,R) 交换。
    """
    if not blocks_round_keys:
        return block64

    ip = _ip(block64)
    L = (ip >> 32) & 0xFFFFFFFF
    R = ip & 0xFFFFFFFF

    s0, s1, s2, s3, s4, s5, s6, s7 = _SP_BOXES
    e0, e1, e2, e3 = _E48

    last_block_idx = len(blocks_round_keys) - 1
    for bi, rk16 in enumerate(blocks_round_keys):
        for k48 in rk16:
            # E 扩展（4×256）并合并
            E = (e0[(R >> 24) & 0xFF] |
                 e1[(R >> 16) & 0xFF] |
                 e2[(R >> 8)  & 0xFF] |
                 e3[R & 0xFF])

            Et = E ^ k48  # 48bit

            # 8 个 6bit 组映射到 SP 表（已融合 P）
            f = (s0[(Et >> 42) & 0x3F] ^
                 s1[(Et >> 36) & 0x3F] ^
                 s2[(Et >> 30) & 0x3F] ^
                 s3[(Et >> 24) & 0x3F] ^
                 s4[(Et >> 18) & 0x3F] ^
                 s5[(Et >> 12) & 0x3F] ^
                 s6[(Et >> 6)  & 0x3F] ^
                 s7[Et & 0x3F])

            L, R = R, (L ^ f) & 0xFFFFFFFF

        if bi != last_block_idx:
            L, R = R, L

    preout = ((R & 0xFFFFFFFF) << 32) | (L & 0xFFFFFFFF)
    return _fp(preout)


# -------------------- 对外 API --------------------
def strEnc(data: str, firstKey: str, secondKey: str, thirdKey: str) -> str:
    """
    DES 加密（按自定义“多密钥分层”方式；未实现解密）
    data: 任意长度字符串；每 4 个字符为一块，不足补 '\\0'
    每个 key 按 4 字符分块，依次对数据块进行再次加密
    返回：每块 16 个十六进制大写字符拼接的字符串
    """
    leng = len(data)
    if leng == 0:
        return ''

    # 将三把密钥各自按 4 字符分块，预生成每块的 16 轮 48bit 子密钥
    blocks_round_keys: list[list[int]] = []
    for k in (firstKey, secondKey, thirdKey):
        if not k:
            continue
        for kb in _str_to_keyblocks(k):
            blocks_round_keys.append(_generate_round_keys_from_u64(kb))

    out_hex = []
    block_size = 4
    full_blocks = leng // block_size
    remainder = leng % block_size

    if not blocks_round_keys:
        # 无密钥：直接输出原块的十六进制（与 des4 行为一致）
        for i in range(full_blocks):
            blk = data[i*block_size:(i+1)*block_size]
            b64 = _pack_block4(blk)
            out_hex.append(_u64_to_hex16(b64))
        if remainder:
            blk = data[full_blocks*block_size:]
            b64 = _pack_block4(blk)
            out_hex.append(_u64_to_hex16(b64))
        return ''.join(out_hex)

    for i in range(full_blocks):
        blk = data[i*block_size:(i+1)*block_size]
        b64 = _pack_block4(blk)
        c64 = _enc_block_chained_u64(b64, blocks_round_keys)
        out_hex.append(_u64_to_hex16(c64))

    if remainder:
        blk = data[full_blocks*block_size:]
        b64 = _pack_block4(blk)
        c64 = _enc_block_chained_u64(b64, blocks_round_keys)
        out_hex.append(_u64_to_hex16(c64))

    return ''.join(out_hex)


# 自测示例：
# if __name__ == '__main__':
#     print(strEnc("HelloWorld", "KeyOne", "KeyTwo", "KeyThree"))
#     print(strEnc("ABC", "K", None, None))
